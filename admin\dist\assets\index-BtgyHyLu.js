(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const f of d)if(f.type==="childList")for(const m of f.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&c(m)}).observe(document,{childList:!0,subtree:!0});function u(d){const f={};return d.integrity&&(f.integrity=d.integrity),d.referrerPolicy&&(f.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?f.credentials="include":d.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function c(d){if(d.ep)return;d.ep=!0;const f=u(d);fetch(d.href,f)}})();function Og(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var ic={exports:{}},Kn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zh;function Dg(){if(zh)return Kn;zh=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(c,d,f){var m=null;if(f!==void 0&&(m=""+f),d.key!==void 0&&(m=""+d.key),"key"in d){f={};for(var g in d)g!=="key"&&(f[g]=d[g])}else f=d;return d=f.ref,{$$typeof:n,type:c,key:m,ref:d!==void 0?d:null,props:f}}return Kn.Fragment=r,Kn.jsx=u,Kn.jsxs=u,Kn}var Uh;function Mg(){return Uh||(Uh=1,ic.exports=Dg()),ic.exports}var s=Mg(),rc={exports:{}},ge={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bh;function zg(){if(Bh)return ge;Bh=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),m=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),b=Symbol.iterator;function _(S){return S===null||typeof S!="object"?null:(S=b&&S[b]||S["@@iterator"],typeof S=="function"?S:null)}var U={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,D={};function M(S,V,le){this.props=S,this.context=V,this.refs=D,this.updater=le||U}M.prototype.isReactComponent={},M.prototype.setState=function(S,V){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,V,"setState")},M.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function O(){}O.prototype=M.prototype;function R(S,V,le){this.props=S,this.context=V,this.refs=D,this.updater=le||U}var T=R.prototype=new O;T.constructor=R,C(T,M.prototype),T.isPureReactComponent=!0;var N=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function P(S,V,le,ae,I,xe){return le=xe.ref,{$$typeof:n,type:S,key:V,ref:le!==void 0?le:null,props:xe}}function F(S,V){return P(S.type,V,void 0,void 0,void 0,S.props)}function ee(S){return typeof S=="object"&&S!==null&&S.$$typeof===n}function Q(S){var V={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(le){return V[le]})}var $=/\/+/g;function ne(S,V){return typeof S=="object"&&S!==null&&S.key!=null?Q(""+S.key):V.toString(36)}function ce(){}function re(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(ce,ce):(S.status="pending",S.then(function(V){S.status==="pending"&&(S.status="fulfilled",S.value=V)},function(V){S.status==="pending"&&(S.status="rejected",S.reason=V)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function ye(S,V,le,ae,I){var xe=typeof S;(xe==="undefined"||xe==="boolean")&&(S=null);var he=!1;if(S===null)he=!0;else switch(xe){case"bigint":case"string":case"number":he=!0;break;case"object":switch(S.$$typeof){case n:case r:he=!0;break;case x:return he=S._init,ye(he(S._payload),V,le,ae,I)}}if(he)return I=I(S),he=ae===""?"."+ne(S,0):ae,N(I)?(le="",he!=null&&(le=he.replace($,"$&/")+"/"),ye(I,V,le,"",function(da){return da})):I!=null&&(ee(I)&&(I=F(I,le+(I.key==null||S&&S.key===I.key?"":(""+I.key).replace($,"$&/")+"/")+he)),V.push(I)),1;he=0;var ft=ae===""?".":ae+":";if(N(S))for(var ze=0;ze<S.length;ze++)ae=S[ze],xe=ft+ne(ae,ze),he+=ye(ae,V,le,xe,I);else if(ze=_(S),typeof ze=="function")for(S=ze.call(S),ze=0;!(ae=S.next()).done;)ae=ae.value,xe=ft+ne(ae,ze++),he+=ye(ae,V,le,xe,I);else if(xe==="object"){if(typeof S.then=="function")return ye(re(S),V,le,ae,I);throw V=String(S),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.")}return he}function X(S,V,le){if(S==null)return S;var ae=[],I=0;return ye(S,ae,"","",function(xe){return V.call(le,xe,I++)}),ae}function te(S){if(S._status===-1){var V=S._result;V=V(),V.then(function(le){(S._status===0||S._status===-1)&&(S._status=1,S._result=le)},function(le){(S._status===0||S._status===-1)&&(S._status=2,S._result=le)}),S._status===-1&&(S._status=0,S._result=V)}if(S._status===1)return S._result.default;throw S._result}var q=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var V=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(V))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function se(){}return ge.Children={map:X,forEach:function(S,V,le){X(S,function(){V.apply(this,arguments)},le)},count:function(S){var V=0;return X(S,function(){V++}),V},toArray:function(S){return X(S,function(V){return V})||[]},only:function(S){if(!ee(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ge.Component=M,ge.Fragment=u,ge.Profiler=d,ge.PureComponent=R,ge.StrictMode=c,ge.Suspense=v,ge.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,ge.__COMPILER_RUNTIME={__proto__:null,c:function(S){return w.H.useMemoCache(S)}},ge.cache=function(S){return function(){return S.apply(null,arguments)}},ge.cloneElement=function(S,V,le){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var ae=C({},S.props),I=S.key,xe=void 0;if(V!=null)for(he in V.ref!==void 0&&(xe=void 0),V.key!==void 0&&(I=""+V.key),V)!W.call(V,he)||he==="key"||he==="__self"||he==="__source"||he==="ref"&&V.ref===void 0||(ae[he]=V[he]);var he=arguments.length-2;if(he===1)ae.children=le;else if(1<he){for(var ft=Array(he),ze=0;ze<he;ze++)ft[ze]=arguments[ze+2];ae.children=ft}return P(S.type,I,void 0,void 0,xe,ae)},ge.createContext=function(S){return S={$$typeof:m,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:f,_context:S},S},ge.createElement=function(S,V,le){var ae,I={},xe=null;if(V!=null)for(ae in V.key!==void 0&&(xe=""+V.key),V)W.call(V,ae)&&ae!=="key"&&ae!=="__self"&&ae!=="__source"&&(I[ae]=V[ae]);var he=arguments.length-2;if(he===1)I.children=le;else if(1<he){for(var ft=Array(he),ze=0;ze<he;ze++)ft[ze]=arguments[ze+2];I.children=ft}if(S&&S.defaultProps)for(ae in he=S.defaultProps,he)I[ae]===void 0&&(I[ae]=he[ae]);return P(S,xe,void 0,void 0,null,I)},ge.createRef=function(){return{current:null}},ge.forwardRef=function(S){return{$$typeof:g,render:S}},ge.isValidElement=ee,ge.lazy=function(S){return{$$typeof:x,_payload:{_status:-1,_result:S},_init:te}},ge.memo=function(S,V){return{$$typeof:p,type:S,compare:V===void 0?null:V}},ge.startTransition=function(S){var V=w.T,le={};w.T=le;try{var ae=S(),I=w.S;I!==null&&I(le,ae),typeof ae=="object"&&ae!==null&&typeof ae.then=="function"&&ae.then(se,q)}catch(xe){q(xe)}finally{w.T=V}},ge.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},ge.use=function(S){return w.H.use(S)},ge.useActionState=function(S,V,le){return w.H.useActionState(S,V,le)},ge.useCallback=function(S,V){return w.H.useCallback(S,V)},ge.useContext=function(S){return w.H.useContext(S)},ge.useDebugValue=function(){},ge.useDeferredValue=function(S,V){return w.H.useDeferredValue(S,V)},ge.useEffect=function(S,V,le){var ae=w.H;if(typeof le=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ae.useEffect(S,V)},ge.useId=function(){return w.H.useId()},ge.useImperativeHandle=function(S,V,le){return w.H.useImperativeHandle(S,V,le)},ge.useInsertionEffect=function(S,V){return w.H.useInsertionEffect(S,V)},ge.useLayoutEffect=function(S,V){return w.H.useLayoutEffect(S,V)},ge.useMemo=function(S,V){return w.H.useMemo(S,V)},ge.useOptimistic=function(S,V){return w.H.useOptimistic(S,V)},ge.useReducer=function(S,V,le){return w.H.useReducer(S,V,le)},ge.useRef=function(S){return w.H.useRef(S)},ge.useState=function(S){return w.H.useState(S)},ge.useSyncExternalStore=function(S,V,le){return w.H.useSyncExternalStore(S,V,le)},ge.useTransition=function(){return w.H.useTransition()},ge.version="19.1.0",ge}var Lh;function Rc(){return Lh||(Lh=1,rc.exports=zg()),rc.exports}var E=Rc();const Me=Og(E);var oc={exports:{}},Jn={},cc={exports:{}},uc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hh;function Ug(){return Hh||(Hh=1,function(n){function r(X,te){var q=X.length;X.push(te);e:for(;0<q;){var se=q-1>>>1,S=X[se];if(0<d(S,te))X[se]=te,X[q]=S,q=se;else break e}}function u(X){return X.length===0?null:X[0]}function c(X){if(X.length===0)return null;var te=X[0],q=X.pop();if(q!==te){X[0]=q;e:for(var se=0,S=X.length,V=S>>>1;se<V;){var le=2*(se+1)-1,ae=X[le],I=le+1,xe=X[I];if(0>d(ae,q))I<S&&0>d(xe,ae)?(X[se]=xe,X[I]=q,se=I):(X[se]=ae,X[le]=q,se=le);else if(I<S&&0>d(xe,q))X[se]=xe,X[I]=q,se=I;else break e}}return te}function d(X,te){var q=X.sortIndex-te.sortIndex;return q!==0?q:X.id-te.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var m=Date,g=m.now();n.unstable_now=function(){return m.now()-g}}var v=[],p=[],x=1,b=null,_=3,U=!1,C=!1,D=!1,M=!1,O=typeof setTimeout=="function"?setTimeout:null,R=typeof clearTimeout=="function"?clearTimeout:null,T=typeof setImmediate<"u"?setImmediate:null;function N(X){for(var te=u(p);te!==null;){if(te.callback===null)c(p);else if(te.startTime<=X)c(p),te.sortIndex=te.expirationTime,r(v,te);else break;te=u(p)}}function w(X){if(D=!1,N(X),!C)if(u(v)!==null)C=!0,W||(W=!0,ne());else{var te=u(p);te!==null&&ye(w,te.startTime-X)}}var W=!1,P=-1,F=5,ee=-1;function Q(){return M?!0:!(n.unstable_now()-ee<F)}function $(){if(M=!1,W){var X=n.unstable_now();ee=X;var te=!0;try{e:{C=!1,D&&(D=!1,R(P),P=-1),U=!0;var q=_;try{t:{for(N(X),b=u(v);b!==null&&!(b.expirationTime>X&&Q());){var se=b.callback;if(typeof se=="function"){b.callback=null,_=b.priorityLevel;var S=se(b.expirationTime<=X);if(X=n.unstable_now(),typeof S=="function"){b.callback=S,N(X),te=!0;break t}b===u(v)&&c(v),N(X)}else c(v);b=u(v)}if(b!==null)te=!0;else{var V=u(p);V!==null&&ye(w,V.startTime-X),te=!1}}break e}finally{b=null,_=q,U=!1}te=void 0}}finally{te?ne():W=!1}}}var ne;if(typeof T=="function")ne=function(){T($)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,re=ce.port2;ce.port1.onmessage=$,ne=function(){re.postMessage(null)}}else ne=function(){O($,0)};function ye(X,te){P=O(function(){X(n.unstable_now())},te)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(X){X.callback=null},n.unstable_forceFrameRate=function(X){0>X||125<X?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<X?Math.floor(1e3/X):5},n.unstable_getCurrentPriorityLevel=function(){return _},n.unstable_next=function(X){switch(_){case 1:case 2:case 3:var te=3;break;default:te=_}var q=_;_=te;try{return X()}finally{_=q}},n.unstable_requestPaint=function(){M=!0},n.unstable_runWithPriority=function(X,te){switch(X){case 1:case 2:case 3:case 4:case 5:break;default:X=3}var q=_;_=X;try{return te()}finally{_=q}},n.unstable_scheduleCallback=function(X,te,q){var se=n.unstable_now();switch(typeof q=="object"&&q!==null?(q=q.delay,q=typeof q=="number"&&0<q?se+q:se):q=se,X){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=q+S,X={id:x++,callback:te,priorityLevel:X,startTime:q,expirationTime:S,sortIndex:-1},q>se?(X.sortIndex=q,r(p,X),u(v)===null&&X===u(p)&&(D?(R(P),P=-1):D=!0,ye(w,q-se))):(X.sortIndex=S,r(v,X),C||U||(C=!0,W||(W=!0,ne()))),X},n.unstable_shouldYield=Q,n.unstable_wrapCallback=function(X){var te=_;return function(){var q=_;_=te;try{return X.apply(this,arguments)}finally{_=q}}}}(uc)),uc}var kh;function Bg(){return kh||(kh=1,cc.exports=Ug()),cc.exports}var dc={exports:{}},tt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qh;function Lg(){if(qh)return tt;qh=1;var n=Rc();function r(v){var p="https://react.dev/errors/"+v;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)p+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+v+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var c={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},d=Symbol.for("react.portal");function f(v,p,x){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:b==null?null:""+b,children:v,containerInfo:p,implementation:x}}var m=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(v,p){if(v==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return tt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,tt.createPortal=function(v,p){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(r(299));return f(v,p,null,x)},tt.flushSync=function(v){var p=m.T,x=c.p;try{if(m.T=null,c.p=2,v)return v()}finally{m.T=p,c.p=x,c.d.f()}},tt.preconnect=function(v,p){typeof v=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,c.d.C(v,p))},tt.prefetchDNS=function(v){typeof v=="string"&&c.d.D(v)},tt.preinit=function(v,p){if(typeof v=="string"&&p&&typeof p.as=="string"){var x=p.as,b=g(x,p.crossOrigin),_=typeof p.integrity=="string"?p.integrity:void 0,U=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;x==="style"?c.d.S(v,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:b,integrity:_,fetchPriority:U}):x==="script"&&c.d.X(v,{crossOrigin:b,integrity:_,fetchPriority:U,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},tt.preinitModule=function(v,p){if(typeof v=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var x=g(p.as,p.crossOrigin);c.d.M(v,{crossOrigin:x,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&c.d.M(v)},tt.preload=function(v,p){if(typeof v=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var x=p.as,b=g(x,p.crossOrigin);c.d.L(v,x,{crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},tt.preloadModule=function(v,p){if(typeof v=="string")if(p){var x=g(p.as,p.crossOrigin);c.d.m(v,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:x,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else c.d.m(v)},tt.requestFormReset=function(v){c.d.r(v)},tt.unstable_batchedUpdates=function(v,p){return v(p)},tt.useFormState=function(v,p,x){return m.H.useFormState(v,p,x)},tt.useFormStatus=function(){return m.H.useHostTransitionStatus()},tt.version="19.1.0",tt}var Yh;function Hg(){if(Yh)return dc.exports;Yh=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),dc.exports=Lg(),dc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function kg(){if(Gh)return Jn;Gh=1;var n=Bg(),r=Rc(),u=Hg();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(f(e)!==e)throw Error(c(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,l=t;;){var i=a.return;if(i===null)break;var o=i.alternate;if(o===null){if(l=i.return,l!==null){a=l;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===a)return g(i),e;if(o===l)return g(i),t;o=o.sibling}throw Error(c(188))}if(a.return!==l.return)a=i,l=o;else{for(var h=!1,y=i.child;y;){if(y===a){h=!0,a=i,l=o;break}if(y===l){h=!0,l=i,a=o;break}y=y.sibling}if(!h){for(y=o.child;y;){if(y===a){h=!0,a=o,l=i;break}if(y===l){h=!0,l=o,a=i;break}y=y.sibling}if(!h)throw Error(c(189))}}if(a.alternate!==l)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,b=Symbol.for("react.element"),_=Symbol.for("react.transitional.element"),U=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),R=Symbol.for("react.consumer"),T=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),ee=Symbol.for("react.activity"),Q=Symbol.for("react.memo_cache_sentinel"),$=Symbol.iterator;function ne(e){return e===null||typeof e!="object"?null:(e=$&&e[$]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Symbol.for("react.client.reference");function re(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ce?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case C:return"Fragment";case M:return"Profiler";case D:return"StrictMode";case w:return"Suspense";case W:return"SuspenseList";case ee:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case U:return"Portal";case T:return(e.displayName||"Context")+".Provider";case R:return(e._context.displayName||"Context")+".Consumer";case N:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:re(e.type)||"Memo";case F:t=e._payload,e=e._init;try{return re(e(t))}catch{}}return null}var ye=Array.isArray,X=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q={pending:!1,data:null,method:null,action:null},se=[],S=-1;function V(e){return{current:e}}function le(e){0>S||(e.current=se[S],se[S]=null,S--)}function ae(e,t){S++,se[S]=e.current,e.current=t}var I=V(null),xe=V(null),he=V(null),ft=V(null);function ze(e,t){switch(ae(he,t),ae(xe,e),ae(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?oh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=oh(t),e=ch(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}le(I),ae(I,e)}function da(){le(I),le(xe),le(he)}function Vi(e){e.memoizedState!==null&&ae(ft,e);var t=I.current,a=ch(t,e.type);t!==a&&(ae(xe,e),ae(I,a))}function os(e){xe.current===e&&(le(I),le(xe)),ft.current===e&&(le(ft),Xn._currentValue=q)}var Qi=Object.prototype.hasOwnProperty,Zi=n.unstable_scheduleCallback,Fi=n.unstable_cancelCallback,cp=n.unstable_shouldYield,up=n.unstable_requestPaint,Ht=n.unstable_now,dp=n.unstable_getCurrentPriorityLevel,Yc=n.unstable_ImmediatePriority,Gc=n.unstable_UserBlockingPriority,cs=n.unstable_NormalPriority,fp=n.unstable_LowPriority,Xc=n.unstable_IdlePriority,hp=n.log,mp=n.unstable_setDisableYieldValue,$l=null,ht=null;function fa(e){if(typeof hp=="function"&&mp(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode($l,e)}catch{}}var mt=Math.clz32?Math.clz32:gp,pp=Math.log,yp=Math.LN2;function gp(e){return e>>>=0,e===0?32:31-(pp(e)/yp|0)|0}var us=256,ds=4194304;function La(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function fs(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var i=0,o=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~o,l!==0?i=La(l):(h&=y,h!==0?i=La(h):a||(a=y&~e,a!==0&&(i=La(a))))):(y=l&~o,y!==0?i=La(y):h!==0?i=La(h):a||(a=l&~e,a!==0&&(i=La(a)))),i===0?0:t!==0&&t!==i&&(t&o)===0&&(o=i&-i,a=t&-t,o>=a||o===32&&(a&4194048)!==0)?t:i}function Wl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function vp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vc(){var e=us;return us<<=1,(us&4194048)===0&&(us=256),e}function Qc(){var e=ds;return ds<<=1,(ds&62914560)===0&&(ds=4194304),e}function Ki(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Pl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function xp(e,t,a,l,i,o){var h=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var y=e.entanglements,j=e.expirationTimes,L=e.hiddenUpdates;for(a=h&~a;0<a;){var Z=31-mt(a),J=1<<Z;y[Z]=0,j[Z]=-1;var H=L[Z];if(H!==null)for(L[Z]=null,Z=0;Z<H.length;Z++){var k=H[Z];k!==null&&(k.lane&=-536870913)}a&=~J}l!==0&&Zc(e,l,0),o!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=o&~(h&~t))}function Zc(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-mt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Fc(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-mt(a),i=1<<l;i&t|e[l]&t&&(e[l]|=t),a&=~i}}function Ji(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function $i(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Kc(){var e=te.p;return e!==0?e:(e=window.event,e===void 0?32:Ch(e.type))}function bp(e,t){var a=te.p;try{return te.p=e,t()}finally{te.p=a}}var ha=Math.random().toString(36).slice(2),Ie="__reactFiber$"+ha,st="__reactProps$"+ha,il="__reactContainer$"+ha,Wi="__reactEvents$"+ha,jp="__reactListeners$"+ha,Sp="__reactHandles$"+ha,Jc="__reactResources$"+ha,Il="__reactMarker$"+ha;function Pi(e){delete e[Ie],delete e[st],delete e[Wi],delete e[jp],delete e[Sp]}function rl(e){var t=e[Ie];if(t)return t;for(var a=e.parentNode;a;){if(t=a[il]||a[Ie]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=hh(e);e!==null;){if(a=e[Ie])return a;e=hh(e)}return t}e=a,a=e.parentNode}return null}function ol(e){if(e=e[Ie]||e[il]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function en(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function cl(e){var t=e[Jc];return t||(t=e[Jc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[Il]=!0}var $c=new Set,Wc={};function Ha(e,t){ul(e,t),ul(e+"Capture",t)}function ul(e,t){for(Wc[e]=t,e=0;e<t.length;e++)$c.add(t[e])}var Np=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Pc={},Ic={};function Tp(e){return Qi.call(Ic,e)?!0:Qi.call(Pc,e)?!1:Np.test(e)?Ic[e]=!0:(Pc[e]=!0,!1)}function hs(e,t,a){if(Tp(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function ms(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Zt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var Ii,eu;function dl(e){if(Ii===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Ii=t&&t[1]||"",eu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ii+e+eu}var er=!1;function tr(e,t){if(!e||er)return"";er=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var J=function(){throw Error()};if(Object.defineProperty(J.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(J,[])}catch(k){var H=k}Reflect.construct(e,[],J)}else{try{J.call()}catch(k){H=k}e.call(J.prototype)}}else{try{throw Error()}catch(k){H=k}(J=e())&&typeof J.catch=="function"&&J.catch(function(){})}}catch(k){if(k&&H&&typeof k.stack=="string")return[k.stack,H.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),h=o[0],y=o[1];if(h&&y){var j=h.split(`
`),L=y.split(`
`);for(i=l=0;l<j.length&&!j[l].includes("DetermineComponentFrameRoot");)l++;for(;i<L.length&&!L[i].includes("DetermineComponentFrameRoot");)i++;if(l===j.length||i===L.length)for(l=j.length-1,i=L.length-1;1<=l&&0<=i&&j[l]!==L[i];)i--;for(;1<=l&&0<=i;l--,i--)if(j[l]!==L[i]){if(l!==1||i!==1)do if(l--,i--,0>i||j[l]!==L[i]){var Z=`
`+j[l].replace(" at new "," at ");return e.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",e.displayName)),Z}while(1<=l&&0<=i);break}}}finally{er=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?dl(a):""}function Ep(e){switch(e.tag){case 26:case 27:case 5:return dl(e.type);case 16:return dl("Lazy");case 13:return dl("Suspense");case 19:return dl("SuspenseList");case 0:case 15:return tr(e.type,!1);case 11:return tr(e.type.render,!1);case 1:return tr(e.type,!0);case 31:return dl("Activity");default:return""}}function tu(e){try{var t="";do t+=Ep(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Nt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function au(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function _p(e){var t=au(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,o=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){l=""+h,o.call(this,h)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ps(e){e._valueTracker||(e._valueTracker=_p(e))}function lu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=au(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function ys(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var wp=/[\n"\\]/g;function Tt(e){return e.replace(wp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ar(e,t,a,l,i,o,h,y){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Nt(t)):e.value!==""+Nt(t)&&(e.value=""+Nt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?lr(e,h,Nt(t)):a!=null?lr(e,h,Nt(a)):l!=null&&e.removeAttribute("value"),i==null&&o!=null&&(e.defaultChecked=!!o),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+Nt(y):e.removeAttribute("name")}function nu(e,t,a,l,i,o,h,y){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||a!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;a=a!=null?""+Nt(a):"",t=t!=null?""+Nt(t):a,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function lr(e,t,a){t==="number"&&ys(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function fl(e,t,a,l){if(e=e.options,t){t={};for(var i=0;i<a.length;i++)t["$"+a[i]]=!0;for(a=0;a<e.length;a++)i=t.hasOwnProperty("$"+e[a].value),e[a].selected!==i&&(e[a].selected=i),i&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Nt(a),t=null,i=0;i<e.length;i++){if(e[i].value===a){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function su(e,t,a){if(t!=null&&(t=""+Nt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Nt(a):""}function iu(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(c(92));if(ye(l)){if(1<l.length)throw Error(c(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Nt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function hl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Cp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ru(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Cp.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function ou(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&a[i]!==l&&ru(e,i,l)}else for(var o in t)t.hasOwnProperty(o)&&ru(e,o,t[o])}function nr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ap=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function gs(e){return Ap.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var sr=null;function ir(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ml=null,pl=null;function cu(e){var t=ol(e);if(t&&(e=t.stateNode)){var a=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(ar(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Tt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var i=l[st]||null;if(!i)throw Error(c(90));ar(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&lu(l)}break e;case"textarea":su(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&fl(e,!!a.multiple,t,!1)}}}var rr=!1;function uu(e,t,a){if(rr)return e(t,a);rr=!0;try{var l=e(t);return l}finally{if(rr=!1,(ml!==null||pl!==null)&&(ai(),ml&&(t=ml,e=pl,pl=ml=null,cu(t),e)))for(t=0;t<e.length;t++)cu(e[t])}}function tn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[st]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var Ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),or=!1;if(Ft)try{var an={};Object.defineProperty(an,"passive",{get:function(){or=!0}}),window.addEventListener("test",an,an),window.removeEventListener("test",an,an)}catch{or=!1}var ma=null,cr=null,vs=null;function du(){if(vs)return vs;var e,t=cr,a=t.length,l,i="value"in ma?ma.value:ma.textContent,o=i.length;for(e=0;e<a&&t[e]===i[e];e++);var h=a-e;for(l=1;l<=h&&t[a-l]===i[o-l];l++);return vs=i.slice(e,1<l?1-l:void 0)}function xs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bs(){return!0}function fu(){return!1}function it(e){function t(a,l,i,o,h){this._reactName=a,this._targetInst=i,this.type=l,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(a=e[y],this[y]=a?a(o):o[y]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?bs:fu,this.isPropagationStopped=fu,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=bs)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=bs)},persist:function(){},isPersistent:bs}),t}var ka={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},js=it(ka),ln=x({},ka,{view:0,detail:0}),Op=it(ln),ur,dr,nn,Ss=x({},ln,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nn&&(nn&&e.type==="mousemove"?(ur=e.screenX-nn.screenX,dr=e.screenY-nn.screenY):dr=ur=0,nn=e),ur)},movementY:function(e){return"movementY"in e?e.movementY:dr}}),hu=it(Ss),Dp=x({},Ss,{dataTransfer:0}),Mp=it(Dp),zp=x({},ln,{relatedTarget:0}),fr=it(zp),Up=x({},ka,{animationName:0,elapsedTime:0,pseudoElement:0}),Bp=it(Up),Lp=x({},ka,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hp=it(Lp),kp=x({},ka,{data:0}),mu=it(kp),qp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Yp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Gp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Gp[e])?!!t[e]:!1}function hr(){return Xp}var Vp=x({},ln,{key:function(e){if(e.key){var t=qp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Yp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hr,charCode:function(e){return e.type==="keypress"?xs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Qp=it(Vp),Zp=x({},Ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=it(Zp),Fp=x({},ln,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hr}),Kp=it(Fp),Jp=x({},ka,{propertyName:0,elapsedTime:0,pseudoElement:0}),$p=it(Jp),Wp=x({},Ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Pp=it(Wp),Ip=x({},ka,{newState:0,oldState:0}),ey=it(Ip),ty=[9,13,27,32],mr=Ft&&"CompositionEvent"in window,sn=null;Ft&&"documentMode"in document&&(sn=document.documentMode);var ay=Ft&&"TextEvent"in window&&!sn,yu=Ft&&(!mr||sn&&8<sn&&11>=sn),gu=" ",vu=!1;function xu(e,t){switch(e){case"keyup":return ty.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function bu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yl=!1;function ly(e,t){switch(e){case"compositionend":return bu(t);case"keypress":return t.which!==32?null:(vu=!0,gu);case"textInput":return e=t.data,e===gu&&vu?null:e;default:return null}}function ny(e,t){if(yl)return e==="compositionend"||!mr&&xu(e,t)?(e=du(),vs=cr=ma=null,yl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return yu&&t.locale!=="ko"?null:t.data;default:return null}}var sy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ju(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!sy[e.type]:t==="textarea"}function Su(e,t,a,l){ml?pl?pl.push(l):pl=[l]:ml=l,t=oi(t,"onChange"),0<t.length&&(a=new js("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var rn=null,on=null;function iy(e){lh(e,0)}function Ns(e){var t=en(e);if(lu(t))return e}function Nu(e,t){if(e==="change")return t}var Tu=!1;if(Ft){var pr;if(Ft){var yr="oninput"in document;if(!yr){var Eu=document.createElement("div");Eu.setAttribute("oninput","return;"),yr=typeof Eu.oninput=="function"}pr=yr}else pr=!1;Tu=pr&&(!document.documentMode||9<document.documentMode)}function _u(){rn&&(rn.detachEvent("onpropertychange",wu),on=rn=null)}function wu(e){if(e.propertyName==="value"&&Ns(on)){var t=[];Su(t,on,e,ir(e)),uu(iy,t)}}function ry(e,t,a){e==="focusin"?(_u(),rn=t,on=a,rn.attachEvent("onpropertychange",wu)):e==="focusout"&&_u()}function oy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ns(on)}function cy(e,t){if(e==="click")return Ns(t)}function uy(e,t){if(e==="input"||e==="change")return Ns(t)}function dy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:dy;function cn(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var i=a[l];if(!Qi.call(t,i)||!pt(e[i],t[i]))return!1}return!0}function Cu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ru(e,t){var a=Cu(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Cu(a)}}function Au(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Au(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ou(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ys(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=ys(e.document)}return t}function gr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var fy=Ft&&"documentMode"in document&&11>=document.documentMode,gl=null,vr=null,un=null,xr=!1;function Du(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;xr||gl==null||gl!==ys(l)||(l=gl,"selectionStart"in l&&gr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),un&&cn(un,l)||(un=l,l=oi(vr,"onSelect"),0<l.length&&(t=new js("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=gl)))}function qa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var vl={animationend:qa("Animation","AnimationEnd"),animationiteration:qa("Animation","AnimationIteration"),animationstart:qa("Animation","AnimationStart"),transitionrun:qa("Transition","TransitionRun"),transitionstart:qa("Transition","TransitionStart"),transitioncancel:qa("Transition","TransitionCancel"),transitionend:qa("Transition","TransitionEnd")},br={},Mu={};Ft&&(Mu=document.createElement("div").style,"AnimationEvent"in window||(delete vl.animationend.animation,delete vl.animationiteration.animation,delete vl.animationstart.animation),"TransitionEvent"in window||delete vl.transitionend.transition);function Ya(e){if(br[e])return br[e];if(!vl[e])return e;var t=vl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Mu)return br[e]=t[a];return e}var zu=Ya("animationend"),Uu=Ya("animationiteration"),Bu=Ya("animationstart"),hy=Ya("transitionrun"),my=Ya("transitionstart"),py=Ya("transitioncancel"),Lu=Ya("transitionend"),Hu=new Map,jr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");jr.push("scrollEnd");function Dt(e,t){Hu.set(e,t),Ha(t,[e])}var ku=new WeakMap;function Et(e,t){if(typeof e=="object"&&e!==null){var a=ku.get(e);return a!==void 0?a:(t={value:e,source:t,stack:tu(t)},ku.set(e,t),t)}return{value:e,source:t,stack:tu(t)}}var _t=[],xl=0,Sr=0;function Ts(){for(var e=xl,t=Sr=xl=0;t<e;){var a=_t[t];_t[t++]=null;var l=_t[t];_t[t++]=null;var i=_t[t];_t[t++]=null;var o=_t[t];if(_t[t++]=null,l!==null&&i!==null){var h=l.pending;h===null?i.next=i:(i.next=h.next,h.next=i),l.pending=i}o!==0&&qu(a,i,o)}}function Es(e,t,a,l){_t[xl++]=e,_t[xl++]=t,_t[xl++]=a,_t[xl++]=l,Sr|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Nr(e,t,a,l){return Es(e,t,a,l),_s(e)}function bl(e,t){return Es(e,null,null,t),_s(e)}function qu(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var i=!1,o=e.return;o!==null;)o.childLanes|=a,l=o.alternate,l!==null&&(l.childLanes|=a),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(i=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,i&&t!==null&&(i=31-mt(a),e=o.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=a|536870912),o):null}function _s(e){if(50<Un)throw Un=0,Ao=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var jl={};function yy(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,a,l){return new yy(e,t,a,l)}function Tr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Kt(e,t){var a=e.alternate;return a===null?(a=yt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Yu(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ws(e,t,a,l,i,o){var h=0;if(l=e,typeof e=="function")Tr(e)&&(h=1);else if(typeof e=="string")h=vg(e,a,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ee:return e=yt(31,a,t,i),e.elementType=ee,e.lanes=o,e;case C:return Ga(a.children,i,o,t);case D:h=8,i|=24;break;case M:return e=yt(12,a,t,i|2),e.elementType=M,e.lanes=o,e;case w:return e=yt(13,a,t,i),e.elementType=w,e.lanes=o,e;case W:return e=yt(19,a,t,i),e.elementType=W,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case O:case T:h=10;break e;case R:h=9;break e;case N:h=11;break e;case P:h=14;break e;case F:h=16,l=null;break e}h=29,a=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=yt(h,a,t,i),t.elementType=e,t.type=l,t.lanes=o,t}function Ga(e,t,a,l){return e=yt(7,e,l,t),e.lanes=a,e}function Er(e,t,a){return e=yt(6,e,null,t),e.lanes=a,e}function _r(e,t,a){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Sl=[],Nl=0,Cs=null,Rs=0,wt=[],Ct=0,Xa=null,Jt=1,$t="";function Va(e,t){Sl[Nl++]=Rs,Sl[Nl++]=Cs,Cs=e,Rs=t}function Gu(e,t,a){wt[Ct++]=Jt,wt[Ct++]=$t,wt[Ct++]=Xa,Xa=e;var l=Jt;e=$t;var i=32-mt(l)-1;l&=~(1<<i),a+=1;var o=32-mt(t)+i;if(30<o){var h=i-i%5;o=(l&(1<<h)-1).toString(32),l>>=h,i-=h,Jt=1<<32-mt(t)+i|a<<i|l,$t=o+e}else Jt=1<<o|a<<i|l,$t=e}function wr(e){e.return!==null&&(Va(e,1),Gu(e,1,0))}function Cr(e){for(;e===Cs;)Cs=Sl[--Nl],Sl[Nl]=null,Rs=Sl[--Nl],Sl[Nl]=null;for(;e===Xa;)Xa=wt[--Ct],wt[Ct]=null,$t=wt[--Ct],wt[Ct]=null,Jt=wt[--Ct],wt[Ct]=null}var lt=null,He=null,Ee=!1,Qa=null,kt=!1,Rr=Error(c(519));function Za(e){var t=Error(c(418,""));throw hn(Et(t,e)),Rr}function Xu(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Ie]=e,t[st]=l,a){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(a=0;a<Ln.length;a++)Se(Ln[a],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),nu(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ps(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),iu(t,l.value,l.defaultValue,l.children),ps(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||rh(t.textContent,a)?(l.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),l.onScroll!=null&&Se("scroll",t),l.onScrollEnd!=null&&Se("scrollend",t),l.onClick!=null&&(t.onclick=ci),t=!0):t=!1,t||Za(e)}function Vu(e){for(lt=e.return;lt;)switch(lt.tag){case 5:case 13:kt=!1;return;case 27:case 3:kt=!0;return;default:lt=lt.return}}function dn(e){if(e!==lt)return!1;if(!Ee)return Vu(e),Ee=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Zo(e.type,e.memoizedProps)),a=!a),a&&He&&Za(e),Vu(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){He=zt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}He=null}}else t===27?(t=He,Aa(e.type)?(e=$o,$o=null,He=e):He=t):He=lt?zt(e.stateNode.nextSibling):null;return!0}function fn(){He=lt=null,Ee=!1}function Qu(){var e=Qa;return e!==null&&(ct===null?ct=e:ct.push.apply(ct,e),Qa=null),e}function hn(e){Qa===null?Qa=[e]:Qa.push(e)}var Ar=V(null),Fa=null,Wt=null;function pa(e,t,a){ae(Ar,t._currentValue),t._currentValue=a}function Pt(e){e._currentValue=Ar.current,le(Ar)}function Or(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Dr(e,t,a,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){var h=i.child;o=o.firstContext;e:for(;o!==null;){var y=o;o=i;for(var j=0;j<t.length;j++)if(y.context===t[j]){o.lanes|=a,y=o.alternate,y!==null&&(y.lanes|=a),Or(o.return,a,e),l||(h=null);break e}o=y.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(c(341));h.lanes|=a,o=h.alternate,o!==null&&(o.lanes|=a),Or(h,a,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function mn(e,t,a,l){e=null;for(var i=t,o=!1;i!==null;){if(!o){if((i.flags&524288)!==0)o=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(c(387));if(h=h.memoizedProps,h!==null){var y=i.type;pt(i.pendingProps.value,h.value)||(e!==null?e.push(y):e=[y])}}else if(i===ft.current){if(h=i.alternate,h===null)throw Error(c(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Xn):e=[Xn])}i=i.return}e!==null&&Dr(t,e,a,l),t.flags|=262144}function As(e){for(e=e.firstContext;e!==null;){if(!pt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ka(e){Fa=e,Wt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function et(e){return Zu(Fa,e)}function Os(e,t){return Fa===null&&Ka(e),Zu(e,t)}function Zu(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Wt===null){if(e===null)throw Error(c(308));Wt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Wt=Wt.next=t;return a}var gy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},vy=n.unstable_scheduleCallback,xy=n.unstable_NormalPriority,Ve={$$typeof:T,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mr(){return{controller:new gy,data:new Map,refCount:0}}function pn(e){e.refCount--,e.refCount===0&&vy(xy,function(){e.controller.abort()})}var yn=null,zr=0,Tl=0,El=null;function by(e,t){if(yn===null){var a=yn=[];zr=0,Tl=Lo(),El={status:"pending",value:void 0,then:function(l){a.push(l)}}}return zr++,t.then(Fu,Fu),t}function Fu(){if(--zr===0&&yn!==null){El!==null&&(El.status="fulfilled");var e=yn;yn=null,Tl=0,El=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function jy(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<a.length;i++)(0,a[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),l}var Ku=X.S;X.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&by(e,t),Ku!==null&&Ku(e,t)};var Ja=V(null);function Ur(){var e=Ja.current;return e!==null?e:De.pooledCache}function Ds(e,t){t===null?ae(Ja,Ja.current):ae(Ja,t.pool)}function Ju(){var e=Ur();return e===null?null:{parent:Ve._currentValue,pool:e}}var gn=Error(c(460)),$u=Error(c(474)),Ms=Error(c(542)),Br={then:function(){}};function Wu(e){return e=e.status,e==="fulfilled"||e==="rejected"}function zs(){}function Pu(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(zs,zs),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ed(e),e;default:if(typeof t.status=="string")t.then(zs,zs);else{if(e=De,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ed(e),e}throw vn=t,gn}}var vn=null;function Iu(){if(vn===null)throw Error(c(459));var e=vn;return vn=null,e}function ed(e){if(e===gn||e===Ms)throw Error(c(483))}var ya=!1;function Lr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ga(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function va(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(_e&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=_s(e),qu(e,null,a),t}return Es(e,l,t,a),_s(e)}function xn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Fc(e,a)}}function kr(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var i=null,o=null;if(a=a.firstBaseUpdate,a!==null){do{var h={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};o===null?i=o=h:o=o.next=h,a=a.next}while(a!==null);o===null?i=o=t:o=o.next=t}else i=o=t;a={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var qr=!1;function bn(){if(qr){var e=El;if(e!==null)throw e}}function jn(e,t,a,l){qr=!1;var i=e.updateQueue;ya=!1;var o=i.firstBaseUpdate,h=i.lastBaseUpdate,y=i.shared.pending;if(y!==null){i.shared.pending=null;var j=y,L=j.next;j.next=null,h===null?o=L:h.next=L,h=j;var Z=e.alternate;Z!==null&&(Z=Z.updateQueue,y=Z.lastBaseUpdate,y!==h&&(y===null?Z.firstBaseUpdate=L:y.next=L,Z.lastBaseUpdate=j))}if(o!==null){var J=i.baseState;h=0,Z=L=j=null,y=o;do{var H=y.lane&-536870913,k=H!==y.lane;if(k?(Ne&H)===H:(l&H)===H){H!==0&&H===Tl&&(qr=!0),Z!==null&&(Z=Z.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var fe=e,ue=y;H=t;var Ae=a;switch(ue.tag){case 1:if(fe=ue.payload,typeof fe=="function"){J=fe.call(Ae,J,H);break e}J=fe;break e;case 3:fe.flags=fe.flags&-65537|128;case 0:if(fe=ue.payload,H=typeof fe=="function"?fe.call(Ae,J,H):fe,H==null)break e;J=x({},J,H);break e;case 2:ya=!0}}H=y.callback,H!==null&&(e.flags|=64,k&&(e.flags|=8192),k=i.callbacks,k===null?i.callbacks=[H]:k.push(H))}else k={lane:H,tag:y.tag,payload:y.payload,callback:y.callback,next:null},Z===null?(L=Z=k,j=J):Z=Z.next=k,h|=H;if(y=y.next,y===null){if(y=i.shared.pending,y===null)break;k=y,y=k.next,k.next=null,i.lastBaseUpdate=k,i.shared.pending=null}}while(!0);Z===null&&(j=J),i.baseState=j,i.firstBaseUpdate=L,i.lastBaseUpdate=Z,o===null&&(i.shared.lanes=0),_a|=h,e.lanes=h,e.memoizedState=J}}function td(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function ad(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)td(a[e],t)}var _l=V(null),Us=V(0);function ld(e,t){e=sa,ae(Us,e),ae(_l,t),sa=e|t.baseLanes}function Yr(){ae(Us,sa),ae(_l,_l.current)}function Gr(){sa=Us.current,le(_l),le(Us)}var xa=0,ve=null,Ce=null,Ge=null,Bs=!1,wl=!1,$a=!1,Ls=0,Sn=0,Cl=null,Sy=0;function qe(){throw Error(c(321))}function Xr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!pt(e[a],t[a]))return!1;return!0}function Vr(e,t,a,l,i,o){return xa=o,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,X.H=e===null||e.memoizedState===null?qd:Yd,$a=!1,o=a(l,i),$a=!1,wl&&(o=sd(t,a,l,i)),nd(e),o}function nd(e){X.H=Xs;var t=Ce!==null&&Ce.next!==null;if(xa=0,Ge=Ce=ve=null,Bs=!1,Sn=0,Cl=null,t)throw Error(c(300));e===null||Fe||(e=e.dependencies,e!==null&&As(e)&&(Fe=!0))}function sd(e,t,a,l){ve=e;var i=0;do{if(wl&&(Cl=null),Sn=0,wl=!1,25<=i)throw Error(c(301));if(i+=1,Ge=Ce=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}X.H=Ry,o=t(a,l)}while(wl);return o}function Ny(){var e=X.H,t=e.useState()[0];return t=typeof t.then=="function"?Nn(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(ve.flags|=1024),t}function Qr(){var e=Ls!==0;return Ls=0,e}function Zr(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Fr(e){if(Bs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Bs=!1}xa=0,Ge=Ce=ve=null,wl=!1,Sn=Ls=0,Cl=null}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ve.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Xe(){if(Ce===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ge===null?ve.memoizedState:Ge.next;if(t!==null)Ge=t,Ce=e;else{if(e===null)throw ve.alternate===null?Error(c(467)):Error(c(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ge===null?ve.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Kr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Nn(e){var t=Sn;return Sn+=1,Cl===null&&(Cl=[]),e=Pu(Cl,e,t),t=ve,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,X.H=t===null||t.memoizedState===null?qd:Yd),e}function Hs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Nn(e);if(e.$$typeof===T)return et(e)}throw Error(c(438,String(e)))}function Jr(e){var t=null,a=ve.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ve.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Kr(),ve.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Q;return t.index++,a}function It(e,t){return typeof t=="function"?t(e):t}function ks(e){var t=Xe();return $r(t,Ce,e)}function $r(e,t,a){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=a;var i=e.baseQueue,o=l.pending;if(o!==null){if(i!==null){var h=i.next;i.next=o.next,o.next=h}t.baseQueue=i=o,l.pending=null}if(o=e.baseState,i===null)e.memoizedState=o;else{t=i.next;var y=h=null,j=null,L=t,Z=!1;do{var J=L.lane&-536870913;if(J!==L.lane?(Ne&J)===J:(xa&J)===J){var H=L.revertLane;if(H===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null}),J===Tl&&(Z=!0);else if((xa&H)===H){L=L.next,H===Tl&&(Z=!0);continue}else J={lane:0,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},j===null?(y=j=J,h=o):j=j.next=J,ve.lanes|=H,_a|=H;J=L.action,$a&&a(o,J),o=L.hasEagerState?L.eagerState:a(o,J)}else H={lane:J,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},j===null?(y=j=H,h=o):j=j.next=H,ve.lanes|=J,_a|=J;L=L.next}while(L!==null&&L!==t);if(j===null?h=o:j.next=y,!pt(o,e.memoizedState)&&(Fe=!0,Z&&(a=El,a!==null)))throw a;e.memoizedState=o,e.baseState=h,e.baseQueue=j,l.lastRenderedState=o}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Wr(e){var t=Xe(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var l=a.dispatch,i=a.pending,o=t.memoizedState;if(i!==null){a.pending=null;var h=i=i.next;do o=e(o,h.action),h=h.next;while(h!==i);pt(o,t.memoizedState)||(Fe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),a.lastRenderedState=o}return[o,l]}function id(e,t,a){var l=ve,i=Xe(),o=Ee;if(o){if(a===void 0)throw Error(c(407));a=a()}else a=t();var h=!pt((Ce||i).memoizedState,a);h&&(i.memoizedState=a,Fe=!0),i=i.queue;var y=cd.bind(null,l,i,e);if(Tn(2048,8,y,[e]),i.getSnapshot!==t||h||Ge!==null&&Ge.memoizedState.tag&1){if(l.flags|=2048,Rl(9,qs(),od.bind(null,l,i,a,t),null),De===null)throw Error(c(349));o||(xa&124)!==0||rd(l,t,a)}return a}function rd(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ve.updateQueue,t===null?(t=Kr(),ve.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function od(e,t,a,l){t.value=a,t.getSnapshot=l,ud(t)&&dd(e)}function cd(e,t,a){return a(function(){ud(t)&&dd(e)})}function ud(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!pt(e,a)}catch{return!0}}function dd(e){var t=bl(e,2);t!==null&&jt(t,e,2)}function Pr(e){var t=rt();if(typeof e=="function"){var a=e;if(e=a(),$a){fa(!0);try{a()}finally{fa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:e},t}function fd(e,t,a,l){return e.baseState=a,$r(e,Ce,typeof l=="function"?l:It)}function Ty(e,t,a,l,i){if(Gs(e))throw Error(c(485));if(e=t.action,e!==null){var o={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};X.T!==null?a(!0):o.isTransition=!1,l(o),a=t.pending,a===null?(o.next=t.pending=o,hd(t,o)):(o.next=a.next,t.pending=a.next=o)}}function hd(e,t){var a=t.action,l=t.payload,i=e.state;if(t.isTransition){var o=X.T,h={};X.T=h;try{var y=a(i,l),j=X.S;j!==null&&j(h,y),md(e,t,y)}catch(L){Ir(e,t,L)}finally{X.T=o}}else try{o=a(i,l),md(e,t,o)}catch(L){Ir(e,t,L)}}function md(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){pd(e,t,l)},function(l){return Ir(e,t,l)}):pd(e,t,a)}function pd(e,t,a){t.status="fulfilled",t.value=a,yd(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,hd(e,a)))}function Ir(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,yd(t),t=t.next;while(t!==l)}e.action=null}function yd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function gd(e,t){return t}function vd(e,t){if(Ee){var a=De.formState;if(a!==null){e:{var l=ve;if(Ee){if(He){t:{for(var i=He,o=kt;i.nodeType!==8;){if(!o){i=null;break t}if(i=zt(i.nextSibling),i===null){i=null;break t}}o=i.data,i=o==="F!"||o==="F"?i:null}if(i){He=zt(i.nextSibling),l=i.data==="F!";break e}}Za(l)}l=!1}l&&(t=a[0])}}return a=rt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:gd,lastRenderedState:t},a.queue=l,a=Ld.bind(null,ve,l),l.dispatch=a,l=Pr(!1),o=no.bind(null,ve,!1,l.queue),l=rt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,a=Ty.bind(null,ve,i,o,a),i.dispatch=a,l.memoizedState=e,[t,a,!1]}function xd(e){var t=Xe();return bd(t,Ce,e)}function bd(e,t,a){if(t=$r(e,t,gd)[0],e=ks(It)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Nn(t)}catch(h){throw h===gn?Ms:h}else l=t;t=Xe();var i=t.queue,o=i.dispatch;return a!==t.memoizedState&&(ve.flags|=2048,Rl(9,qs(),Ey.bind(null,i,a),null)),[l,o,e]}function Ey(e,t){e.action=t}function jd(e){var t=Xe(),a=Ce;if(a!==null)return bd(t,a,e);Xe(),t=t.memoizedState,a=Xe();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Rl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ve.updateQueue,t===null&&(t=Kr(),ve.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function qs(){return{destroy:void 0,resource:void 0}}function Sd(){return Xe().memoizedState}function Ys(e,t,a,l){var i=rt();l=l===void 0?null:l,ve.flags|=e,i.memoizedState=Rl(1|t,qs(),a,l)}function Tn(e,t,a,l){var i=Xe();l=l===void 0?null:l;var o=i.memoizedState.inst;Ce!==null&&l!==null&&Xr(l,Ce.memoizedState.deps)?i.memoizedState=Rl(t,o,a,l):(ve.flags|=e,i.memoizedState=Rl(1|t,o,a,l))}function Nd(e,t){Ys(8390656,8,e,t)}function Td(e,t){Tn(2048,8,e,t)}function Ed(e,t){return Tn(4,2,e,t)}function _d(e,t){return Tn(4,4,e,t)}function wd(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Cd(e,t,a){a=a!=null?a.concat([e]):null,Tn(4,4,wd.bind(null,t,e),a)}function eo(){}function Rd(e,t){var a=Xe();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Xr(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Ad(e,t){var a=Xe();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Xr(t,l[1]))return l[0];if(l=e(),$a){fa(!0);try{e()}finally{fa(!1)}}return a.memoizedState=[l,t],l}function to(e,t,a){return a===void 0||(xa&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=zf(),ve.lanes|=e,_a|=e,a)}function Od(e,t,a,l){return pt(a,t)?a:_l.current!==null?(e=to(e,a,l),pt(e,t)||(Fe=!0),e):(xa&42)===0?(Fe=!0,e.memoizedState=a):(e=zf(),ve.lanes|=e,_a|=e,t)}function Dd(e,t,a,l,i){var o=te.p;te.p=o!==0&&8>o?o:8;var h=X.T,y={};X.T=y,no(e,!1,t,a);try{var j=i(),L=X.S;if(L!==null&&L(y,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var Z=jy(j,l);En(e,t,Z,bt(e))}else En(e,t,l,bt(e))}catch(J){En(e,t,{then:function(){},status:"rejected",reason:J},bt())}finally{te.p=o,X.T=h}}function _y(){}function ao(e,t,a,l){if(e.tag!==5)throw Error(c(476));var i=Md(e).queue;Dd(e,i,t,q,a===null?_y:function(){return zd(e),a(l)})}function Md(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:q,baseState:q,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:q},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function zd(e){var t=Md(e).next.queue;En(e,t,{},bt())}function lo(){return et(Xn)}function Ud(){return Xe().memoizedState}function Bd(){return Xe().memoizedState}function wy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=bt();e=ga(a);var l=va(t,e,a);l!==null&&(jt(l,t,a),xn(l,t,a)),t={cache:Mr()},e.payload=t;return}t=t.return}}function Cy(e,t,a){var l=bt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Gs(e)?Hd(t,a):(a=Nr(e,t,a,l),a!==null&&(jt(a,e,l),kd(a,t,l)))}function Ld(e,t,a){var l=bt();En(e,t,a,l)}function En(e,t,a,l){var i={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Gs(e))Hd(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var h=t.lastRenderedState,y=o(h,a);if(i.hasEagerState=!0,i.eagerState=y,pt(y,h))return Es(e,t,i,0),De===null&&Ts(),!1}catch{}finally{}if(a=Nr(e,t,i,l),a!==null)return jt(a,e,l),kd(a,t,l),!0}return!1}function no(e,t,a,l){if(l={lane:2,revertLane:Lo(),action:l,hasEagerState:!1,eagerState:null,next:null},Gs(e)){if(t)throw Error(c(479))}else t=Nr(e,a,l,2),t!==null&&jt(t,e,2)}function Gs(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function Hd(e,t){wl=Bs=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function kd(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Fc(e,a)}}var Xs={readContext:et,use:Hs,useCallback:qe,useContext:qe,useEffect:qe,useImperativeHandle:qe,useLayoutEffect:qe,useInsertionEffect:qe,useMemo:qe,useReducer:qe,useRef:qe,useState:qe,useDebugValue:qe,useDeferredValue:qe,useTransition:qe,useSyncExternalStore:qe,useId:qe,useHostTransitionStatus:qe,useFormState:qe,useActionState:qe,useOptimistic:qe,useMemoCache:qe,useCacheRefresh:qe},qd={readContext:et,use:Hs,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:Nd,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ys(4194308,4,wd.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ys(4194308,4,e,t)},useInsertionEffect:function(e,t){Ys(4,2,e,t)},useMemo:function(e,t){var a=rt();t=t===void 0?null:t;var l=e();if($a){fa(!0);try{e()}finally{fa(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=rt();if(a!==void 0){var i=a(t);if($a){fa(!0);try{a(t)}finally{fa(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=Cy.bind(null,ve,e),[l.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:function(e){e=Pr(e);var t=e.queue,a=Ld.bind(null,ve,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:eo,useDeferredValue:function(e,t){var a=rt();return to(a,e,t)},useTransition:function(){var e=Pr(!1);return e=Dd.bind(null,ve,e.queue,!0,!1),rt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ve,i=rt();if(Ee){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),De===null)throw Error(c(349));(Ne&124)!==0||rd(l,t,a)}i.memoizedState=a;var o={value:a,getSnapshot:t};return i.queue=o,Nd(cd.bind(null,l,o,e),[e]),l.flags|=2048,Rl(9,qs(),od.bind(null,l,o,a,t),null),a},useId:function(){var e=rt(),t=De.identifierPrefix;if(Ee){var a=$t,l=Jt;a=(l&~(1<<32-mt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ls++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Sy++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:lo,useFormState:vd,useActionState:vd,useOptimistic:function(e){var t=rt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=no.bind(null,ve,!0,a),a.dispatch=t,[e,t]},useMemoCache:Jr,useCacheRefresh:function(){return rt().memoizedState=wy.bind(null,ve)}},Yd={readContext:et,use:Hs,useCallback:Rd,useContext:et,useEffect:Td,useImperativeHandle:Cd,useInsertionEffect:Ed,useLayoutEffect:_d,useMemo:Ad,useReducer:ks,useRef:Sd,useState:function(){return ks(It)},useDebugValue:eo,useDeferredValue:function(e,t){var a=Xe();return Od(a,Ce.memoizedState,e,t)},useTransition:function(){var e=ks(It)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:id,useId:Ud,useHostTransitionStatus:lo,useFormState:xd,useActionState:xd,useOptimistic:function(e,t){var a=Xe();return fd(a,Ce,e,t)},useMemoCache:Jr,useCacheRefresh:Bd},Ry={readContext:et,use:Hs,useCallback:Rd,useContext:et,useEffect:Td,useImperativeHandle:Cd,useInsertionEffect:Ed,useLayoutEffect:_d,useMemo:Ad,useReducer:Wr,useRef:Sd,useState:function(){return Wr(It)},useDebugValue:eo,useDeferredValue:function(e,t){var a=Xe();return Ce===null?to(a,e,t):Od(a,Ce.memoizedState,e,t)},useTransition:function(){var e=Wr(It)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:id,useId:Ud,useHostTransitionStatus:lo,useFormState:jd,useActionState:jd,useOptimistic:function(e,t){var a=Xe();return Ce!==null?fd(a,Ce,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Jr,useCacheRefresh:Bd},Al=null,_n=0;function Vs(e){var t=_n;return _n+=1,Al===null&&(Al=[]),Pu(Al,e,t)}function wn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Qs(e,t){throw t.$$typeof===b?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Gd(e){var t=e._init;return t(e._payload)}function Xd(e){function t(z,A){if(e){var B=z.deletions;B===null?(z.deletions=[A],z.flags|=16):B.push(A)}}function a(z,A){if(!e)return null;for(;A!==null;)t(z,A),A=A.sibling;return null}function l(z){for(var A=new Map;z!==null;)z.key!==null?A.set(z.key,z):A.set(z.index,z),z=z.sibling;return A}function i(z,A){return z=Kt(z,A),z.index=0,z.sibling=null,z}function o(z,A,B){return z.index=B,e?(B=z.alternate,B!==null?(B=B.index,B<A?(z.flags|=67108866,A):B):(z.flags|=67108866,A)):(z.flags|=1048576,A)}function h(z){return e&&z.alternate===null&&(z.flags|=67108866),z}function y(z,A,B,K){return A===null||A.tag!==6?(A=Er(B,z.mode,K),A.return=z,A):(A=i(A,B),A.return=z,A)}function j(z,A,B,K){var ie=B.type;return ie===C?Z(z,A,B.props.children,K,B.key):A!==null&&(A.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===F&&Gd(ie)===A.type)?(A=i(A,B.props),wn(A,B),A.return=z,A):(A=ws(B.type,B.key,B.props,null,z.mode,K),wn(A,B),A.return=z,A)}function L(z,A,B,K){return A===null||A.tag!==4||A.stateNode.containerInfo!==B.containerInfo||A.stateNode.implementation!==B.implementation?(A=_r(B,z.mode,K),A.return=z,A):(A=i(A,B.children||[]),A.return=z,A)}function Z(z,A,B,K,ie){return A===null||A.tag!==7?(A=Ga(B,z.mode,K,ie),A.return=z,A):(A=i(A,B),A.return=z,A)}function J(z,A,B){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=Er(""+A,z.mode,B),A.return=z,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case _:return B=ws(A.type,A.key,A.props,null,z.mode,B),wn(B,A),B.return=z,B;case U:return A=_r(A,z.mode,B),A.return=z,A;case F:var K=A._init;return A=K(A._payload),J(z,A,B)}if(ye(A)||ne(A))return A=Ga(A,z.mode,B,null),A.return=z,A;if(typeof A.then=="function")return J(z,Vs(A),B);if(A.$$typeof===T)return J(z,Os(z,A),B);Qs(z,A)}return null}function H(z,A,B,K){var ie=A!==null?A.key:null;if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return ie!==null?null:y(z,A,""+B,K);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case _:return B.key===ie?j(z,A,B,K):null;case U:return B.key===ie?L(z,A,B,K):null;case F:return ie=B._init,B=ie(B._payload),H(z,A,B,K)}if(ye(B)||ne(B))return ie!==null?null:Z(z,A,B,K,null);if(typeof B.then=="function")return H(z,A,Vs(B),K);if(B.$$typeof===T)return H(z,A,Os(z,B),K);Qs(z,B)}return null}function k(z,A,B,K,ie){if(typeof K=="string"&&K!==""||typeof K=="number"||typeof K=="bigint")return z=z.get(B)||null,y(A,z,""+K,ie);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case _:return z=z.get(K.key===null?B:K.key)||null,j(A,z,K,ie);case U:return z=z.get(K.key===null?B:K.key)||null,L(A,z,K,ie);case F:var be=K._init;return K=be(K._payload),k(z,A,B,K,ie)}if(ye(K)||ne(K))return z=z.get(B)||null,Z(A,z,K,ie,null);if(typeof K.then=="function")return k(z,A,B,Vs(K),ie);if(K.$$typeof===T)return k(z,A,B,Os(A,K),ie);Qs(A,K)}return null}function fe(z,A,B,K){for(var ie=null,be=null,oe=A,de=A=0,Je=null;oe!==null&&de<B.length;de++){oe.index>de?(Je=oe,oe=null):Je=oe.sibling;var Te=H(z,oe,B[de],K);if(Te===null){oe===null&&(oe=Je);break}e&&oe&&Te.alternate===null&&t(z,oe),A=o(Te,A,de),be===null?ie=Te:be.sibling=Te,be=Te,oe=Je}if(de===B.length)return a(z,oe),Ee&&Va(z,de),ie;if(oe===null){for(;de<B.length;de++)oe=J(z,B[de],K),oe!==null&&(A=o(oe,A,de),be===null?ie=oe:be.sibling=oe,be=oe);return Ee&&Va(z,de),ie}for(oe=l(oe);de<B.length;de++)Je=k(oe,z,de,B[de],K),Je!==null&&(e&&Je.alternate!==null&&oe.delete(Je.key===null?de:Je.key),A=o(Je,A,de),be===null?ie=Je:be.sibling=Je,be=Je);return e&&oe.forEach(function(Ua){return t(z,Ua)}),Ee&&Va(z,de),ie}function ue(z,A,B,K){if(B==null)throw Error(c(151));for(var ie=null,be=null,oe=A,de=A=0,Je=null,Te=B.next();oe!==null&&!Te.done;de++,Te=B.next()){oe.index>de?(Je=oe,oe=null):Je=oe.sibling;var Ua=H(z,oe,Te.value,K);if(Ua===null){oe===null&&(oe=Je);break}e&&oe&&Ua.alternate===null&&t(z,oe),A=o(Ua,A,de),be===null?ie=Ua:be.sibling=Ua,be=Ua,oe=Je}if(Te.done)return a(z,oe),Ee&&Va(z,de),ie;if(oe===null){for(;!Te.done;de++,Te=B.next())Te=J(z,Te.value,K),Te!==null&&(A=o(Te,A,de),be===null?ie=Te:be.sibling=Te,be=Te);return Ee&&Va(z,de),ie}for(oe=l(oe);!Te.done;de++,Te=B.next())Te=k(oe,z,de,Te.value,K),Te!==null&&(e&&Te.alternate!==null&&oe.delete(Te.key===null?de:Te.key),A=o(Te,A,de),be===null?ie=Te:be.sibling=Te,be=Te);return e&&oe.forEach(function(Ag){return t(z,Ag)}),Ee&&Va(z,de),ie}function Ae(z,A,B,K){if(typeof B=="object"&&B!==null&&B.type===C&&B.key===null&&(B=B.props.children),typeof B=="object"&&B!==null){switch(B.$$typeof){case _:e:{for(var ie=B.key;A!==null;){if(A.key===ie){if(ie=B.type,ie===C){if(A.tag===7){a(z,A.sibling),K=i(A,B.props.children),K.return=z,z=K;break e}}else if(A.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===F&&Gd(ie)===A.type){a(z,A.sibling),K=i(A,B.props),wn(K,B),K.return=z,z=K;break e}a(z,A);break}else t(z,A);A=A.sibling}B.type===C?(K=Ga(B.props.children,z.mode,K,B.key),K.return=z,z=K):(K=ws(B.type,B.key,B.props,null,z.mode,K),wn(K,B),K.return=z,z=K)}return h(z);case U:e:{for(ie=B.key;A!==null;){if(A.key===ie)if(A.tag===4&&A.stateNode.containerInfo===B.containerInfo&&A.stateNode.implementation===B.implementation){a(z,A.sibling),K=i(A,B.children||[]),K.return=z,z=K;break e}else{a(z,A);break}else t(z,A);A=A.sibling}K=_r(B,z.mode,K),K.return=z,z=K}return h(z);case F:return ie=B._init,B=ie(B._payload),Ae(z,A,B,K)}if(ye(B))return fe(z,A,B,K);if(ne(B)){if(ie=ne(B),typeof ie!="function")throw Error(c(150));return B=ie.call(B),ue(z,A,B,K)}if(typeof B.then=="function")return Ae(z,A,Vs(B),K);if(B.$$typeof===T)return Ae(z,A,Os(z,B),K);Qs(z,B)}return typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint"?(B=""+B,A!==null&&A.tag===6?(a(z,A.sibling),K=i(A,B),K.return=z,z=K):(a(z,A),K=Er(B,z.mode,K),K.return=z,z=K),h(z)):a(z,A)}return function(z,A,B,K){try{_n=0;var ie=Ae(z,A,B,K);return Al=null,ie}catch(oe){if(oe===gn||oe===Ms)throw oe;var be=yt(29,oe,null,z.mode);return be.lanes=K,be.return=z,be}finally{}}}var Ol=Xd(!0),Vd=Xd(!1),Rt=V(null),qt=null;function ba(e){var t=e.alternate;ae(Qe,Qe.current&1),ae(Rt,e),qt===null&&(t===null||_l.current!==null||t.memoizedState!==null)&&(qt=e)}function Qd(e){if(e.tag===22){if(ae(Qe,Qe.current),ae(Rt,e),qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(qt=e)}}else ja()}function ja(){ae(Qe,Qe.current),ae(Rt,Rt.current)}function ea(e){le(Rt),qt===e&&(qt=null),le(Qe)}var Qe=V(0);function Zs(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Jo(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function so(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var io={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=bt(),i=ga(l);i.payload=t,a!=null&&(i.callback=a),t=va(e,i,l),t!==null&&(jt(t,e,l),xn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=bt(),i=ga(l);i.tag=1,i.payload=t,a!=null&&(i.callback=a),t=va(e,i,l),t!==null&&(jt(t,e,l),xn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=bt(),l=ga(a);l.tag=2,t!=null&&(l.callback=t),t=va(e,l,a),t!==null&&(jt(t,e,a),xn(t,e,a))}};function Zd(e,t,a,l,i,o,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,o,h):t.prototype&&t.prototype.isPureReactComponent?!cn(a,l)||!cn(i,o):!0}function Fd(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&io.enqueueReplaceState(t,t.state,null)}function Wa(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var i in e)a[i]===void 0&&(a[i]=e[i])}return a}var Fs=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Kd(e){Fs(e)}function Jd(e){console.error(e)}function $d(e){Fs(e)}function Ks(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Wd(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function ro(e,t,a){return a=ga(a),a.tag=3,a.payload={element:null},a.callback=function(){Ks(e,t)},a}function Pd(e){return e=ga(e),e.tag=3,e}function Id(e,t,a,l){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var o=l.value;e.payload=function(){return i(o)},e.callback=function(){Wd(t,a,l)}}var h=a.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Wd(t,a,l),typeof i!="function"&&(wa===null?wa=new Set([this]):wa.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function Ay(e,t,a,l,i){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&mn(t,a,i,!0),a=Rt.current,a!==null){switch(a.tag){case 13:return qt===null?Do():a.alternate===null&&ke===0&&(ke=3),a.flags&=-257,a.flags|=65536,a.lanes=i,l===Br?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),zo(e,l,i)),!1;case 22:return a.flags|=65536,l===Br?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),zo(e,l,i)),!1}throw Error(c(435,a.tag))}return zo(e,l,i),Do(),!1}if(Ee)return t=Rt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==Rr&&(e=Error(c(422),{cause:l}),hn(Et(e,a)))):(l!==Rr&&(t=Error(c(423),{cause:l}),hn(Et(t,a))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Et(l,a),i=ro(e.stateNode,l,i),kr(e,i),ke!==4&&(ke=2)),!1;var o=Error(c(520),{cause:l});if(o=Et(o,a),zn===null?zn=[o]:zn.push(o),ke!==4&&(ke=2),t===null)return!0;l=Et(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=i&-i,a.lanes|=e,e=ro(a.stateNode,l,e),kr(a,e),!1;case 1:if(t=a.type,o=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(wa===null||!wa.has(o))))return a.flags|=65536,i&=-i,a.lanes|=i,i=Pd(i),Id(i,e,a,l),kr(a,i),!1}a=a.return}while(a!==null);return!1}var ef=Error(c(461)),Fe=!1;function $e(e,t,a,l){t.child=e===null?Vd(t,null,a,l):Ol(t,e.child,a,l)}function tf(e,t,a,l,i){a=a.render;var o=t.ref;if("ref"in l){var h={};for(var y in l)y!=="ref"&&(h[y]=l[y])}else h=l;return Ka(t),l=Vr(e,t,a,h,o,i),y=Qr(),e!==null&&!Fe?(Zr(e,t,i),ta(e,t,i)):(Ee&&y&&wr(t),t.flags|=1,$e(e,t,l,i),t.child)}function af(e,t,a,l,i){if(e===null){var o=a.type;return typeof o=="function"&&!Tr(o)&&o.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=o,lf(e,t,o,l,i)):(e=ws(a.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!yo(e,i)){var h=o.memoizedProps;if(a=a.compare,a=a!==null?a:cn,a(h,l)&&e.ref===t.ref)return ta(e,t,i)}return t.flags|=1,e=Kt(o,l),e.ref=t.ref,e.return=t,t.child=e}function lf(e,t,a,l,i){if(e!==null){var o=e.memoizedProps;if(cn(o,l)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=l=o,yo(e,i))(e.flags&131072)!==0&&(Fe=!0);else return t.lanes=e.lanes,ta(e,t,i)}return oo(e,t,a,l,i)}function nf(e,t,a){var l=t.pendingProps,i=l.children,o=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=o!==null?o.baseLanes|a:a,e!==null){for(i=t.child=e.child,o=0;i!==null;)o=o|i.lanes|i.childLanes,i=i.sibling;t.childLanes=o&~l}else t.childLanes=0,t.child=null;return sf(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ds(t,o!==null?o.cachePool:null),o!==null?ld(t,o):Yr(),Qd(t);else return t.lanes=t.childLanes=536870912,sf(e,t,o!==null?o.baseLanes|a:a,a)}else o!==null?(Ds(t,o.cachePool),ld(t,o),ja(),t.memoizedState=null):(e!==null&&Ds(t,null),Yr(),ja());return $e(e,t,i,a),t.child}function sf(e,t,a,l){var i=Ur();return i=i===null?null:{parent:Ve._currentValue,pool:i},t.memoizedState={baseLanes:a,cachePool:i},e!==null&&Ds(t,null),Yr(),Qd(t),e!==null&&mn(e,t,l,!0),null}function Js(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function oo(e,t,a,l,i){return Ka(t),a=Vr(e,t,a,l,void 0,i),l=Qr(),e!==null&&!Fe?(Zr(e,t,i),ta(e,t,i)):(Ee&&l&&wr(t),t.flags|=1,$e(e,t,a,i),t.child)}function rf(e,t,a,l,i,o){return Ka(t),t.updateQueue=null,a=sd(t,l,a,i),nd(e),l=Qr(),e!==null&&!Fe?(Zr(e,t,o),ta(e,t,o)):(Ee&&l&&wr(t),t.flags|=1,$e(e,t,a,o),t.child)}function of(e,t,a,l,i){if(Ka(t),t.stateNode===null){var o=jl,h=a.contextType;typeof h=="object"&&h!==null&&(o=et(h)),o=new a(l,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=io,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=l,o.state=t.memoizedState,o.refs={},Lr(t),h=a.contextType,o.context=typeof h=="object"&&h!==null?et(h):jl,o.state=t.memoizedState,h=a.getDerivedStateFromProps,typeof h=="function"&&(so(t,a,h,l),o.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&io.enqueueReplaceState(o,o.state,null),jn(t,l,o,i),bn(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){o=t.stateNode;var y=t.memoizedProps,j=Wa(a,y);o.props=j;var L=o.context,Z=a.contextType;h=jl,typeof Z=="object"&&Z!==null&&(h=et(Z));var J=a.getDerivedStateFromProps;Z=typeof J=="function"||typeof o.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,Z||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(y||L!==h)&&Fd(t,o,l,h),ya=!1;var H=t.memoizedState;o.state=H,jn(t,l,o,i),bn(),L=t.memoizedState,y||H!==L||ya?(typeof J=="function"&&(so(t,a,J,l),L=t.memoizedState),(j=ya||Zd(t,a,j,l,H,L,h))?(Z||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=L),o.props=l,o.state=L,o.context=h,l=j):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{o=t.stateNode,Hr(e,t),h=t.memoizedProps,Z=Wa(a,h),o.props=Z,J=t.pendingProps,H=o.context,L=a.contextType,j=jl,typeof L=="object"&&L!==null&&(j=et(L)),y=a.getDerivedStateFromProps,(L=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==J||H!==j)&&Fd(t,o,l,j),ya=!1,H=t.memoizedState,o.state=H,jn(t,l,o,i),bn();var k=t.memoizedState;h!==J||H!==k||ya||e!==null&&e.dependencies!==null&&As(e.dependencies)?(typeof y=="function"&&(so(t,a,y,l),k=t.memoizedState),(Z=ya||Zd(t,a,Z,l,H,k,j)||e!==null&&e.dependencies!==null&&As(e.dependencies))?(L||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(l,k,j),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(l,k,j)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=k),o.props=l,o.state=k,o.context=j,l=Z):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),l=!1)}return o=l,Js(e,t),l=(t.flags&128)!==0,o||l?(o=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&l?(t.child=Ol(t,e.child,null,i),t.child=Ol(t,null,a,i)):$e(e,t,a,i),t.memoizedState=o.state,e=t.child):e=ta(e,t,i),e}function cf(e,t,a,l){return fn(),t.flags|=256,$e(e,t,a,l),t.child}var co={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function uo(e){return{baseLanes:e,cachePool:Ju()}}function fo(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=At),e}function uf(e,t,a){var l=t.pendingProps,i=!1,o=(t.flags&128)!==0,h;if((h=o)||(h=e!==null&&e.memoizedState===null?!1:(Qe.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ee){if(i?ba(t):ja(),Ee){var y=He,j;if(j=y){e:{for(j=y,y=kt;j.nodeType!==8;){if(!y){y=null;break e}if(j=zt(j.nextSibling),j===null){y=null;break e}}y=j}y!==null?(t.memoizedState={dehydrated:y,treeContext:Xa!==null?{id:Jt,overflow:$t}:null,retryLane:536870912,hydrationErrors:null},j=yt(18,null,null,0),j.stateNode=y,j.return=t,t.child=j,lt=t,He=null,j=!0):j=!1}j||Za(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Jo(y)?t.lanes=32:t.lanes=536870912,null;ea(t)}return y=l.children,l=l.fallback,i?(ja(),i=t.mode,y=$s({mode:"hidden",children:y},i),l=Ga(l,i,a,null),y.return=t,l.return=t,y.sibling=l,t.child=y,i=t.child,i.memoizedState=uo(a),i.childLanes=fo(e,h,a),t.memoizedState=co,l):(ba(t),ho(t,y))}if(j=e.memoizedState,j!==null&&(y=j.dehydrated,y!==null)){if(o)t.flags&256?(ba(t),t.flags&=-257,t=mo(e,t,a)):t.memoizedState!==null?(ja(),t.child=e.child,t.flags|=128,t=null):(ja(),i=l.fallback,y=t.mode,l=$s({mode:"visible",children:l.children},y),i=Ga(i,y,a,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,Ol(t,e.child,null,a),l=t.child,l.memoizedState=uo(a),l.childLanes=fo(e,h,a),t.memoizedState=co,t=i);else if(ba(t),Jo(y)){if(h=y.nextSibling&&y.nextSibling.dataset,h)var L=h.dgst;h=L,l=Error(c(419)),l.stack="",l.digest=h,hn({value:l,source:null,stack:null}),t=mo(e,t,a)}else if(Fe||mn(e,t,a,!1),h=(a&e.childLanes)!==0,Fe||h){if(h=De,h!==null&&(l=a&-a,l=(l&42)!==0?1:Ji(l),l=(l&(h.suspendedLanes|a))!==0?0:l,l!==0&&l!==j.retryLane))throw j.retryLane=l,bl(e,l),jt(h,e,l),ef;y.data==="$?"||Do(),t=mo(e,t,a)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,He=zt(y.nextSibling),lt=t,Ee=!0,Qa=null,kt=!1,e!==null&&(wt[Ct++]=Jt,wt[Ct++]=$t,wt[Ct++]=Xa,Jt=e.id,$t=e.overflow,Xa=t),t=ho(t,l.children),t.flags|=4096);return t}return i?(ja(),i=l.fallback,y=t.mode,j=e.child,L=j.sibling,l=Kt(j,{mode:"hidden",children:l.children}),l.subtreeFlags=j.subtreeFlags&65011712,L!==null?i=Kt(L,i):(i=Ga(i,y,a,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,y=e.child.memoizedState,y===null?y=uo(a):(j=y.cachePool,j!==null?(L=Ve._currentValue,j=j.parent!==L?{parent:L,pool:L}:j):j=Ju(),y={baseLanes:y.baseLanes|a,cachePool:j}),i.memoizedState=y,i.childLanes=fo(e,h,a),t.memoizedState=co,l):(ba(t),a=e.child,e=a.sibling,a=Kt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=a,t.memoizedState=null,a)}function ho(e,t){return t=$s({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function $s(e,t){return e=yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function mo(e,t,a){return Ol(t,e.child,null,a),e=ho(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function df(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Or(e.return,t,a)}function po(e,t,a,l,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=l,o.tail=a,o.tailMode=i)}function ff(e,t,a){var l=t.pendingProps,i=l.revealOrder,o=l.tail;if($e(e,t,l.children,a),l=Qe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&df(e,a,t);else if(e.tag===19)df(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(ae(Qe,l),i){case"forwards":for(a=t.child,i=null;a!==null;)e=a.alternate,e!==null&&Zs(e)===null&&(i=a),a=a.sibling;a=i,a===null?(i=t.child,t.child=null):(i=a.sibling,a.sibling=null),po(t,!1,i,a,o);break;case"backwards":for(a=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Zs(e)===null){t.child=i;break}e=i.sibling,i.sibling=a,a=i,i=e}po(t,!0,a,null,o);break;case"together":po(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ta(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),_a|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(mn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=Kt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Kt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function yo(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&As(e)))}function Oy(e,t,a){switch(t.tag){case 3:ze(t,t.stateNode.containerInfo),pa(t,Ve,e.memoizedState.cache),fn();break;case 27:case 5:Vi(t);break;case 4:ze(t,t.stateNode.containerInfo);break;case 10:pa(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(ba(t),t.flags|=128,null):(a&t.child.childLanes)!==0?uf(e,t,a):(ba(t),e=ta(e,t,a),e!==null?e.sibling:null);ba(t);break;case 19:var i=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(mn(e,t,a,!1),l=(a&t.childLanes)!==0),i){if(l)return ff(e,t,a);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),ae(Qe,Qe.current),l)break;return null;case 22:case 23:return t.lanes=0,nf(e,t,a);case 24:pa(t,Ve,e.memoizedState.cache)}return ta(e,t,a)}function hf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Fe=!0;else{if(!yo(e,a)&&(t.flags&128)===0)return Fe=!1,Oy(e,t,a);Fe=(e.flags&131072)!==0}else Fe=!1,Ee&&(t.flags&1048576)!==0&&Gu(t,Rs,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")Tr(l)?(e=Wa(l,e),t.tag=1,t=of(null,t,l,e,a)):(t.tag=0,t=oo(null,t,l,e,a));else{if(l!=null){if(i=l.$$typeof,i===N){t.tag=11,t=tf(null,t,l,e,a);break e}else if(i===P){t.tag=14,t=af(null,t,l,e,a);break e}}throw t=re(l)||l,Error(c(306,t,""))}}return t;case 0:return oo(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,i=Wa(l,t.pendingProps),of(e,t,l,i,a);case 3:e:{if(ze(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var o=t.memoizedState;i=o.element,Hr(e,t),jn(t,l,null,a);var h=t.memoizedState;if(l=h.cache,pa(t,Ve,l),l!==o.cache&&Dr(t,[Ve],a,!0),bn(),l=h.element,o.isDehydrated)if(o={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=cf(e,t,l,a);break e}else if(l!==i){i=Et(Error(c(424)),t),hn(i),t=cf(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(He=zt(e.firstChild),lt=t,Ee=!0,Qa=null,kt=!0,a=Vd(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(fn(),l===i){t=ta(e,t,a);break e}$e(e,t,l,a)}t=t.child}return t;case 26:return Js(e,t),e===null?(a=gh(t.type,null,t.pendingProps,null))?t.memoizedState=a:Ee||(a=t.type,e=t.pendingProps,l=ui(he.current).createElement(a),l[Ie]=t,l[st]=e,Pe(l,a,e),Ze(l),t.stateNode=l):t.memoizedState=gh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Vi(t),e===null&&Ee&&(l=t.stateNode=mh(t.type,t.pendingProps,he.current),lt=t,kt=!0,i=He,Aa(t.type)?($o=i,He=zt(l.firstChild)):He=i),$e(e,t,t.pendingProps.children,a),Js(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ee&&((i=l=He)&&(l=sg(l,t.type,t.pendingProps,kt),l!==null?(t.stateNode=l,lt=t,He=zt(l.firstChild),kt=!1,i=!0):i=!1),i||Za(t)),Vi(t),i=t.type,o=t.pendingProps,h=e!==null?e.memoizedProps:null,l=o.children,Zo(i,o)?l=null:h!==null&&Zo(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=Vr(e,t,Ny,null,null,a),Xn._currentValue=i),Js(e,t),$e(e,t,l,a),t.child;case 6:return e===null&&Ee&&((e=a=He)&&(a=ig(a,t.pendingProps,kt),a!==null?(t.stateNode=a,lt=t,He=null,e=!0):e=!1),e||Za(t)),null;case 13:return uf(e,t,a);case 4:return ze(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Ol(t,null,l,a):$e(e,t,l,a),t.child;case 11:return tf(e,t,t.type,t.pendingProps,a);case 7:return $e(e,t,t.pendingProps,a),t.child;case 8:return $e(e,t,t.pendingProps.children,a),t.child;case 12:return $e(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,pa(t,t.type,l.value),$e(e,t,l.children,a),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,Ka(t),i=et(i),l=l(i),t.flags|=1,$e(e,t,l,a),t.child;case 14:return af(e,t,t.type,t.pendingProps,a);case 15:return lf(e,t,t.type,t.pendingProps,a);case 19:return ff(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=$s(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Kt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return nf(e,t,a);case 24:return Ka(t),l=et(Ve),e===null?(i=Ur(),i===null&&(i=De,o=Mr(),i.pooledCache=o,o.refCount++,o!==null&&(i.pooledCacheLanes|=a),i=o),t.memoizedState={parent:l,cache:i},Lr(t),pa(t,Ve,i)):((e.lanes&a)!==0&&(Hr(e,t),jn(t,null,null,a),bn()),i=e.memoizedState,o=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),pa(t,Ve,l)):(l=o.cache,pa(t,Ve,l),l!==i.cache&&Dr(t,[Ve],a,!0))),$e(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function aa(e){e.flags|=4}function mf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Sh(t)){if(t=Rt.current,t!==null&&((Ne&4194048)===Ne?qt!==null:(Ne&62914560)!==Ne&&(Ne&536870912)===0||t!==qt))throw vn=Br,$u;e.flags|=8192}}function Ws(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Qc():536870912,e.lanes|=t,Ul|=t)}function Cn(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Dy(e,t,a){var l=t.pendingProps;switch(Cr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Le(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Pt(Ve),da(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(dn(t)?aa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Qu())),Le(t),null;case 26:return a=t.memoizedState,e===null?(aa(t),a!==null?(Le(t),mf(t,a)):(Le(t),t.flags&=-16777217)):a?a!==e.memoizedState?(aa(t),Le(t),mf(t,a)):(Le(t),t.flags&=-16777217):(e.memoizedProps!==l&&aa(t),Le(t),t.flags&=-16777217),null;case 27:os(t),a=he.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Le(t),null}e=I.current,dn(t)?Xu(t):(e=mh(i,l,a),t.stateNode=e,aa(t))}return Le(t),null;case 5:if(os(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Le(t),null}if(e=I.current,dn(t))Xu(t);else{switch(i=ui(he.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(a,{is:l.is}):i.createElement(a)}}e[Ie]=t,e[st]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(Pe(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&aa(t)}}return Le(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=he.current,dn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,i=lt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[Ie]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||rh(e.nodeValue,a)),e||Za(t)}else e=ui(e).createTextNode(l),e[Ie]=t,t.stateNode=e}return Le(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=dn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[Ie]=t}else fn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Le(t),i=!1}else i=Qu(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(ea(t),t):(ea(t),null)}if(ea(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var o=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(o=l.memoizedState.cachePool.pool),o!==i&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Ws(t,t.updateQueue),Le(t),null;case 4:return da(),e===null&&Yo(t.stateNode.containerInfo),Le(t),null;case 10:return Pt(t.type),Le(t),null;case 19:if(le(Qe),i=t.memoizedState,i===null)return Le(t),null;if(l=(t.flags&128)!==0,o=i.rendering,o===null)if(l)Cn(i,!1);else{if(ke!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=Zs(e),o!==null){for(t.flags|=128,Cn(i,!1),e=o.updateQueue,t.updateQueue=e,Ws(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Yu(a,e),a=a.sibling;return ae(Qe,Qe.current&1|2),t.child}e=e.sibling}i.tail!==null&&Ht()>ei&&(t.flags|=128,l=!0,Cn(i,!1),t.lanes=4194304)}else{if(!l)if(e=Zs(o),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Ws(t,e),Cn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!Ee)return Le(t),null}else 2*Ht()-i.renderingStartTime>ei&&a!==536870912&&(t.flags|=128,l=!0,Cn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(e=i.last,e!==null?e.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ht(),t.sibling=null,e=Qe.current,ae(Qe,l?e&1|2:e&1),t):(Le(t),null);case 22:case 23:return ea(t),Gr(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),a=t.updateQueue,a!==null&&Ws(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&le(Ja),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Pt(Ve),Le(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function My(e,t){switch(Cr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Pt(Ve),da(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return os(t),null;case 13:if(ea(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));fn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(Qe),null;case 4:return da(),null;case 10:return Pt(t.type),null;case 22:case 23:return ea(t),Gr(),e!==null&&le(Ja),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Pt(Ve),null;case 25:return null;default:return null}}function pf(e,t){switch(Cr(t),t.tag){case 3:Pt(Ve),da();break;case 26:case 27:case 5:os(t);break;case 4:da();break;case 13:ea(t);break;case 19:le(Qe);break;case 10:Pt(t.type);break;case 22:case 23:ea(t),Gr(),e!==null&&le(Ja);break;case 24:Pt(Ve)}}function Rn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var i=l.next;a=i;do{if((a.tag&e)===e){l=void 0;var o=a.create,h=a.inst;l=o(),h.destroy=l}a=a.next}while(a!==i)}}catch(y){Oe(t,t.return,y)}}function Sa(e,t,a){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var o=i.next;l=o;do{if((l.tag&e)===e){var h=l.inst,y=h.destroy;if(y!==void 0){h.destroy=void 0,i=t;var j=a,L=y;try{L()}catch(Z){Oe(i,j,Z)}}}l=l.next}while(l!==o)}}catch(Z){Oe(t,t.return,Z)}}function yf(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{ad(t,a)}catch(l){Oe(e,e.return,l)}}}function gf(e,t,a){a.props=Wa(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Oe(e,t,l)}}function An(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(i){Oe(e,t,i)}}function Yt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(i){Oe(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){Oe(e,t,i)}else a.current=null}function vf(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(i){Oe(e,e.return,i)}}function go(e,t,a){try{var l=e.stateNode;eg(l,e.type,a,t),l[st]=t}catch(i){Oe(e,e.return,i)}}function xf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Aa(e.type)||e.tag===4}function vo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||xf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Aa(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function xo(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=ci));else if(l!==4&&(l===27&&Aa(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(xo(e,t,a),e=e.sibling;e!==null;)xo(e,t,a),e=e.sibling}function Ps(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Aa(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ps(e,t,a),e=e.sibling;e!==null;)Ps(e,t,a),e=e.sibling}function bf(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Pe(t,l,a),t[Ie]=e,t[st]=a}catch(o){Oe(e,e.return,o)}}var la=!1,Ye=!1,bo=!1,jf=typeof WeakSet=="function"?WeakSet:Set,Ke=null;function zy(e,t){if(e=e.containerInfo,Vo=yi,e=Ou(e),gr(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var i=l.anchorOffset,o=l.focusNode;l=l.focusOffset;try{a.nodeType,o.nodeType}catch{a=null;break e}var h=0,y=-1,j=-1,L=0,Z=0,J=e,H=null;t:for(;;){for(var k;J!==a||i!==0&&J.nodeType!==3||(y=h+i),J!==o||l!==0&&J.nodeType!==3||(j=h+l),J.nodeType===3&&(h+=J.nodeValue.length),(k=J.firstChild)!==null;)H=J,J=k;for(;;){if(J===e)break t;if(H===a&&++L===i&&(y=h),H===o&&++Z===l&&(j=h),(k=J.nextSibling)!==null)break;J=H,H=J.parentNode}J=k}a=y===-1||j===-1?null:{start:y,end:j}}else a=null}a=a||{start:0,end:0}}else a=null;for(Qo={focusedElem:e,selectionRange:a},yi=!1,Ke=t;Ke!==null;)if(t=Ke,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ke=e;else for(;Ke!==null;){switch(t=Ke,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,a=t,i=o.memoizedProps,o=o.memoizedState,l=a.stateNode;try{var fe=Wa(a.type,i,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(fe,o),l.__reactInternalSnapshotBeforeUpdate=e}catch(ue){Oe(a,a.return,ue)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Ko(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Ko(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Ke=e;break}Ke=t.return}}function Sf(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Na(e,a),l&4&&Rn(5,a);break;case 1:if(Na(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(h){Oe(a,a.return,h)}else{var i=Wa(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Oe(a,a.return,h)}}l&64&&yf(a),l&512&&An(a,a.return);break;case 3:if(Na(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{ad(e,t)}catch(h){Oe(a,a.return,h)}}break;case 27:t===null&&l&4&&bf(a);case 26:case 5:Na(e,a),t===null&&l&4&&vf(a),l&512&&An(a,a.return);break;case 12:Na(e,a);break;case 13:Na(e,a),l&4&&Ef(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Xy.bind(null,a),rg(e,a))));break;case 22:if(l=a.memoizedState!==null||la,!l){t=t!==null&&t.memoizedState!==null||Ye,i=la;var o=Ye;la=l,(Ye=t)&&!o?Ta(e,a,(a.subtreeFlags&8772)!==0):Na(e,a),la=i,Ye=o}break;case 30:break;default:Na(e,a)}}function Nf(e){var t=e.alternate;t!==null&&(e.alternate=null,Nf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Pi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ue=null,ot=!1;function na(e,t,a){for(a=a.child;a!==null;)Tf(e,t,a),a=a.sibling}function Tf(e,t,a){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount($l,a)}catch{}switch(a.tag){case 26:Ye||Yt(a,t),na(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ye||Yt(a,t);var l=Ue,i=ot;Aa(a.type)&&(Ue=a.stateNode,ot=!1),na(e,t,a),kn(a.stateNode),Ue=l,ot=i;break;case 5:Ye||Yt(a,t);case 6:if(l=Ue,i=ot,Ue=null,na(e,t,a),Ue=l,ot=i,Ue!==null)if(ot)try{(Ue.nodeType===9?Ue.body:Ue.nodeName==="HTML"?Ue.ownerDocument.body:Ue).removeChild(a.stateNode)}catch(o){Oe(a,t,o)}else try{Ue.removeChild(a.stateNode)}catch(o){Oe(a,t,o)}break;case 18:Ue!==null&&(ot?(e=Ue,fh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Fn(e)):fh(Ue,a.stateNode));break;case 4:l=Ue,i=ot,Ue=a.stateNode.containerInfo,ot=!0,na(e,t,a),Ue=l,ot=i;break;case 0:case 11:case 14:case 15:Ye||Sa(2,a,t),Ye||Sa(4,a,t),na(e,t,a);break;case 1:Ye||(Yt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&gf(a,t,l)),na(e,t,a);break;case 21:na(e,t,a);break;case 22:Ye=(l=Ye)||a.memoizedState!==null,na(e,t,a),Ye=l;break;default:na(e,t,a)}}function Ef(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Fn(e)}catch(a){Oe(t,t.return,a)}}function Uy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new jf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new jf),t;default:throw Error(c(435,e.tag))}}function jo(e,t){var a=Uy(e);t.forEach(function(l){var i=Vy.bind(null,e,l);a.has(l)||(a.add(l),l.then(i,i))})}function gt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var i=a[l],o=e,h=t,y=h;e:for(;y!==null;){switch(y.tag){case 27:if(Aa(y.type)){Ue=y.stateNode,ot=!1;break e}break;case 5:Ue=y.stateNode,ot=!1;break e;case 3:case 4:Ue=y.stateNode.containerInfo,ot=!0;break e}y=y.return}if(Ue===null)throw Error(c(160));Tf(o,h,i),Ue=null,ot=!1,o=i.alternate,o!==null&&(o.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)_f(t,e),t=t.sibling}var Mt=null;function _f(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:gt(t,e),vt(e),l&4&&(Sa(3,e,e.return),Rn(3,e),Sa(5,e,e.return));break;case 1:gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),l&64&&la&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var i=Mt;if(gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),l&4){var o=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":o=i.getElementsByTagName("title")[0],(!o||o[Il]||o[Ie]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=i.createElement(l),i.head.insertBefore(o,i.querySelector("head > title"))),Pe(o,l,a),o[Ie]=e,Ze(o),l=o;break e;case"link":var h=bh("link","href",i).get(l+(a.href||""));if(h){for(var y=0;y<h.length;y++)if(o=h[y],o.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&o.getAttribute("rel")===(a.rel==null?null:a.rel)&&o.getAttribute("title")===(a.title==null?null:a.title)&&o.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){h.splice(y,1);break t}}o=i.createElement(l),Pe(o,l,a),i.head.appendChild(o);break;case"meta":if(h=bh("meta","content",i).get(l+(a.content||""))){for(y=0;y<h.length;y++)if(o=h[y],o.getAttribute("content")===(a.content==null?null:""+a.content)&&o.getAttribute("name")===(a.name==null?null:a.name)&&o.getAttribute("property")===(a.property==null?null:a.property)&&o.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&o.getAttribute("charset")===(a.charSet==null?null:a.charSet)){h.splice(y,1);break t}}o=i.createElement(l),Pe(o,l,a),i.head.appendChild(o);break;default:throw Error(c(468,l))}o[Ie]=e,Ze(o),l=o}e.stateNode=l}else jh(i,e.type,e.stateNode);else e.stateNode=xh(i,l,e.memoizedProps);else o!==l?(o===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):o.count--,l===null?jh(i,e.type,e.stateNode):xh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&go(e,e.memoizedProps,a.memoizedProps)}break;case 27:gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),a!==null&&l&4&&go(e,e.memoizedProps,a.memoizedProps);break;case 5:if(gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),e.flags&32){i=e.stateNode;try{hl(i,"")}catch(k){Oe(e,e.return,k)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,go(e,i,a!==null?a.memoizedProps:i)),l&1024&&(bo=!0);break;case 6:if(gt(t,e),vt(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(k){Oe(e,e.return,k)}}break;case 3:if(hi=null,i=Mt,Mt=di(t.containerInfo),gt(t,e),Mt=i,vt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Fn(t.containerInfo)}catch(k){Oe(e,e.return,k)}bo&&(bo=!1,wf(e));break;case 4:l=Mt,Mt=di(e.stateNode.containerInfo),gt(t,e),vt(e),Mt=l;break;case 12:gt(t,e),vt(e);break;case 13:gt(t,e),vt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(wo=Ht()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,jo(e,l)));break;case 22:i=e.memoizedState!==null;var j=a!==null&&a.memoizedState!==null,L=la,Z=Ye;if(la=L||i,Ye=Z||j,gt(t,e),Ye=Z,la=L,vt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(a===null||j||la||Ye||Pa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){j=a=t;try{if(o=j.stateNode,i)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{y=j.stateNode;var J=j.memoizedProps.style,H=J!=null&&J.hasOwnProperty("display")?J.display:null;y.style.display=H==null||typeof H=="boolean"?"":(""+H).trim()}}catch(k){Oe(j,j.return,k)}}}else if(t.tag===6){if(a===null){j=t;try{j.stateNode.nodeValue=i?"":j.memoizedProps}catch(k){Oe(j,j.return,k)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,jo(e,a))));break;case 19:gt(t,e),vt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,jo(e,l)));break;case 30:break;case 21:break;default:gt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(xf(l)){a=l;break}l=l.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var i=a.stateNode,o=vo(e);Ps(e,o,i);break;case 5:var h=a.stateNode;a.flags&32&&(hl(h,""),a.flags&=-33);var y=vo(e);Ps(e,y,h);break;case 3:case 4:var j=a.stateNode.containerInfo,L=vo(e);xo(e,L,j);break;default:throw Error(c(161))}}catch(Z){Oe(e,e.return,Z)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;wf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Na(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Sf(e,t.alternate,t),t=t.sibling}function Pa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Sa(4,t,t.return),Pa(t);break;case 1:Yt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&gf(t,t.return,a),Pa(t);break;case 27:kn(t.stateNode);case 26:case 5:Yt(t,t.return),Pa(t);break;case 22:t.memoizedState===null&&Pa(t);break;case 30:Pa(t);break;default:Pa(t)}e=e.sibling}}function Ta(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,o=t,h=o.flags;switch(o.tag){case 0:case 11:case 15:Ta(i,o,a),Rn(4,o);break;case 1:if(Ta(i,o,a),l=o,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(L){Oe(l,l.return,L)}if(l=o,i=l.updateQueue,i!==null){var y=l.stateNode;try{var j=i.shared.hiddenCallbacks;if(j!==null)for(i.shared.hiddenCallbacks=null,i=0;i<j.length;i++)td(j[i],y)}catch(L){Oe(l,l.return,L)}}a&&h&64&&yf(o),An(o,o.return);break;case 27:bf(o);case 26:case 5:Ta(i,o,a),a&&l===null&&h&4&&vf(o),An(o,o.return);break;case 12:Ta(i,o,a);break;case 13:Ta(i,o,a),a&&h&4&&Ef(i,o);break;case 22:o.memoizedState===null&&Ta(i,o,a),An(o,o.return);break;case 30:break;default:Ta(i,o,a)}t=t.sibling}}function So(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&pn(a))}function No(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pn(e))}function Gt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Cf(e,t,a,l),t=t.sibling}function Cf(e,t,a,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Gt(e,t,a,l),i&2048&&Rn(9,t);break;case 1:Gt(e,t,a,l);break;case 3:Gt(e,t,a,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pn(e)));break;case 12:if(i&2048){Gt(e,t,a,l),e=t.stateNode;try{var o=t.memoizedProps,h=o.id,y=o.onPostCommit;typeof y=="function"&&y(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Oe(t,t.return,j)}}else Gt(e,t,a,l);break;case 13:Gt(e,t,a,l);break;case 23:break;case 22:o=t.stateNode,h=t.alternate,t.memoizedState!==null?o._visibility&2?Gt(e,t,a,l):On(e,t):o._visibility&2?Gt(e,t,a,l):(o._visibility|=2,Dl(e,t,a,l,(t.subtreeFlags&10256)!==0)),i&2048&&So(h,t);break;case 24:Gt(e,t,a,l),i&2048&&No(t.alternate,t);break;default:Gt(e,t,a,l)}}function Dl(e,t,a,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,h=t,y=a,j=l,L=h.flags;switch(h.tag){case 0:case 11:case 15:Dl(o,h,y,j,i),Rn(8,h);break;case 23:break;case 22:var Z=h.stateNode;h.memoizedState!==null?Z._visibility&2?Dl(o,h,y,j,i):On(o,h):(Z._visibility|=2,Dl(o,h,y,j,i)),i&&L&2048&&So(h.alternate,h);break;case 24:Dl(o,h,y,j,i),i&&L&2048&&No(h.alternate,h);break;default:Dl(o,h,y,j,i)}t=t.sibling}}function On(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,i=l.flags;switch(l.tag){case 22:On(a,l),i&2048&&So(l.alternate,l);break;case 24:On(a,l),i&2048&&No(l.alternate,l);break;default:On(a,l)}t=t.sibling}}var Dn=8192;function Ml(e){if(e.subtreeFlags&Dn)for(e=e.child;e!==null;)Rf(e),e=e.sibling}function Rf(e){switch(e.tag){case 26:Ml(e),e.flags&Dn&&e.memoizedState!==null&&bg(Mt,e.memoizedState,e.memoizedProps);break;case 5:Ml(e);break;case 3:case 4:var t=Mt;Mt=di(e.stateNode.containerInfo),Ml(e),Mt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Dn,Dn=16777216,Ml(e),Dn=t):Ml(e));break;default:Ml(e)}}function Af(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Mn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Df(l,e)}Af(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Of(e),e=e.sibling}function Of(e){switch(e.tag){case 0:case 11:case 15:Mn(e),e.flags&2048&&Sa(9,e,e.return);break;case 3:Mn(e);break;case 12:Mn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Is(e)):Mn(e);break;default:Mn(e)}}function Is(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Df(l,e)}Af(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Sa(8,t,t.return),Is(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Is(t));break;default:Is(t)}e=e.sibling}}function Df(e,t){for(;Ke!==null;){var a=Ke;switch(a.tag){case 0:case 11:case 15:Sa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:pn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ke=l;else e:for(a=e;Ke!==null;){l=Ke;var i=l.sibling,o=l.return;if(Nf(l),l===a){Ke=null;break e}if(i!==null){i.return=o,Ke=i;break e}Ke=o}}}var By={getCacheForType:function(e){var t=et(Ve),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Ly=typeof WeakMap=="function"?WeakMap:Map,_e=0,De=null,je=null,Ne=0,we=0,xt=null,Ea=!1,zl=!1,To=!1,sa=0,ke=0,_a=0,Ia=0,Eo=0,At=0,Ul=0,zn=null,ct=null,_o=!1,wo=0,ei=1/0,ti=null,wa=null,We=0,Ca=null,Bl=null,Ll=0,Co=0,Ro=null,Mf=null,Un=0,Ao=null;function bt(){if((_e&2)!==0&&Ne!==0)return Ne&-Ne;if(X.T!==null){var e=Tl;return e!==0?e:Lo()}return Kc()}function zf(){At===0&&(At=(Ne&536870912)===0||Ee?Vc():536870912);var e=Rt.current;return e!==null&&(e.flags|=32),At}function jt(e,t,a){(e===De&&(we===2||we===9)||e.cancelPendingCommit!==null)&&(Hl(e,0),Ra(e,Ne,At,!1)),Pl(e,a),((_e&2)===0||e!==De)&&(e===De&&((_e&2)===0&&(Ia|=a),ke===4&&Ra(e,Ne,At,!1)),Xt(e))}function Uf(e,t,a){if((_e&6)!==0)throw Error(c(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Wl(e,t),i=l?qy(e,t):Mo(e,t,!0),o=l;do{if(i===0){zl&&!l&&Ra(e,t,0,!1);break}else{if(a=e.current.alternate,o&&!Hy(a)){i=Mo(e,t,!1),o=!1;continue}if(i===2){if(o=t,e.errorRecoveryDisabledLanes&o)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var y=e;i=zn;var j=y.current.memoizedState.isDehydrated;if(j&&(Hl(y,h).flags|=256),h=Mo(y,h,!1),h!==2){if(To&&!j){y.errorRecoveryDisabledLanes|=o,Ia|=o,i=4;break e}o=ct,ct=i,o!==null&&(ct===null?ct=o:ct.push.apply(ct,o))}i=h}if(o=!1,i!==2)continue}}if(i===1){Hl(e,0),Ra(e,t,0,!0);break}e:{switch(l=e,o=i,o){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Ra(l,t,At,!Ea);break e;case 2:ct=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=wo+300-Ht(),10<i)){if(Ra(l,t,At,!Ea),fs(l,0,!0)!==0)break e;l.timeoutHandle=uh(Bf.bind(null,l,a,ct,ti,_o,t,At,Ia,Ul,Ea,o,2,-0,0),i);break e}Bf(l,a,ct,ti,_o,t,At,Ia,Ul,Ea,o,0,-0,0)}}break}while(!0);Xt(e)}function Bf(e,t,a,l,i,o,h,y,j,L,Z,J,H,k){if(e.timeoutHandle=-1,J=t.subtreeFlags,(J&8192||(J&16785408)===16785408)&&(Gn={stylesheets:null,count:0,unsuspend:xg},Rf(t),J=jg(),J!==null)){e.cancelPendingCommit=J(Xf.bind(null,e,t,o,a,l,i,h,y,j,Z,1,H,k)),Ra(e,o,h,!L);return}Xf(e,t,o,a,l,i,h,y,j)}function Hy(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var i=a[l],o=i.getSnapshot;i=i.value;try{if(!pt(o(),i))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ra(e,t,a,l){t&=~Eo,t&=~Ia,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var o=31-mt(i),h=1<<o;l[o]=-1,i&=~h}a!==0&&Zc(e,a,t)}function ai(){return(_e&6)===0?(Bn(0),!1):!0}function Oo(){if(je!==null){if(we===0)var e=je.return;else e=je,Wt=Fa=null,Fr(e),Al=null,_n=0,e=je;for(;e!==null;)pf(e.alternate,e),e=e.return;je=null}}function Hl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,ag(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Oo(),De=e,je=a=Kt(e.current,null),Ne=t,we=0,xt=null,Ea=!1,zl=Wl(e,t),To=!1,Ul=At=Eo=Ia=_a=ke=0,ct=zn=null,_o=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-mt(l),o=1<<i;t|=e[i],l&=~o}return sa=t,Ts(),a}function Lf(e,t){ve=null,X.H=Xs,t===gn||t===Ms?(t=Iu(),we=3):t===$u?(t=Iu(),we=4):we=t===ef?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,xt=t,je===null&&(ke=1,Ks(e,Et(t,e.current)))}function Hf(){var e=X.H;return X.H=Xs,e===null?Xs:e}function kf(){var e=X.A;return X.A=By,e}function Do(){ke=4,Ea||(Ne&4194048)!==Ne&&Rt.current!==null||(zl=!0),(_a&134217727)===0&&(Ia&134217727)===0||De===null||Ra(De,Ne,At,!1)}function Mo(e,t,a){var l=_e;_e|=2;var i=Hf(),o=kf();(De!==e||Ne!==t)&&(ti=null,Hl(e,t)),t=!1;var h=ke;e:do try{if(we!==0&&je!==null){var y=je,j=xt;switch(we){case 8:Oo(),h=6;break e;case 3:case 2:case 9:case 6:Rt.current===null&&(t=!0);var L=we;if(we=0,xt=null,kl(e,y,j,L),a&&zl){h=0;break e}break;default:L=we,we=0,xt=null,kl(e,y,j,L)}}ky(),h=ke;break}catch(Z){Lf(e,Z)}while(!0);return t&&e.shellSuspendCounter++,Wt=Fa=null,_e=l,X.H=i,X.A=o,je===null&&(De=null,Ne=0,Ts()),h}function ky(){for(;je!==null;)qf(je)}function qy(e,t){var a=_e;_e|=2;var l=Hf(),i=kf();De!==e||Ne!==t?(ti=null,ei=Ht()+500,Hl(e,t)):zl=Wl(e,t);e:do try{if(we!==0&&je!==null){t=je;var o=xt;t:switch(we){case 1:we=0,xt=null,kl(e,t,o,1);break;case 2:case 9:if(Wu(o)){we=0,xt=null,Yf(t);break}t=function(){we!==2&&we!==9||De!==e||(we=7),Xt(e)},o.then(t,t);break e;case 3:we=7;break e;case 4:we=5;break e;case 7:Wu(o)?(we=0,xt=null,Yf(t)):(we=0,xt=null,kl(e,t,o,7));break;case 5:var h=null;switch(je.tag){case 26:h=je.memoizedState;case 5:case 27:var y=je;if(!h||Sh(h)){we=0,xt=null;var j=y.sibling;if(j!==null)je=j;else{var L=y.return;L!==null?(je=L,li(L)):je=null}break t}}we=0,xt=null,kl(e,t,o,5);break;case 6:we=0,xt=null,kl(e,t,o,6);break;case 8:Oo(),ke=6;break e;default:throw Error(c(462))}}Yy();break}catch(Z){Lf(e,Z)}while(!0);return Wt=Fa=null,X.H=l,X.A=i,_e=a,je!==null?0:(De=null,Ne=0,Ts(),ke)}function Yy(){for(;je!==null&&!cp();)qf(je)}function qf(e){var t=hf(e.alternate,e,sa);e.memoizedProps=e.pendingProps,t===null?li(e):je=t}function Yf(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=rf(a,t,t.pendingProps,t.type,void 0,Ne);break;case 11:t=rf(a,t,t.pendingProps,t.type.render,t.ref,Ne);break;case 5:Fr(t);default:pf(a,t),t=je=Yu(t,sa),t=hf(a,t,sa)}e.memoizedProps=e.pendingProps,t===null?li(e):je=t}function kl(e,t,a,l){Wt=Fa=null,Fr(t),Al=null,_n=0;var i=t.return;try{if(Ay(e,i,t,a,Ne)){ke=1,Ks(e,Et(a,e.current)),je=null;return}}catch(o){if(i!==null)throw je=i,o;ke=1,Ks(e,Et(a,e.current)),je=null;return}t.flags&32768?(Ee||l===1?e=!0:zl||(Ne&536870912)!==0?e=!1:(Ea=e=!0,(l===2||l===9||l===3||l===6)&&(l=Rt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Gf(t,e)):li(t)}function li(e){var t=e;do{if((t.flags&32768)!==0){Gf(t,Ea);return}e=t.return;var a=Dy(t.alternate,t,sa);if(a!==null){je=a;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);ke===0&&(ke=5)}function Gf(e,t){do{var a=My(e.alternate,e);if(a!==null){a.flags&=32767,je=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){je=e;return}je=e=a}while(e!==null);ke=6,je=null}function Xf(e,t,a,l,i,o,h,y,j){e.cancelPendingCommit=null;do ni();while(We!==0);if((_e&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(o=t.lanes|t.childLanes,o|=Sr,xp(e,a,o,h,y,j),e===De&&(je=De=null,Ne=0),Bl=t,Ca=e,Ll=a,Co=o,Ro=i,Mf=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Qy(cs,function(){return Kf(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=X.T,X.T=null,i=te.p,te.p=2,h=_e,_e|=4;try{zy(e,t,a)}finally{_e=h,te.p=i,X.T=l}}We=1,Vf(),Qf(),Zf()}}function Vf(){if(We===1){We=0;var e=Ca,t=Bl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=X.T,X.T=null;var l=te.p;te.p=2;var i=_e;_e|=4;try{_f(t,e);var o=Qo,h=Ou(e.containerInfo),y=o.focusedElem,j=o.selectionRange;if(h!==y&&y&&y.ownerDocument&&Au(y.ownerDocument.documentElement,y)){if(j!==null&&gr(y)){var L=j.start,Z=j.end;if(Z===void 0&&(Z=L),"selectionStart"in y)y.selectionStart=L,y.selectionEnd=Math.min(Z,y.value.length);else{var J=y.ownerDocument||document,H=J&&J.defaultView||window;if(H.getSelection){var k=H.getSelection(),fe=y.textContent.length,ue=Math.min(j.start,fe),Ae=j.end===void 0?ue:Math.min(j.end,fe);!k.extend&&ue>Ae&&(h=Ae,Ae=ue,ue=h);var z=Ru(y,ue),A=Ru(y,Ae);if(z&&A&&(k.rangeCount!==1||k.anchorNode!==z.node||k.anchorOffset!==z.offset||k.focusNode!==A.node||k.focusOffset!==A.offset)){var B=J.createRange();B.setStart(z.node,z.offset),k.removeAllRanges(),ue>Ae?(k.addRange(B),k.extend(A.node,A.offset)):(B.setEnd(A.node,A.offset),k.addRange(B))}}}}for(J=[],k=y;k=k.parentNode;)k.nodeType===1&&J.push({element:k,left:k.scrollLeft,top:k.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<J.length;y++){var K=J[y];K.element.scrollLeft=K.left,K.element.scrollTop=K.top}}yi=!!Vo,Qo=Vo=null}finally{_e=i,te.p=l,X.T=a}}e.current=t,We=2}}function Qf(){if(We===2){We=0;var e=Ca,t=Bl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=X.T,X.T=null;var l=te.p;te.p=2;var i=_e;_e|=4;try{Sf(e,t.alternate,t)}finally{_e=i,te.p=l,X.T=a}}We=3}}function Zf(){if(We===4||We===3){We=0,up();var e=Ca,t=Bl,a=Ll,l=Mf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?We=5:(We=0,Bl=Ca=null,Ff(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(wa=null),$i(a),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot($l,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=X.T,i=te.p,te.p=2,X.T=null;try{for(var o=e.onRecoverableError,h=0;h<l.length;h++){var y=l[h];o(y.value,{componentStack:y.stack})}}finally{X.T=t,te.p=i}}(Ll&3)!==0&&ni(),Xt(e),i=e.pendingLanes,(a&4194090)!==0&&(i&42)!==0?e===Ao?Un++:(Un=0,Ao=e):Un=0,Bn(0)}}function Ff(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,pn(t)))}function ni(e){return Vf(),Qf(),Zf(),Kf()}function Kf(){if(We!==5)return!1;var e=Ca,t=Co;Co=0;var a=$i(Ll),l=X.T,i=te.p;try{te.p=32>a?32:a,X.T=null,a=Ro,Ro=null;var o=Ca,h=Ll;if(We=0,Bl=Ca=null,Ll=0,(_e&6)!==0)throw Error(c(331));var y=_e;if(_e|=4,Of(o.current),Cf(o,o.current,h,a),_e=y,Bn(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot($l,o)}catch{}return!0}finally{te.p=i,X.T=l,Ff(e,t)}}function Jf(e,t,a){t=Et(a,t),t=ro(e.stateNode,t,2),e=va(e,t,2),e!==null&&(Pl(e,2),Xt(e))}function Oe(e,t,a){if(e.tag===3)Jf(e,e,a);else for(;t!==null;){if(t.tag===3){Jf(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(wa===null||!wa.has(l))){e=Et(a,e),a=Pd(2),l=va(t,a,2),l!==null&&(Id(a,l,t,e),Pl(l,2),Xt(l));break}}t=t.return}}function zo(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Ly;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(a)||(To=!0,i.add(a),e=Gy.bind(null,e,t,a),t.then(e,e))}function Gy(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,De===e&&(Ne&a)===a&&(ke===4||ke===3&&(Ne&62914560)===Ne&&300>Ht()-wo?(_e&2)===0&&Hl(e,0):Eo|=a,Ul===Ne&&(Ul=0)),Xt(e)}function $f(e,t){t===0&&(t=Qc()),e=bl(e,t),e!==null&&(Pl(e,t),Xt(e))}function Xy(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),$f(e,a)}function Vy(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(a=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),$f(e,a)}function Qy(e,t){return Zi(e,t)}var si=null,ql=null,Uo=!1,ii=!1,Bo=!1,el=0;function Xt(e){e!==ql&&e.next===null&&(ql===null?si=ql=e:ql=ql.next=e),ii=!0,Uo||(Uo=!0,Fy())}function Bn(e,t){if(!Bo&&ii){Bo=!0;do for(var a=!1,l=si;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var o=0;else{var h=l.suspendedLanes,y=l.pingedLanes;o=(1<<31-mt(42|e)+1)-1,o&=i&~(h&~y),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(a=!0,eh(l,o))}else o=Ne,o=fs(l,l===De?o:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(o&3)===0||Wl(l,o)||(a=!0,eh(l,o));l=l.next}while(a);Bo=!1}}function Zy(){Wf()}function Wf(){ii=Uo=!1;var e=0;el!==0&&(tg()&&(e=el),el=0);for(var t=Ht(),a=null,l=si;l!==null;){var i=l.next,o=Pf(l,t);o===0?(l.next=null,a===null?si=i:a.next=i,i===null&&(ql=a)):(a=l,(e!==0||(o&3)!==0)&&(ii=!0)),l=i}Bn(e)}function Pf(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var h=31-mt(o),y=1<<h,j=i[h];j===-1?((y&a)===0||(y&l)!==0)&&(i[h]=vp(y,t)):j<=t&&(e.expiredLanes|=y),o&=~y}if(t=De,a=Ne,a=fs(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(we===2||we===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Fi(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Wl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Fi(l),$i(a)){case 2:case 8:a=Gc;break;case 32:a=cs;break;case 268435456:a=Xc;break;default:a=cs}return l=If.bind(null,e),a=Zi(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Fi(l),e.callbackPriority=2,e.callbackNode=null,2}function If(e,t){if(We!==0&&We!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ni()&&e.callbackNode!==a)return null;var l=Ne;return l=fs(e,e===De?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Uf(e,l,t),Pf(e,Ht()),e.callbackNode!=null&&e.callbackNode===a?If.bind(null,e):null)}function eh(e,t){if(ni())return null;Uf(e,t,!0)}function Fy(){lg(function(){(_e&6)!==0?Zi(Yc,Zy):Wf()})}function Lo(){return el===0&&(el=Vc()),el}function th(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:gs(""+e)}function ah(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ky(e,t,a,l,i){if(t==="submit"&&a&&a.stateNode===i){var o=th((i[st]||null).action),h=l.submitter;h&&(t=(t=h[st]||null)?th(t.formAction):h.getAttribute("formAction"),t!==null&&(o=t,h=null));var y=new js("action","action",null,l,i);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(el!==0){var j=h?ah(i,h):new FormData(i);ao(a,{pending:!0,data:j,method:i.method,action:o},null,j)}}else typeof o=="function"&&(y.preventDefault(),j=h?ah(i,h):new FormData(i),ao(a,{pending:!0,data:j,method:i.method,action:o},o,j))},currentTarget:i}]})}}for(var Ho=0;Ho<jr.length;Ho++){var ko=jr[Ho],Jy=ko.toLowerCase(),$y=ko[0].toUpperCase()+ko.slice(1);Dt(Jy,"on"+$y)}Dt(zu,"onAnimationEnd"),Dt(Uu,"onAnimationIteration"),Dt(Bu,"onAnimationStart"),Dt("dblclick","onDoubleClick"),Dt("focusin","onFocus"),Dt("focusout","onBlur"),Dt(hy,"onTransitionRun"),Dt(my,"onTransitionStart"),Dt(py,"onTransitionCancel"),Dt(Lu,"onTransitionEnd"),ul("onMouseEnter",["mouseout","mouseover"]),ul("onMouseLeave",["mouseout","mouseover"]),ul("onPointerEnter",["pointerout","pointerover"]),ul("onPointerLeave",["pointerout","pointerover"]),Ha("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ha("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ha("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ha("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ha("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ha("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ln="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ln));function lh(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],i=l.event;l=l.listeners;e:{var o=void 0;if(t)for(var h=l.length-1;0<=h;h--){var y=l[h],j=y.instance,L=y.currentTarget;if(y=y.listener,j!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=L;try{o(i)}catch(Z){Fs(Z)}i.currentTarget=null,o=j}else for(h=0;h<l.length;h++){if(y=l[h],j=y.instance,L=y.currentTarget,y=y.listener,j!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=L;try{o(i)}catch(Z){Fs(Z)}i.currentTarget=null,o=j}}}}function Se(e,t){var a=t[Wi];a===void 0&&(a=t[Wi]=new Set);var l=e+"__bubble";a.has(l)||(nh(t,e,2,!1),a.add(l))}function qo(e,t,a){var l=0;t&&(l|=4),nh(a,e,l,t)}var ri="_reactListening"+Math.random().toString(36).slice(2);function Yo(e){if(!e[ri]){e[ri]=!0,$c.forEach(function(a){a!=="selectionchange"&&(Wy.has(a)||qo(a,!1,e),qo(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ri]||(t[ri]=!0,qo("selectionchange",!1,t))}}function nh(e,t,a,l){switch(Ch(t)){case 2:var i=Tg;break;case 8:i=Eg;break;default:i=tc}a=i.bind(null,t,a,e),i=void 0,!or||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,a,{capture:!0,passive:i}):e.addEventListener(t,a,!0):i!==void 0?e.addEventListener(t,a,{passive:i}):e.addEventListener(t,a,!1)}function Go(e,t,a,l,i){var o=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var y=l.stateNode.containerInfo;if(y===i)break;if(h===4)for(h=l.return;h!==null;){var j=h.tag;if((j===3||j===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;y!==null;){if(h=rl(y),h===null)return;if(j=h.tag,j===5||j===6||j===26||j===27){l=o=h;continue e}y=y.parentNode}}l=l.return}uu(function(){var L=o,Z=ir(a),J=[];e:{var H=Hu.get(e);if(H!==void 0){var k=js,fe=e;switch(e){case"keypress":if(xs(a)===0)break e;case"keydown":case"keyup":k=Qp;break;case"focusin":fe="focus",k=fr;break;case"focusout":fe="blur",k=fr;break;case"beforeblur":case"afterblur":k=fr;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":k=hu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":k=Mp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":k=Kp;break;case zu:case Uu:case Bu:k=Bp;break;case Lu:k=$p;break;case"scroll":case"scrollend":k=Op;break;case"wheel":k=Pp;break;case"copy":case"cut":case"paste":k=Hp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":k=pu;break;case"toggle":case"beforetoggle":k=ey}var ue=(t&4)!==0,Ae=!ue&&(e==="scroll"||e==="scrollend"),z=ue?H!==null?H+"Capture":null:H;ue=[];for(var A=L,B;A!==null;){var K=A;if(B=K.stateNode,K=K.tag,K!==5&&K!==26&&K!==27||B===null||z===null||(K=tn(A,z),K!=null&&ue.push(Hn(A,K,B))),Ae)break;A=A.return}0<ue.length&&(H=new k(H,fe,null,a,Z),J.push({event:H,listeners:ue}))}}if((t&7)===0){e:{if(H=e==="mouseover"||e==="pointerover",k=e==="mouseout"||e==="pointerout",H&&a!==sr&&(fe=a.relatedTarget||a.fromElement)&&(rl(fe)||fe[il]))break e;if((k||H)&&(H=Z.window===Z?Z:(H=Z.ownerDocument)?H.defaultView||H.parentWindow:window,k?(fe=a.relatedTarget||a.toElement,k=L,fe=fe?rl(fe):null,fe!==null&&(Ae=f(fe),ue=fe.tag,fe!==Ae||ue!==5&&ue!==27&&ue!==6)&&(fe=null)):(k=null,fe=L),k!==fe)){if(ue=hu,K="onMouseLeave",z="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(ue=pu,K="onPointerLeave",z="onPointerEnter",A="pointer"),Ae=k==null?H:en(k),B=fe==null?H:en(fe),H=new ue(K,A+"leave",k,a,Z),H.target=Ae,H.relatedTarget=B,K=null,rl(Z)===L&&(ue=new ue(z,A+"enter",fe,a,Z),ue.target=B,ue.relatedTarget=Ae,K=ue),Ae=K,k&&fe)t:{for(ue=k,z=fe,A=0,B=ue;B;B=Yl(B))A++;for(B=0,K=z;K;K=Yl(K))B++;for(;0<A-B;)ue=Yl(ue),A--;for(;0<B-A;)z=Yl(z),B--;for(;A--;){if(ue===z||z!==null&&ue===z.alternate)break t;ue=Yl(ue),z=Yl(z)}ue=null}else ue=null;k!==null&&sh(J,H,k,ue,!1),fe!==null&&Ae!==null&&sh(J,Ae,fe,ue,!0)}}e:{if(H=L?en(L):window,k=H.nodeName&&H.nodeName.toLowerCase(),k==="select"||k==="input"&&H.type==="file")var ie=Nu;else if(ju(H))if(Tu)ie=uy;else{ie=oy;var be=ry}else k=H.nodeName,!k||k.toLowerCase()!=="input"||H.type!=="checkbox"&&H.type!=="radio"?L&&nr(L.elementType)&&(ie=Nu):ie=cy;if(ie&&(ie=ie(e,L))){Su(J,ie,a,Z);break e}be&&be(e,H,L),e==="focusout"&&L&&H.type==="number"&&L.memoizedProps.value!=null&&lr(H,"number",H.value)}switch(be=L?en(L):window,e){case"focusin":(ju(be)||be.contentEditable==="true")&&(gl=be,vr=L,un=null);break;case"focusout":un=vr=gl=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,Du(J,a,Z);break;case"selectionchange":if(fy)break;case"keydown":case"keyup":Du(J,a,Z)}var oe;if(mr)e:{switch(e){case"compositionstart":var de="onCompositionStart";break e;case"compositionend":de="onCompositionEnd";break e;case"compositionupdate":de="onCompositionUpdate";break e}de=void 0}else yl?xu(e,a)&&(de="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(de="onCompositionStart");de&&(yu&&a.locale!=="ko"&&(yl||de!=="onCompositionStart"?de==="onCompositionEnd"&&yl&&(oe=du()):(ma=Z,cr="value"in ma?ma.value:ma.textContent,yl=!0)),be=oi(L,de),0<be.length&&(de=new mu(de,e,null,a,Z),J.push({event:de,listeners:be}),oe?de.data=oe:(oe=bu(a),oe!==null&&(de.data=oe)))),(oe=ay?ly(e,a):ny(e,a))&&(de=oi(L,"onBeforeInput"),0<de.length&&(be=new mu("onBeforeInput","beforeinput",null,a,Z),J.push({event:be,listeners:de}),be.data=oe)),Ky(J,e,L,a,Z)}lh(J,t)})}function Hn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function oi(e,t){for(var a=t+"Capture",l=[];e!==null;){var i=e,o=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||o===null||(i=tn(e,a),i!=null&&l.unshift(Hn(e,i,o)),i=tn(e,t),i!=null&&l.push(Hn(e,i,o))),e.tag===3)return l;e=e.return}return[]}function Yl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function sh(e,t,a,l,i){for(var o=t._reactName,h=[];a!==null&&a!==l;){var y=a,j=y.alternate,L=y.stateNode;if(y=y.tag,j!==null&&j===l)break;y!==5&&y!==26&&y!==27||L===null||(j=L,i?(L=tn(a,o),L!=null&&h.unshift(Hn(a,L,j))):i||(L=tn(a,o),L!=null&&h.push(Hn(a,L,j)))),a=a.return}h.length!==0&&e.push({event:t,listeners:h})}var Py=/\r\n?/g,Iy=/\u0000|\uFFFD/g;function ih(e){return(typeof e=="string"?e:""+e).replace(Py,`
`).replace(Iy,"")}function rh(e,t){return t=ih(t),ih(e)===t}function ci(){}function Re(e,t,a,l,i,o){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||hl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&hl(e,""+l);break;case"className":ms(e,"class",l);break;case"tabIndex":ms(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ms(e,a,l);break;case"style":ou(e,l,o);break;case"data":if(t!=="object"){ms(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gs(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(a==="formAction"?(t!=="input"&&Re(e,t,"name",i.name,i,null),Re(e,t,"formEncType",i.formEncType,i,null),Re(e,t,"formMethod",i.formMethod,i,null),Re(e,t,"formTarget",i.formTarget,i,null)):(Re(e,t,"encType",i.encType,i,null),Re(e,t,"method",i.method,i,null),Re(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gs(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=ci);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=gs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":Se("beforetoggle",e),Se("toggle",e),hs(e,"popover",l);break;case"xlinkActuate":Zt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Zt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Zt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Zt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Zt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Zt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":hs(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Rp.get(a)||a,hs(e,a,l))}}function Xo(e,t,a,l,i,o){switch(a){case"style":ou(e,l,o);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof l=="string"?hl(e,l):(typeof l=="number"||typeof l=="bigint")&&hl(e,""+l);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"onClick":l!=null&&(e.onclick=ci);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Wc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),t=a.slice(2,i?a.length-7:void 0),o=e[st]||null,o=o!=null?o[a]:null,typeof o=="function"&&e.removeEventListener(t,o,i),typeof l=="function")){typeof o!="function"&&o!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,i);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):hs(e,a,l)}}}function Pe(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var l=!1,i=!1,o;for(o in a)if(a.hasOwnProperty(o)){var h=a[o];if(h!=null)switch(o){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Re(e,t,o,h,a,null)}}i&&Re(e,t,"srcSet",a.srcSet,a,null),l&&Re(e,t,"src",a.src,a,null);return;case"input":Se("invalid",e);var y=o=h=i=null,j=null,L=null;for(l in a)if(a.hasOwnProperty(l)){var Z=a[l];if(Z!=null)switch(l){case"name":i=Z;break;case"type":h=Z;break;case"checked":j=Z;break;case"defaultChecked":L=Z;break;case"value":o=Z;break;case"defaultValue":y=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(c(137,t));break;default:Re(e,t,l,Z,a,null)}}nu(e,o,y,j,L,h,i,!1),ps(e);return;case"select":Se("invalid",e),l=h=o=null;for(i in a)if(a.hasOwnProperty(i)&&(y=a[i],y!=null))switch(i){case"value":o=y;break;case"defaultValue":h=y;break;case"multiple":l=y;default:Re(e,t,i,y,a,null)}t=o,a=h,e.multiple=!!l,t!=null?fl(e,!!l,t,!1):a!=null&&fl(e,!!l,a,!0);return;case"textarea":Se("invalid",e),o=i=l=null;for(h in a)if(a.hasOwnProperty(h)&&(y=a[h],y!=null))switch(h){case"value":l=y;break;case"defaultValue":i=y;break;case"children":o=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(c(91));break;default:Re(e,t,h,y,a,null)}iu(e,l,i,o),ps(e);return;case"option":for(j in a)if(a.hasOwnProperty(j)&&(l=a[j],l!=null))switch(j){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Re(e,t,j,l,a,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(l=0;l<Ln.length;l++)Se(Ln[l],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(L in a)if(a.hasOwnProperty(L)&&(l=a[L],l!=null))switch(L){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Re(e,t,L,l,a,null)}return;default:if(nr(t)){for(Z in a)a.hasOwnProperty(Z)&&(l=a[Z],l!==void 0&&Xo(e,t,Z,l,a,void 0));return}}for(y in a)a.hasOwnProperty(y)&&(l=a[y],l!=null&&Re(e,t,y,l,a,null))}function eg(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,o=null,h=null,y=null,j=null,L=null,Z=null;for(k in a){var J=a[k];if(a.hasOwnProperty(k)&&J!=null)switch(k){case"checked":break;case"value":break;case"defaultValue":j=J;default:l.hasOwnProperty(k)||Re(e,t,k,null,l,J)}}for(var H in l){var k=l[H];if(J=a[H],l.hasOwnProperty(H)&&(k!=null||J!=null))switch(H){case"type":o=k;break;case"name":i=k;break;case"checked":L=k;break;case"defaultChecked":Z=k;break;case"value":h=k;break;case"defaultValue":y=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(c(137,t));break;default:k!==J&&Re(e,t,H,k,l,J)}}ar(e,h,y,j,L,Z,o,i);return;case"select":k=h=y=H=null;for(o in a)if(j=a[o],a.hasOwnProperty(o)&&j!=null)switch(o){case"value":break;case"multiple":k=j;default:l.hasOwnProperty(o)||Re(e,t,o,null,l,j)}for(i in l)if(o=l[i],j=a[i],l.hasOwnProperty(i)&&(o!=null||j!=null))switch(i){case"value":H=o;break;case"defaultValue":y=o;break;case"multiple":h=o;default:o!==j&&Re(e,t,i,o,l,j)}t=y,a=h,l=k,H!=null?fl(e,!!a,H,!1):!!l!=!!a&&(t!=null?fl(e,!!a,t,!0):fl(e,!!a,a?[]:"",!1));return;case"textarea":k=H=null;for(y in a)if(i=a[y],a.hasOwnProperty(y)&&i!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:Re(e,t,y,null,l,i)}for(h in l)if(i=l[h],o=a[h],l.hasOwnProperty(h)&&(i!=null||o!=null))switch(h){case"value":H=i;break;case"defaultValue":k=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==o&&Re(e,t,h,i,l,o)}su(e,H,k);return;case"option":for(var fe in a)if(H=a[fe],a.hasOwnProperty(fe)&&H!=null&&!l.hasOwnProperty(fe))switch(fe){case"selected":e.selected=!1;break;default:Re(e,t,fe,null,l,H)}for(j in l)if(H=l[j],k=a[j],l.hasOwnProperty(j)&&H!==k&&(H!=null||k!=null))switch(j){case"selected":e.selected=H&&typeof H!="function"&&typeof H!="symbol";break;default:Re(e,t,j,H,l,k)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ue in a)H=a[ue],a.hasOwnProperty(ue)&&H!=null&&!l.hasOwnProperty(ue)&&Re(e,t,ue,null,l,H);for(L in l)if(H=l[L],k=a[L],l.hasOwnProperty(L)&&H!==k&&(H!=null||k!=null))switch(L){case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(c(137,t));break;default:Re(e,t,L,H,l,k)}return;default:if(nr(t)){for(var Ae in a)H=a[Ae],a.hasOwnProperty(Ae)&&H!==void 0&&!l.hasOwnProperty(Ae)&&Xo(e,t,Ae,void 0,l,H);for(Z in l)H=l[Z],k=a[Z],!l.hasOwnProperty(Z)||H===k||H===void 0&&k===void 0||Xo(e,t,Z,H,l,k);return}}for(var z in a)H=a[z],a.hasOwnProperty(z)&&H!=null&&!l.hasOwnProperty(z)&&Re(e,t,z,null,l,H);for(J in l)H=l[J],k=a[J],!l.hasOwnProperty(J)||H===k||H==null&&k==null||Re(e,t,J,H,l,k)}var Vo=null,Qo=null;function ui(e){return e.nodeType===9?e:e.ownerDocument}function oh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ch(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Zo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fo=null;function tg(){var e=window.event;return e&&e.type==="popstate"?e===Fo?!1:(Fo=e,!0):(Fo=null,!1)}var uh=typeof setTimeout=="function"?setTimeout:void 0,ag=typeof clearTimeout=="function"?clearTimeout:void 0,dh=typeof Promise=="function"?Promise:void 0,lg=typeof queueMicrotask=="function"?queueMicrotask:typeof dh<"u"?function(e){return dh.resolve(null).then(e).catch(ng)}:uh;function ng(e){setTimeout(function(){throw e})}function Aa(e){return e==="head"}function fh(e,t){var a=t,l=0,i=0;do{var o=a.nextSibling;if(e.removeChild(a),o&&o.nodeType===8)if(a=o.data,a==="/$"){if(0<l&&8>l){a=l;var h=e.ownerDocument;if(a&1&&kn(h.documentElement),a&2&&kn(h.body),a&4)for(a=h.head,kn(a),h=a.firstChild;h;){var y=h.nextSibling,j=h.nodeName;h[Il]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&h.rel.toLowerCase()==="stylesheet"||a.removeChild(h),h=y}}if(i===0){e.removeChild(o),Fn(t);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:l=a.charCodeAt(0)-48;else l=0;a=o}while(a);Fn(t)}function Ko(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Ko(a),Pi(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function sg(e,t,a,l){for(;e.nodeType===1;){var i=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Il])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=zt(e.nextSibling),e===null)break}return null}function ig(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=zt(e.nextSibling),e===null))return null;return e}function Jo(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function rg(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var $o=null;function hh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function mh(e,t,a){switch(t=ui(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function kn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Pi(e)}var Ot=new Map,ph=new Set;function di(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ia=te.d;te.d={f:og,r:cg,D:ug,C:dg,L:fg,m:hg,X:pg,S:mg,M:yg};function og(){var e=ia.f(),t=ai();return e||t}function cg(e){var t=ol(e);t!==null&&t.tag===5&&t.type==="form"?zd(t):ia.r(e)}var Gl=typeof document>"u"?null:document;function yh(e,t,a){var l=Gl;if(l&&typeof t=="string"&&t){var i=Tt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),ph.has(i)||(ph.add(i),e={rel:e,crossOrigin:a,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function ug(e){ia.D(e),yh("dns-prefetch",e,null)}function dg(e,t){ia.C(e,t),yh("preconnect",e,t)}function fg(e,t,a){ia.L(e,t,a);var l=Gl;if(l&&e&&t){var i='link[rel="preload"][as="'+Tt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+Tt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+Tt(a.imageSizes)+'"]')):i+='[href="'+Tt(e)+'"]';var o=i;switch(t){case"style":o=Xl(e);break;case"script":o=Vl(e)}Ot.has(o)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Ot.set(o,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(qn(o))||t==="script"&&l.querySelector(Yn(o))||(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function hg(e,t){ia.m(e,t);var a=Gl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Tt(l)+'"][href="'+Tt(e)+'"]',o=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Vl(e)}if(!Ot.has(o)&&(e=x({rel:"modulepreload",href:e},t),Ot.set(o,e),a.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Yn(o)))return}l=a.createElement("link"),Pe(l,"link",e),Ze(l),a.head.appendChild(l)}}}function mg(e,t,a){ia.S(e,t,a);var l=Gl;if(l&&e){var i=cl(l).hoistableStyles,o=Xl(e);t=t||"default";var h=i.get(o);if(!h){var y={loading:0,preload:null};if(h=l.querySelector(qn(o)))y.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Ot.get(o))&&Wo(e,a);var j=h=l.createElement("link");Ze(j),Pe(j,"link",e),j._p=new Promise(function(L,Z){j.onload=L,j.onerror=Z}),j.addEventListener("load",function(){y.loading|=1}),j.addEventListener("error",function(){y.loading|=2}),y.loading|=4,fi(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:y},i.set(o,h)}}}function pg(e,t){ia.X(e,t);var a=Gl;if(a&&e){var l=cl(a).hoistableScripts,i=Vl(e),o=l.get(i);o||(o=a.querySelector(Yn(i)),o||(e=x({src:e,async:!0},t),(t=Ot.get(i))&&Po(e,t),o=a.createElement("script"),Ze(o),Pe(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function yg(e,t){ia.M(e,t);var a=Gl;if(a&&e){var l=cl(a).hoistableScripts,i=Vl(e),o=l.get(i);o||(o=a.querySelector(Yn(i)),o||(e=x({src:e,async:!0,type:"module"},t),(t=Ot.get(i))&&Po(e,t),o=a.createElement("script"),Ze(o),Pe(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function gh(e,t,a,l){var i=(i=he.current)?di(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Xl(a.href),a=cl(i).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Xl(a.href);var o=cl(i).hoistableStyles,h=o.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,h),(o=i.querySelector(qn(e)))&&!o._p&&(h.instance=o,h.state.loading=5),Ot.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Ot.set(e,a),o||gg(i,e,a,h.state))),t&&l===null)throw Error(c(528,""));return h}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Vl(a),a=cl(i).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Xl(e){return'href="'+Tt(e)+'"'}function qn(e){return'link[rel="stylesheet"]['+e+"]"}function vh(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function gg(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Pe(t,"link",a),Ze(t),e.head.appendChild(t))}function Vl(e){return'[src="'+Tt(e)+'"]'}function Yn(e){return"script[async]"+e}function xh(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Tt(a.href)+'"]');if(l)return t.instance=l,Ze(l),l;var i=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ze(l),Pe(l,"style",i),fi(l,a.precedence,e),t.instance=l;case"stylesheet":i=Xl(a.href);var o=e.querySelector(qn(i));if(o)return t.state.loading|=4,t.instance=o,Ze(o),o;l=vh(a),(i=Ot.get(i))&&Wo(l,i),o=(e.ownerDocument||e).createElement("link"),Ze(o);var h=o;return h._p=new Promise(function(y,j){h.onload=y,h.onerror=j}),Pe(o,"link",l),t.state.loading|=4,fi(o,a.precedence,e),t.instance=o;case"script":return o=Vl(a.src),(i=e.querySelector(Yn(o)))?(t.instance=i,Ze(i),i):(l=a,(i=Ot.get(o))&&(l=x({},a),Po(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),Ze(i),Pe(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,fi(l,a.precedence,e));return t.instance}function fi(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,o=i,h=0;h<l.length;h++){var y=l[h];if(y.dataset.precedence===t)o=y;else if(o!==i)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Wo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Po(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var hi=null;function bh(e,t,a){if(hi===null){var l=new Map,i=hi=new Map;i.set(a,l)}else i=hi,l=i.get(a),l||(l=new Map,i.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),i=0;i<a.length;i++){var o=a[i];if(!(o[Il]||o[Ie]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(t)||"";h=e+h;var y=l.get(h);y?y.push(o):l.set(h,[o])}}return l}function jh(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function vg(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Sh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Gn=null;function xg(){}function bg(e,t,a){if(Gn===null)throw Error(c(475));var l=Gn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=Xl(a.href),o=e.querySelector(qn(i));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=mi.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=o,Ze(o);return}o=e.ownerDocument||e,a=vh(a),(i=Ot.get(i))&&Wo(a,i),o=o.createElement("link"),Ze(o);var h=o;h._p=new Promise(function(y,j){h.onload=y,h.onerror=j}),Pe(o,"link",a),t.instance=o}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=mi.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function jg(){if(Gn===null)throw Error(c(475));var e=Gn;return e.stylesheets&&e.count===0&&Io(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Io(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function mi(){if(this.count--,this.count===0){if(this.stylesheets)Io(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var pi=null;function Io(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,pi=new Map,t.forEach(Sg,e),pi=null,mi.call(e))}function Sg(e,t){if(!(t.state.loading&4)){var a=pi.get(e);if(a)var l=a.get(null);else{a=new Map,pi.set(e,a);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<i.length;o++){var h=i[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(a.set(h.dataset.precedence,h),l=h)}l&&a.set(null,l)}i=t.instance,h=i.getAttribute("data-precedence"),o=a.get(h)||l,o===l&&a.set(null,i),a.set(h,i),this.count++,l=mi.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),o?o.parentNode.insertBefore(i,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Xn={$$typeof:T,Provider:null,Consumer:null,_currentValue:q,_currentValue2:q,_threadCount:0};function Ng(e,t,a,l,i,o,h,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ki(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ki(0),this.hiddenUpdates=Ki(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Nh(e,t,a,l,i,o,h,y,j,L,Z,J){return e=new Ng(e,t,a,h,y,j,L,J),t=1,o===!0&&(t|=24),o=yt(3,null,null,t),e.current=o,o.stateNode=e,t=Mr(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:l,isDehydrated:a,cache:t},Lr(o),e}function Th(e){return e?(e=jl,e):jl}function Eh(e,t,a,l,i,o){i=Th(i),l.context===null?l.context=i:l.pendingContext=i,l=ga(t),l.payload={element:a},o=o===void 0?null:o,o!==null&&(l.callback=o),a=va(e,l,t),a!==null&&(jt(a,e,t),xn(a,e,t))}function _h(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function ec(e,t){_h(e,t),(e=e.alternate)&&_h(e,t)}function wh(e){if(e.tag===13){var t=bl(e,67108864);t!==null&&jt(t,e,67108864),ec(e,67108864)}}var yi=!0;function Tg(e,t,a,l){var i=X.T;X.T=null;var o=te.p;try{te.p=2,tc(e,t,a,l)}finally{te.p=o,X.T=i}}function Eg(e,t,a,l){var i=X.T;X.T=null;var o=te.p;try{te.p=8,tc(e,t,a,l)}finally{te.p=o,X.T=i}}function tc(e,t,a,l){if(yi){var i=ac(l);if(i===null)Go(e,t,l,gi,a),Rh(e,l);else if(wg(i,e,t,a,l))l.stopPropagation();else if(Rh(e,l),t&4&&-1<_g.indexOf(e)){for(;i!==null;){var o=ol(i);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=La(o.pendingLanes);if(h!==0){var y=o;for(y.pendingLanes|=2,y.entangledLanes|=2;h;){var j=1<<31-mt(h);y.entanglements[1]|=j,h&=~j}Xt(o),(_e&6)===0&&(ei=Ht()+500,Bn(0))}}break;case 13:y=bl(o,2),y!==null&&jt(y,o,2),ai(),ec(o,2)}if(o=ac(l),o===null&&Go(e,t,l,gi,a),o===i)break;i=o}i!==null&&l.stopPropagation()}else Go(e,t,l,null,a)}}function ac(e){return e=ir(e),lc(e)}var gi=null;function lc(e){if(gi=null,e=rl(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=m(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return gi=e,null}function Ch(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(dp()){case Yc:return 2;case Gc:return 8;case cs:case fp:return 32;case Xc:return 268435456;default:return 32}default:return 32}}var nc=!1,Oa=null,Da=null,Ma=null,Vn=new Map,Qn=new Map,za=[],_g="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Rh(e,t){switch(e){case"focusin":case"focusout":Oa=null;break;case"dragenter":case"dragleave":Da=null;break;case"mouseover":case"mouseout":Ma=null;break;case"pointerover":case"pointerout":Vn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qn.delete(t.pointerId)}}function Zn(e,t,a,l,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:o,targetContainers:[i]},t!==null&&(t=ol(t),t!==null&&wh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function wg(e,t,a,l,i){switch(t){case"focusin":return Oa=Zn(Oa,e,t,a,l,i),!0;case"dragenter":return Da=Zn(Da,e,t,a,l,i),!0;case"mouseover":return Ma=Zn(Ma,e,t,a,l,i),!0;case"pointerover":var o=i.pointerId;return Vn.set(o,Zn(Vn.get(o)||null,e,t,a,l,i)),!0;case"gotpointercapture":return o=i.pointerId,Qn.set(o,Zn(Qn.get(o)||null,e,t,a,l,i)),!0}return!1}function Ah(e){var t=rl(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=m(a),t!==null){e.blockedOn=t,bp(e.priority,function(){if(a.tag===13){var l=bt();l=Ji(l);var i=bl(a,l);i!==null&&jt(i,a,l),ec(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=ac(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);sr=l,a.target.dispatchEvent(l),sr=null}else return t=ol(a),t!==null&&wh(t),e.blockedOn=a,!1;t.shift()}return!0}function Oh(e,t,a){vi(e)&&a.delete(t)}function Cg(){nc=!1,Oa!==null&&vi(Oa)&&(Oa=null),Da!==null&&vi(Da)&&(Da=null),Ma!==null&&vi(Ma)&&(Ma=null),Vn.forEach(Oh),Qn.forEach(Oh)}function xi(e,t){e.blockedOn===t&&(e.blockedOn=null,nc||(nc=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Cg)))}var bi=null;function Dh(e){bi!==e&&(bi=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){bi===e&&(bi=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(lc(l||a)===null)continue;break}var o=ol(a);o!==null&&(e.splice(t,3),t-=3,ao(o,{pending:!0,data:i,method:a.method,action:l},l,i))}}))}function Fn(e){function t(j){return xi(j,e)}Oa!==null&&xi(Oa,e),Da!==null&&xi(Da,e),Ma!==null&&xi(Ma,e),Vn.forEach(t),Qn.forEach(t);for(var a=0;a<za.length;a++){var l=za[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<za.length&&(a=za[0],a.blockedOn===null);)Ah(a),a.blockedOn===null&&za.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var i=a[l],o=a[l+1],h=i[st]||null;if(typeof o=="function")h||Dh(a);else if(h){var y=null;if(o&&o.hasAttribute("formAction")){if(i=o,h=o[st]||null)y=h.formAction;else if(lc(i)!==null)continue}else y=h.action;typeof y=="function"?a[l+1]=y:(a.splice(l,3),l-=3),Dh(a)}}}function sc(e){this._internalRoot=e}ji.prototype.render=sc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,l=bt();Eh(a,l,e,t,null,null)},ji.prototype.unmount=sc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Eh(e.current,2,null,e,null,null),ai(),t[il]=null}};function ji(e){this._internalRoot=e}ji.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kc();e={blockedOn:null,target:e,priority:t};for(var a=0;a<za.length&&t!==0&&t<za[a].priority;a++);za.splice(a,0,e),a===0&&Ah(e)}};var Mh=r.version;if(Mh!=="19.1.0")throw Error(c(527,Mh,"19.1.0"));te.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=v(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var Rg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:X,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Si.isDisabled&&Si.supportsFiber)try{$l=Si.inject(Rg),ht=Si}catch{}}return Jn.createRoot=function(e,t){if(!d(e))throw Error(c(299));var a=!1,l="",i=Kd,o=Jd,h=$d,y=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Nh(e,1,!1,null,null,a,l,i,o,h,y,null),e[il]=t.current,Yo(e),new sc(t)},Jn.hydrateRoot=function(e,t,a){if(!d(e))throw Error(c(299));var l=!1,i="",o=Kd,h=Jd,y=$d,j=null,L=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(o=a.onUncaughtError),a.onCaughtError!==void 0&&(h=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(j=a.unstable_transitionCallbacks),a.formState!==void 0&&(L=a.formState)),t=Nh(e,1,!0,t,a??null,l,i,o,h,y,j,L),t.context=Th(null),a=t.current,l=bt(),l=Ji(l),i=ga(l),i.callback=null,va(a,i,l),a=l,t.current.lanes=a,Pl(t,a),Xt(t),e[il]=t.current,Yo(e),new ji(t)},Jn.version="19.1.0",Jn}var Xh;function qg(){if(Xh)return oc.exports;Xh=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),oc.exports=kg(),oc.exports}var Yg=qg(),$n={},Vh;function Gg(){if(Vh)return $n;Vh=1,Object.defineProperty($n,"__esModule",{value:!0}),$n.parse=m,$n.serialize=p;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,f=(()=>{const _=function(){};return _.prototype=Object.create(null),_})();function m(_,U){const C=new f,D=_.length;if(D<2)return C;const M=(U==null?void 0:U.decode)||x;let O=0;do{const R=_.indexOf("=",O);if(R===-1)break;const T=_.indexOf(";",O),N=T===-1?D:T;if(R>N){O=_.lastIndexOf(";",R-1)+1;continue}const w=g(_,O,R),W=v(_,R,w),P=_.slice(w,W);if(C[P]===void 0){let F=g(_,R+1,N),ee=v(_,N,F);const Q=M(_.slice(F,ee));C[P]=Q}O=N+1}while(O<D);return C}function g(_,U,C){do{const D=_.charCodeAt(U);if(D!==32&&D!==9)return U}while(++U<C);return C}function v(_,U,C){for(;U>C;){const D=_.charCodeAt(--U);if(D!==32&&D!==9)return U+1}return C}function p(_,U,C){const D=(C==null?void 0:C.encode)||encodeURIComponent;if(!n.test(_))throw new TypeError(`argument name is invalid: ${_}`);const M=D(U);if(!r.test(M))throw new TypeError(`argument val is invalid: ${U}`);let O=_+"="+M;if(!C)return O;if(C.maxAge!==void 0){if(!Number.isInteger(C.maxAge))throw new TypeError(`option maxAge is invalid: ${C.maxAge}`);O+="; Max-Age="+C.maxAge}if(C.domain){if(!u.test(C.domain))throw new TypeError(`option domain is invalid: ${C.domain}`);O+="; Domain="+C.domain}if(C.path){if(!c.test(C.path))throw new TypeError(`option path is invalid: ${C.path}`);O+="; Path="+C.path}if(C.expires){if(!b(C.expires)||!Number.isFinite(C.expires.valueOf()))throw new TypeError(`option expires is invalid: ${C.expires}`);O+="; Expires="+C.expires.toUTCString()}if(C.httpOnly&&(O+="; HttpOnly"),C.secure&&(O+="; Secure"),C.partitioned&&(O+="; Partitioned"),C.priority)switch(typeof C.priority=="string"?C.priority.toLowerCase():void 0){case"low":O+="; Priority=Low";break;case"medium":O+="; Priority=Medium";break;case"high":O+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${C.priority}`)}if(C.sameSite)switch(typeof C.sameSite=="string"?C.sameSite.toLowerCase():C.sameSite){case!0:case"strict":O+="; SameSite=Strict";break;case"lax":O+="; SameSite=Lax";break;case"none":O+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${C.sameSite}`)}return O}function x(_){if(_.indexOf("%")===-1)return _;try{return decodeURIComponent(_)}catch{return _}}function b(_){return d.call(_)==="[object Date]"}return $n}Gg();var Qh="popstate";function Xg(n={}){function r(c,d){let{pathname:f,search:m,hash:g}=c.location;return gc("",{pathname:f,search:m,hash:g},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function u(c,d){return typeof d=="string"?d:In(d)}return Qg(r,u,null,n)}function Be(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function Ut(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Vg(){return Math.random().toString(36).substring(2,10)}function Zh(n,r){return{usr:n.state,key:n.key,idx:r}}function gc(n,r,u=null,c){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof r=="string"?Ql(r):r,state:u,key:r&&r.key||c||Vg()}}function In({pathname:n="/",search:r="",hash:u=""}){return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),u&&u!=="#"&&(n+=u.charAt(0)==="#"?u:"#"+u),n}function Ql(n){let r={};if(n){let u=n.indexOf("#");u>=0&&(r.hash=n.substring(u),n=n.substring(0,u));let c=n.indexOf("?");c>=0&&(r.search=n.substring(c),n=n.substring(0,c)),n&&(r.pathname=n)}return r}function Qg(n,r,u,c={}){let{window:d=document.defaultView,v5Compat:f=!1}=c,m=d.history,g="POP",v=null,p=x();p==null&&(p=0,m.replaceState({...m.state,idx:p},""));function x(){return(m.state||{idx:null}).idx}function b(){g="POP";let M=x(),O=M==null?null:M-p;p=M,v&&v({action:g,location:D.location,delta:O})}function _(M,O){g="PUSH";let R=gc(D.location,M,O);p=x()+1;let T=Zh(R,p),N=D.createHref(R);try{m.pushState(T,"",N)}catch(w){if(w instanceof DOMException&&w.name==="DataCloneError")throw w;d.location.assign(N)}f&&v&&v({action:g,location:D.location,delta:1})}function U(M,O){g="REPLACE";let R=gc(D.location,M,O);p=x();let T=Zh(R,p),N=D.createHref(R);m.replaceState(T,"",N),f&&v&&v({action:g,location:D.location,delta:0})}function C(M){return Zg(M)}let D={get action(){return g},get location(){return n(d,m)},listen(M){if(v)throw new Error("A history only accepts one active listener");return d.addEventListener(Qh,b),v=M,()=>{d.removeEventListener(Qh,b),v=null}},createHref(M){return r(d,M)},createURL:C,encodeLocation(M){let O=C(M);return{pathname:O.pathname,search:O.search,hash:O.hash}},push:_,replace:U,go(M){return m.go(M)}};return D}function Zg(n,r=!1){let u="http://localhost";typeof window<"u"&&(u=window.location.origin!=="null"?window.location.origin:window.location.href),Be(u,"No window.location.(origin|href) available to create URL");let c=typeof n=="string"?n:In(n);return c=c.replace(/ $/,"%20"),!r&&c.startsWith("//")&&(c=u+c),new URL(c,u)}function fm(n,r,u="/"){return Fg(n,r,u,!1)}function Fg(n,r,u,c){let d=typeof r=="string"?Ql(r):r,f=ca(d.pathname||"/",u);if(f==null)return null;let m=hm(n);Kg(m);let g=null;for(let v=0;g==null&&v<m.length;++v){let p=s0(f);g=l0(m[v],p,c)}return g}function hm(n,r=[],u=[],c=""){let d=(f,m,g)=>{let v={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:m,route:f};v.relativePath.startsWith("/")&&(Be(v.relativePath.startsWith(c),`Absolute route path "${v.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(c.length));let p=ra([c,v.relativePath]),x=u.concat(v);f.children&&f.children.length>0&&(Be(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${p}".`),hm(f.children,r,x,p)),!(f.path==null&&!f.index)&&r.push({path:p,score:t0(p,f.index),routesMeta:x})};return n.forEach((f,m)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))d(f,m);else for(let v of mm(f.path))d(f,m,v)}),r}function mm(n){let r=n.split("/");if(r.length===0)return[];let[u,...c]=r,d=u.endsWith("?"),f=u.replace(/\?$/,"");if(c.length===0)return d?[f,""]:[f];let m=mm(c.join("/")),g=[];return g.push(...m.map(v=>v===""?f:[f,v].join("/"))),d&&g.push(...m),g.map(v=>n.startsWith("/")&&v===""?"/":v)}function Kg(n){n.sort((r,u)=>r.score!==u.score?u.score-r.score:a0(r.routesMeta.map(c=>c.childrenIndex),u.routesMeta.map(c=>c.childrenIndex)))}var Jg=/^:[\w-]+$/,$g=3,Wg=2,Pg=1,Ig=10,e0=-2,Fh=n=>n==="*";function t0(n,r){let u=n.split("/"),c=u.length;return u.some(Fh)&&(c+=e0),r&&(c+=Wg),u.filter(d=>!Fh(d)).reduce((d,f)=>d+(Jg.test(f)?$g:f===""?Pg:Ig),c)}function a0(n,r){return n.length===r.length&&n.slice(0,-1).every((c,d)=>c===r[d])?n[n.length-1]-r[r.length-1]:0}function l0(n,r,u=!1){let{routesMeta:c}=n,d={},f="/",m=[];for(let g=0;g<c.length;++g){let v=c[g],p=g===c.length-1,x=f==="/"?r:r.slice(f.length)||"/",b=Ri({path:v.relativePath,caseSensitive:v.caseSensitive,end:p},x),_=v.route;if(!b&&p&&u&&!c[c.length-1].route.index&&(b=Ri({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},x)),!b)return null;Object.assign(d,b.params),m.push({params:d,pathname:ra([f,b.pathname]),pathnameBase:c0(ra([f,b.pathnameBase])),route:_}),b.pathnameBase!=="/"&&(f=ra([f,b.pathnameBase]))}return m}function Ri(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[u,c]=n0(n.path,n.caseSensitive,n.end),d=r.match(u);if(!d)return null;let f=d[0],m=f.replace(/(.)\/+$/,"$1"),g=d.slice(1);return{params:c.reduce((p,{paramName:x,isOptional:b},_)=>{if(x==="*"){let C=g[_]||"";m=f.slice(0,f.length-C.length).replace(/(.)\/+$/,"$1")}const U=g[_];return b&&!U?p[x]=void 0:p[x]=(U||"").replace(/%2F/g,"/"),p},{}),pathname:f,pathnameBase:m,pattern:n}}function n0(n,r=!1,u=!0){Ut(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let c=[],d="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,g,v)=>(c.push({paramName:g,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(c.push({paramName:"*"}),d+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?d+="\\/*$":n!==""&&n!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,r?void 0:"i"),c]}function s0(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Ut(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),n}}function ca(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let u=r.endsWith("/")?r.length-1:r.length,c=n.charAt(u);return c&&c!=="/"?null:n.slice(u)||"/"}function i0(n,r="/"){let{pathname:u,search:c="",hash:d=""}=typeof n=="string"?Ql(n):n;return{pathname:u?u.startsWith("/")?u:r0(u,r):r,search:u0(c),hash:d0(d)}}function r0(n,r){let u=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(d=>{d===".."?u.length>1&&u.pop():d!=="."&&u.push(d)}),u.length>1?u.join("/"):"/"}function fc(n,r,u,c){return`Cannot include a '${n}' character in a manually specified \`to.${r}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${u}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function o0(n){return n.filter((r,u)=>u===0||r.route.path&&r.route.path.length>0)}function Ac(n){let r=o0(n);return r.map((u,c)=>c===r.length-1?u.pathname:u.pathnameBase)}function Oc(n,r,u,c=!1){let d;typeof n=="string"?d=Ql(n):(d={...n},Be(!d.pathname||!d.pathname.includes("?"),fc("?","pathname","search",d)),Be(!d.pathname||!d.pathname.includes("#"),fc("#","pathname","hash",d)),Be(!d.search||!d.search.includes("#"),fc("#","search","hash",d)));let f=n===""||d.pathname==="",m=f?"/":d.pathname,g;if(m==null)g=u;else{let b=r.length-1;if(!c&&m.startsWith("..")){let _=m.split("/");for(;_[0]==="..";)_.shift(),b-=1;d.pathname=_.join("/")}g=b>=0?r[b]:"/"}let v=i0(d,g),p=m&&m!=="/"&&m.endsWith("/"),x=(f||m===".")&&u.endsWith("/");return!v.pathname.endsWith("/")&&(p||x)&&(v.pathname+="/"),v}var ra=n=>n.join("/").replace(/\/\/+/g,"/"),c0=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),u0=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,d0=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function f0(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var pm=["POST","PUT","PATCH","DELETE"];new Set(pm);var h0=["GET",...pm];new Set(h0);var Zl=E.createContext(null);Zl.displayName="DataRouter";var Di=E.createContext(null);Di.displayName="DataRouterState";var ym=E.createContext({isTransitioning:!1});ym.displayName="ViewTransition";var m0=E.createContext(new Map);m0.displayName="Fetchers";var p0=E.createContext(null);p0.displayName="Await";var Bt=E.createContext(null);Bt.displayName="Navigation";var as=E.createContext(null);as.displayName="Location";var Qt=E.createContext({outlet:null,matches:[],isDataRoute:!1});Qt.displayName="Route";var Dc=E.createContext(null);Dc.displayName="RouteError";function y0(n,{relative:r}={}){Be(Fl(),"useHref() may be used only in the context of a <Router> component.");let{basename:u,navigator:c}=E.useContext(Bt),{hash:d,pathname:f,search:m}=ls(n,{relative:r}),g=f;return u!=="/"&&(g=f==="/"?u:ra([u,f])),c.createHref({pathname:g,search:m,hash:d})}function Fl(){return E.useContext(as)!=null}function Ba(){return Be(Fl(),"useLocation() may be used only in the context of a <Router> component."),E.useContext(as).location}var gm="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function vm(n){E.useContext(Bt).static||E.useLayoutEffect(n)}function xm(){let{isDataRoute:n}=E.useContext(Qt);return n?R0():g0()}function g0(){Be(Fl(),"useNavigate() may be used only in the context of a <Router> component.");let n=E.useContext(Zl),{basename:r,navigator:u}=E.useContext(Bt),{matches:c}=E.useContext(Qt),{pathname:d}=Ba(),f=JSON.stringify(Ac(c)),m=E.useRef(!1);return vm(()=>{m.current=!0}),E.useCallback((v,p={})=>{if(Ut(m.current,gm),!m.current)return;if(typeof v=="number"){u.go(v);return}let x=Oc(v,JSON.parse(f),d,p.relative==="path");n==null&&r!=="/"&&(x.pathname=x.pathname==="/"?r:ra([r,x.pathname])),(p.replace?u.replace:u.push)(x,p.state,p)},[r,u,f,d,n])}E.createContext(null);function ls(n,{relative:r}={}){let{matches:u}=E.useContext(Qt),{pathname:c}=Ba(),d=JSON.stringify(Ac(u));return E.useMemo(()=>Oc(n,JSON.parse(d),c,r==="path"),[n,d,c,r])}function v0(n,r){return bm(n,r)}function bm(n,r,u,c){var R;Be(Fl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d,static:f}=E.useContext(Bt),{matches:m}=E.useContext(Qt),g=m[m.length-1],v=g?g.params:{},p=g?g.pathname:"/",x=g?g.pathnameBase:"/",b=g&&g.route;{let T=b&&b.path||"";jm(p,!b||T.endsWith("*")||T.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${T}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${T}"> to <Route path="${T==="/"?"*":`${T}/*`}">.`)}let _=Ba(),U;if(r){let T=typeof r=="string"?Ql(r):r;Be(x==="/"||((R=T.pathname)==null?void 0:R.startsWith(x)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${x}" but pathname "${T.pathname}" was given in the \`location\` prop.`),U=T}else U=_;let C=U.pathname||"/",D=C;if(x!=="/"){let T=x.replace(/^\//,"").split("/");D="/"+C.replace(/^\//,"").split("/").slice(T.length).join("/")}let M=!f&&u&&u.matches&&u.matches.length>0?u.matches:fm(n,{pathname:D});Ut(b||M!=null,`No routes matched location "${U.pathname}${U.search}${U.hash}" `),Ut(M==null||M[M.length-1].route.element!==void 0||M[M.length-1].route.Component!==void 0||M[M.length-1].route.lazy!==void 0,`Matched leaf route at location "${U.pathname}${U.search}${U.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let O=N0(M&&M.map(T=>Object.assign({},T,{params:Object.assign({},v,T.params),pathname:ra([x,d.encodeLocation?d.encodeLocation(T.pathname).pathname:T.pathname]),pathnameBase:T.pathnameBase==="/"?x:ra([x,d.encodeLocation?d.encodeLocation(T.pathnameBase).pathname:T.pathnameBase])})),m,u,c);return r&&O?E.createElement(as.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...U},navigationType:"POP"}},O):O}function x0(){let n=C0(),r=f0(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),u=n instanceof Error?n.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},f={padding:"2px 4px",backgroundColor:c},m=null;return console.error("Error handled by React Router default ErrorBoundary:",n),m=E.createElement(E.Fragment,null,E.createElement("p",null,"💿 Hey developer 👋"),E.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",E.createElement("code",{style:f},"ErrorBoundary")," or"," ",E.createElement("code",{style:f},"errorElement")," prop on your route.")),E.createElement(E.Fragment,null,E.createElement("h2",null,"Unexpected Application Error!"),E.createElement("h3",{style:{fontStyle:"italic"}},r),u?E.createElement("pre",{style:d},u):null,m)}var b0=E.createElement(x0,null),j0=class extends E.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,r){return r.location!==n.location||r.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:r.error,location:r.location,revalidation:n.revalidation||r.revalidation}}componentDidCatch(n,r){console.error("React Router caught the following error during render",n,r)}render(){return this.state.error!==void 0?E.createElement(Qt.Provider,{value:this.props.routeContext},E.createElement(Dc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function S0({routeContext:n,match:r,children:u}){let c=E.useContext(Zl);return c&&c.static&&c.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=r.route.id),E.createElement(Qt.Provider,{value:n},u)}function N0(n,r=[],u=null,c=null){if(n==null){if(!u)return null;if(u.errors)n=u.matches;else if(r.length===0&&!u.initialized&&u.matches.length>0)n=u.matches;else return null}let d=n,f=u==null?void 0:u.errors;if(f!=null){let v=d.findIndex(p=>p.route.id&&(f==null?void 0:f[p.route.id])!==void 0);Be(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),d=d.slice(0,Math.min(d.length,v+1))}let m=!1,g=-1;if(u)for(let v=0;v<d.length;v++){let p=d[v];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(g=v),p.route.id){let{loaderData:x,errors:b}=u,_=p.route.loader&&!x.hasOwnProperty(p.route.id)&&(!b||b[p.route.id]===void 0);if(p.route.lazy||_){m=!0,g>=0?d=d.slice(0,g+1):d=[d[0]];break}}}return d.reduceRight((v,p,x)=>{let b,_=!1,U=null,C=null;u&&(b=f&&p.route.id?f[p.route.id]:void 0,U=p.route.errorElement||b0,m&&(g<0&&x===0?(jm("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),_=!0,C=null):g===x&&(_=!0,C=p.route.hydrateFallbackElement||null)));let D=r.concat(d.slice(0,x+1)),M=()=>{let O;return b?O=U:_?O=C:p.route.Component?O=E.createElement(p.route.Component,null):p.route.element?O=p.route.element:O=v,E.createElement(S0,{match:p,routeContext:{outlet:v,matches:D,isDataRoute:u!=null},children:O})};return u&&(p.route.ErrorBoundary||p.route.errorElement||x===0)?E.createElement(j0,{location:u.location,revalidation:u.revalidation,component:U,error:b,children:M(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):M()},null)}function Mc(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function T0(n){let r=E.useContext(Zl);return Be(r,Mc(n)),r}function E0(n){let r=E.useContext(Di);return Be(r,Mc(n)),r}function _0(n){let r=E.useContext(Qt);return Be(r,Mc(n)),r}function zc(n){let r=_0(n),u=r.matches[r.matches.length-1];return Be(u.route.id,`${n} can only be used on routes that contain a unique "id"`),u.route.id}function w0(){return zc("useRouteId")}function C0(){var c;let n=E.useContext(Dc),r=E0("useRouteError"),u=zc("useRouteError");return n!==void 0?n:(c=r.errors)==null?void 0:c[u]}function R0(){let{router:n}=T0("useNavigate"),r=zc("useNavigate"),u=E.useRef(!1);return vm(()=>{u.current=!0}),E.useCallback(async(d,f={})=>{Ut(u.current,gm),u.current&&(typeof d=="number"?n.navigate(d):await n.navigate(d,{fromRouteId:r,...f}))},[n,r])}var Kh={};function jm(n,r,u){!r&&!Kh[n]&&(Kh[n]=!0,Ut(!1,u))}E.memo(A0);function A0({routes:n,future:r,state:u}){return bm(n,void 0,u,r)}function O0({to:n,replace:r,state:u,relative:c}){Be(Fl(),"<Navigate> may be used only in the context of a <Router> component.");let{static:d}=E.useContext(Bt);Ut(!d,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=E.useContext(Qt),{pathname:m}=Ba(),g=xm(),v=Oc(n,Ac(f),m,c==="path"),p=JSON.stringify(v);return E.useEffect(()=>{g(JSON.parse(p),{replace:r,state:u,relative:c})},[g,p,c,r,u]),null}function St(n){Be(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function D0({basename:n="/",children:r=null,location:u,navigationType:c="POP",navigator:d,static:f=!1}){Be(!Fl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let m=n.replace(/^\/*/,"/"),g=E.useMemo(()=>({basename:m,navigator:d,static:f,future:{}}),[m,d,f]);typeof u=="string"&&(u=Ql(u));let{pathname:v="/",search:p="",hash:x="",state:b=null,key:_="default"}=u,U=E.useMemo(()=>{let C=ca(v,m);return C==null?null:{location:{pathname:C,search:p,hash:x,state:b,key:_},navigationType:c}},[m,v,p,x,b,_,c]);return Ut(U!=null,`<Router basename="${m}"> is not able to match the URL "${v}${p}${x}" because it does not start with the basename, so the <Router> won't render anything.`),U==null?null:E.createElement(Bt.Provider,{value:g},E.createElement(as.Provider,{children:r,value:U}))}function M0({children:n,location:r}){return v0(vc(n),r)}function vc(n,r=[]){let u=[];return E.Children.forEach(n,(c,d)=>{if(!E.isValidElement(c))return;let f=[...r,d];if(c.type===E.Fragment){u.push.apply(u,vc(c.props.children,f));return}Be(c.type===St,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Be(!c.props.index||!c.props.children,"An index route cannot have child routes.");let m={id:c.props.id||f.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(m.children=vc(c.props.children,f)),u.push(m)}),u}var Ti="get",Ei="application/x-www-form-urlencoded";function Mi(n){return n!=null&&typeof n.tagName=="string"}function z0(n){return Mi(n)&&n.tagName.toLowerCase()==="button"}function U0(n){return Mi(n)&&n.tagName.toLowerCase()==="form"}function B0(n){return Mi(n)&&n.tagName.toLowerCase()==="input"}function L0(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function H0(n,r){return n.button===0&&(!r||r==="_self")&&!L0(n)}var Ni=null;function k0(){if(Ni===null)try{new FormData(document.createElement("form"),0),Ni=!1}catch{Ni=!0}return Ni}var q0=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function hc(n){return n!=null&&!q0.has(n)?(Ut(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ei}"`),null):n}function Y0(n,r){let u,c,d,f,m;if(U0(n)){let g=n.getAttribute("action");c=g?ca(g,r):null,u=n.getAttribute("method")||Ti,d=hc(n.getAttribute("enctype"))||Ei,f=new FormData(n)}else if(z0(n)||B0(n)&&(n.type==="submit"||n.type==="image")){let g=n.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=n.getAttribute("formaction")||g.getAttribute("action");if(c=v?ca(v,r):null,u=n.getAttribute("formmethod")||g.getAttribute("method")||Ti,d=hc(n.getAttribute("formenctype"))||hc(g.getAttribute("enctype"))||Ei,f=new FormData(g,n),!k0()){let{name:p,type:x,value:b}=n;if(x==="image"){let _=p?`${p}.`:"";f.append(`${_}x`,"0"),f.append(`${_}y`,"0")}else p&&f.append(p,b)}}else{if(Mi(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');u=Ti,c=null,d=Ei,m=n}return f&&d==="text/plain"&&(m=f,f=void 0),{action:c,method:u.toLowerCase(),encType:d,formData:f,body:m}}function Uc(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}async function G0(n,r){if(n.id in r)return r[n.id];try{let u=await import(n.module);return r[n.id]=u,u}catch(u){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(u),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function X0(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function V0(n,r,u){let c=await Promise.all(n.map(async d=>{let f=r.routes[d.route.id];if(f){let m=await G0(f,u);return m.links?m.links():[]}return[]}));return K0(c.flat(1).filter(X0).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function Jh(n,r,u,c,d,f){let m=(v,p)=>u[p]?v.route.id!==u[p].route.id:!0,g=(v,p)=>{var x;return u[p].pathname!==v.pathname||((x=u[p].route.path)==null?void 0:x.endsWith("*"))&&u[p].params["*"]!==v.params["*"]};return f==="assets"?r.filter((v,p)=>m(v,p)||g(v,p)):f==="data"?r.filter((v,p)=>{var b;let x=c.routes[v.route.id];if(!x||!x.hasLoader)return!1;if(m(v,p)||g(v,p))return!0;if(v.route.shouldRevalidate){let _=v.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((b=u[0])==null?void 0:b.params)||{},nextUrl:new URL(n,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof _=="boolean")return _}return!0}):[]}function Q0(n,r,{includeHydrateFallback:u}={}){return Z0(n.map(c=>{let d=r.routes[c.route.id];if(!d)return[];let f=[d.module];return d.clientActionModule&&(f=f.concat(d.clientActionModule)),d.clientLoaderModule&&(f=f.concat(d.clientLoaderModule)),u&&d.hydrateFallbackModule&&(f=f.concat(d.hydrateFallbackModule)),d.imports&&(f=f.concat(d.imports)),f}).flat(1))}function Z0(n){return[...new Set(n)]}function F0(n){let r={},u=Object.keys(n).sort();for(let c of u)r[c]=n[c];return r}function K0(n,r){let u=new Set;return new Set(r),n.reduce((c,d)=>{let f=JSON.stringify(F0(d));return u.has(f)||(u.add(f),c.push({key:f,link:d})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var J0=new Set([100,101,204,205]);function $0(n,r){let u=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return u.pathname==="/"?u.pathname="_root.data":r&&ca(u.pathname,r)==="/"?u.pathname=`${r.replace(/\/$/,"")}/_root.data`:u.pathname=`${u.pathname.replace(/\/$/,"")}.data`,u}function Sm(){let n=E.useContext(Zl);return Uc(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function W0(){let n=E.useContext(Di);return Uc(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Bc=E.createContext(void 0);Bc.displayName="FrameworkContext";function Nm(){let n=E.useContext(Bc);return Uc(n,"You must render this element inside a <HydratedRouter> element"),n}function P0(n,r){let u=E.useContext(Bc),[c,d]=E.useState(!1),[f,m]=E.useState(!1),{onFocus:g,onBlur:v,onMouseEnter:p,onMouseLeave:x,onTouchStart:b}=r,_=E.useRef(null);E.useEffect(()=>{if(n==="render"&&m(!0),n==="viewport"){let D=O=>{O.forEach(R=>{m(R.isIntersecting)})},M=new IntersectionObserver(D,{threshold:.5});return _.current&&M.observe(_.current),()=>{M.disconnect()}}},[n]),E.useEffect(()=>{if(c){let D=setTimeout(()=>{m(!0)},100);return()=>{clearTimeout(D)}}},[c]);let U=()=>{d(!0)},C=()=>{d(!1),m(!1)};return u?n!=="intent"?[f,_,{}]:[f,_,{onFocus:Wn(g,U),onBlur:Wn(v,C),onMouseEnter:Wn(p,U),onMouseLeave:Wn(x,C),onTouchStart:Wn(b,U)}]:[!1,_,{}]}function Wn(n,r){return u=>{n&&n(u),u.defaultPrevented||r(u)}}function I0({page:n,...r}){let{router:u}=Sm(),c=E.useMemo(()=>fm(u.routes,n,u.basename),[u.routes,n,u.basename]);return c?E.createElement(tv,{page:n,matches:c,...r}):null}function ev(n){let{manifest:r,routeModules:u}=Nm(),[c,d]=E.useState([]);return E.useEffect(()=>{let f=!1;return V0(n,r,u).then(m=>{f||d(m)}),()=>{f=!0}},[n,r,u]),c}function tv({page:n,matches:r,...u}){let c=Ba(),{manifest:d,routeModules:f}=Nm(),{basename:m}=Sm(),{loaderData:g,matches:v}=W0(),p=E.useMemo(()=>Jh(n,r,v,d,c,"data"),[n,r,v,d,c]),x=E.useMemo(()=>Jh(n,r,v,d,c,"assets"),[n,r,v,d,c]),b=E.useMemo(()=>{if(n===c.pathname+c.search+c.hash)return[];let C=new Set,D=!1;if(r.forEach(O=>{var T;let R=d.routes[O.route.id];!R||!R.hasLoader||(!p.some(N=>N.route.id===O.route.id)&&O.route.id in g&&((T=f[O.route.id])!=null&&T.shouldRevalidate)||R.hasClientLoader?D=!0:C.add(O.route.id))}),C.size===0)return[];let M=$0(n,m);return D&&C.size>0&&M.searchParams.set("_routes",r.filter(O=>C.has(O.route.id)).map(O=>O.route.id).join(",")),[M.pathname+M.search]},[m,g,c,d,p,r,n,f]),_=E.useMemo(()=>Q0(x,d),[x,d]),U=ev(x);return E.createElement(E.Fragment,null,b.map(C=>E.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...u})),_.map(C=>E.createElement("link",{key:C,rel:"modulepreload",href:C,...u})),U.map(({key:C,link:D})=>E.createElement("link",{key:C,...D})))}function av(...n){return r=>{n.forEach(u=>{typeof u=="function"?u(r):u!=null&&(u.current=r)})}}var Tm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Tm&&(window.__reactRouterVersion="7.6.0")}catch{}function lv({basename:n,children:r,window:u}){let c=E.useRef();c.current==null&&(c.current=Xg({window:u,v5Compat:!0}));let d=c.current,[f,m]=E.useState({action:d.action,location:d.location}),g=E.useCallback(v=>{E.startTransition(()=>m(v))},[m]);return E.useLayoutEffect(()=>d.listen(g),[d,g]),E.createElement(D0,{basename:n,children:r,location:f.location,navigationType:f.action,navigator:d})}var Em=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,_m=E.forwardRef(function({onClick:r,discover:u="render",prefetch:c="none",relative:d,reloadDocument:f,replace:m,state:g,target:v,to:p,preventScrollReset:x,viewTransition:b,..._},U){let{basename:C}=E.useContext(Bt),D=typeof p=="string"&&Em.test(p),M,O=!1;if(typeof p=="string"&&D&&(M=p,Tm))try{let ee=new URL(window.location.href),Q=p.startsWith("//")?new URL(ee.protocol+p):new URL(p),$=ca(Q.pathname,C);Q.origin===ee.origin&&$!=null?p=$+Q.search+Q.hash:O=!0}catch{Ut(!1,`<Link to="${p}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let R=y0(p,{relative:d}),[T,N,w]=P0(c,_),W=iv(p,{replace:m,state:g,target:v,preventScrollReset:x,relative:d,viewTransition:b});function P(ee){r&&r(ee),ee.defaultPrevented||W(ee)}let F=E.createElement("a",{..._,...w,href:M||R,onClick:O||f?r:P,ref:av(U,N),target:v,"data-discover":!D&&u==="render"?"true":void 0});return T&&!D?E.createElement(E.Fragment,null,F,E.createElement(I0,{page:R})):F});_m.displayName="Link";var wm=E.forwardRef(function({"aria-current":r="page",caseSensitive:u=!1,className:c="",end:d=!1,style:f,to:m,viewTransition:g,children:v,...p},x){let b=ls(m,{relative:p.relative}),_=Ba(),U=E.useContext(Di),{navigator:C,basename:D}=E.useContext(Bt),M=U!=null&&dv(b)&&g===!0,O=C.encodeLocation?C.encodeLocation(b).pathname:b.pathname,R=_.pathname,T=U&&U.navigation&&U.navigation.location?U.navigation.location.pathname:null;u||(R=R.toLowerCase(),T=T?T.toLowerCase():null,O=O.toLowerCase()),T&&D&&(T=ca(T,D)||T);const N=O!=="/"&&O.endsWith("/")?O.length-1:O.length;let w=R===O||!d&&R.startsWith(O)&&R.charAt(N)==="/",W=T!=null&&(T===O||!d&&T.startsWith(O)&&T.charAt(O.length)==="/"),P={isActive:w,isPending:W,isTransitioning:M},F=w?r:void 0,ee;typeof c=="function"?ee=c(P):ee=[c,w?"active":null,W?"pending":null,M?"transitioning":null].filter(Boolean).join(" ");let Q=typeof f=="function"?f(P):f;return E.createElement(_m,{...p,"aria-current":F,className:ee,ref:x,style:Q,to:m,viewTransition:g},typeof v=="function"?v(P):v)});wm.displayName="NavLink";var nv=E.forwardRef(({discover:n="render",fetcherKey:r,navigate:u,reloadDocument:c,replace:d,state:f,method:m=Ti,action:g,onSubmit:v,relative:p,preventScrollReset:x,viewTransition:b,..._},U)=>{let C=cv(),D=uv(g,{relative:p}),M=m.toLowerCase()==="get"?"get":"post",O=typeof g=="string"&&Em.test(g),R=T=>{if(v&&v(T),T.defaultPrevented)return;T.preventDefault();let N=T.nativeEvent.submitter,w=(N==null?void 0:N.getAttribute("formmethod"))||m;C(N||T.currentTarget,{fetcherKey:r,method:w,navigate:u,replace:d,state:f,relative:p,preventScrollReset:x,viewTransition:b})};return E.createElement("form",{ref:U,method:M,action:D,onSubmit:c?v:R,..._,"data-discover":!O&&n==="render"?"true":void 0})});nv.displayName="Form";function sv(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Cm(n){let r=E.useContext(Zl);return Be(r,sv(n)),r}function iv(n,{target:r,replace:u,state:c,preventScrollReset:d,relative:f,viewTransition:m}={}){let g=xm(),v=Ba(),p=ls(n,{relative:f});return E.useCallback(x=>{if(H0(x,r)){x.preventDefault();let b=u!==void 0?u:In(v)===In(p);g(n,{replace:b,state:c,preventScrollReset:d,relative:f,viewTransition:m})}},[v,g,p,u,c,r,n,d,f,m])}var rv=0,ov=()=>`__${String(++rv)}__`;function cv(){let{router:n}=Cm("useSubmit"),{basename:r}=E.useContext(Bt),u=w0();return E.useCallback(async(c,d={})=>{let{action:f,method:m,encType:g,formData:v,body:p}=Y0(c,r);if(d.navigate===!1){let x=d.fetcherKey||ov();await n.fetch(x,u,d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:p,formMethod:d.method||m,formEncType:d.encType||g,flushSync:d.flushSync})}else await n.navigate(d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:p,formMethod:d.method||m,formEncType:d.encType||g,replace:d.replace,state:d.state,fromRouteId:u,flushSync:d.flushSync,viewTransition:d.viewTransition})},[n,r,u])}function uv(n,{relative:r}={}){let{basename:u}=E.useContext(Bt),c=E.useContext(Qt);Be(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),f={...ls(n||".",{relative:r})},m=Ba();if(n==null){f.search=m.search;let g=new URLSearchParams(f.search),v=g.getAll("index");if(v.some(x=>x==="")){g.delete("index"),v.filter(b=>b).forEach(b=>g.append("index",b));let x=g.toString();f.search=x?`?${x}`:""}}return(!n||n===".")&&d.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),u!=="/"&&(f.pathname=f.pathname==="/"?u:ra([u,f.pathname])),In(f)}function dv(n,r={}){let u=E.useContext(ym);Be(u!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=Cm("useViewTransitionState"),d=ls(n,{relative:r.relative});if(!u.isTransitioning)return!1;let f=ca(u.currentLocation.pathname,c)||u.currentLocation.pathname,m=ca(u.nextLocation.pathname,c)||u.nextLocation.pathname;return Ri(d.pathname,m)!=null||Ri(d.pathname,f)!=null}[...J0];function Rm(n){var r,u,c="";if(typeof n=="string"||typeof n=="number")c+=n;else if(typeof n=="object")if(Array.isArray(n)){var d=n.length;for(r=0;r<d;r++)n[r]&&(u=Rm(n[r]))&&(c&&(c+=" "),c+=u)}else for(u in n)n[u]&&(c&&(c+=" "),c+=u);return c}function al(){for(var n,r,u=0,c="",d=arguments.length;u<d;u++)(n=arguments[u])&&(r=Rm(n))&&(c&&(c+=" "),c+=r);return c}function fv(n){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],u=document.createElement("style");u.type="text/css",r.firstChild?r.insertBefore(u,r.firstChild):r.appendChild(u),u.styleSheet?u.styleSheet.cssText=n:u.appendChild(document.createTextNode(n))}fv(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var ns=n=>typeof n=="number"&&!isNaN(n),nl=n=>typeof n=="string",ua=n=>typeof n=="function",hv=n=>nl(n)||ns(n),xc=n=>nl(n)||ua(n)?n:null,mv=(n,r)=>n===!1||ns(n)&&n>0?n:r,bc=n=>E.isValidElement(n)||nl(n)||ua(n)||ns(n);function pv(n,r,u=300){let{scrollHeight:c,style:d}=n;requestAnimationFrame(()=>{d.minHeight="initial",d.height=c+"px",d.transition=`all ${u}ms`,requestAnimationFrame(()=>{d.height="0",d.padding="0",d.margin="0",setTimeout(r,u)})})}function yv({enter:n,exit:r,appendPosition:u=!1,collapse:c=!0,collapseDuration:d=300}){return function({children:f,position:m,preventExitTransition:g,done:v,nodeRef:p,isIn:x,playToast:b}){let _=u?`${n}--${m}`:n,U=u?`${r}--${m}`:r,C=E.useRef(0);return E.useLayoutEffect(()=>{let D=p.current,M=_.split(" "),O=R=>{R.target===p.current&&(b(),D.removeEventListener("animationend",O),D.removeEventListener("animationcancel",O),C.current===0&&R.type!=="animationcancel"&&D.classList.remove(...M))};D.classList.add(...M),D.addEventListener("animationend",O),D.addEventListener("animationcancel",O)},[]),E.useEffect(()=>{let D=p.current,M=()=>{D.removeEventListener("animationend",M),c?pv(D,v,d):v()};x||(g?M():(C.current=1,D.className+=` ${U}`,D.addEventListener("animationend",M)))},[x]),Me.createElement(Me.Fragment,null,f)}}function $h(n,r){return{content:Am(n.content,n.props),containerId:n.props.containerId,id:n.props.toastId,theme:n.props.theme,type:n.props.type,data:n.props.data||{},isLoading:n.props.isLoading,icon:n.props.icon,reason:n.removalReason,status:r}}function Am(n,r,u=!1){return E.isValidElement(n)&&!nl(n.type)?E.cloneElement(n,{closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):ua(n)?n({closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):n}function gv({closeToast:n,theme:r,ariaLabel:u="close"}){return Me.createElement("button",{className:`Toastify__close-button Toastify__close-button--${r}`,type:"button",onClick:c=>{c.stopPropagation(),n(!0)},"aria-label":u},Me.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Me.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function vv({delay:n,isRunning:r,closeToast:u,type:c="default",hide:d,className:f,controlledProgress:m,progress:g,rtl:v,isIn:p,theme:x}){let b=d||m&&g===0,_={animationDuration:`${n}ms`,animationPlayState:r?"running":"paused"};m&&(_.transform=`scaleX(${g})`);let U=al("Toastify__progress-bar",m?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${x}`,`Toastify__progress-bar--${c}`,{"Toastify__progress-bar--rtl":v}),C=ua(f)?f({rtl:v,type:c,defaultClassName:U}):al(U,f),D={[m&&g>=1?"onTransitionEnd":"onAnimationEnd"]:m&&g<1?null:()=>{p&&u()}};return Me.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":b},Me.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${x} Toastify__progress-bar--${c}`}),Me.createElement("div",{role:"progressbar","aria-hidden":b?"true":"false","aria-label":"notification timer",className:C,style:_,...D}))}var xv=1,Om=()=>`${xv++}`;function bv(n,r,u){let c=1,d=0,f=[],m=[],g=r,v=new Map,p=new Set,x=R=>(p.add(R),()=>p.delete(R)),b=()=>{m=Array.from(v.values()),p.forEach(R=>R())},_=({containerId:R,toastId:T,updateId:N})=>{let w=R?R!==n:n!==1,W=v.has(T)&&N==null;return w||W},U=(R,T)=>{v.forEach(N=>{var w;(T==null||T===N.props.toastId)&&((w=N.toggle)==null||w.call(N,R))})},C=R=>{var T,N;(N=(T=R.props)==null?void 0:T.onClose)==null||N.call(T,R.removalReason),R.isActive=!1},D=R=>{if(R==null)v.forEach(C);else{let T=v.get(R);T&&C(T)}b()},M=()=>{d-=f.length,f=[]},O=R=>{var T,N;let{toastId:w,updateId:W}=R.props,P=W==null;R.staleId&&v.delete(R.staleId),R.isActive=!0,v.set(w,R),b(),u($h(R,P?"added":"updated")),P&&((N=(T=R.props).onOpen)==null||N.call(T))};return{id:n,props:g,observe:x,toggle:U,removeToast:D,toasts:v,clearQueue:M,buildToast:(R,T)=>{if(_(T))return;let{toastId:N,updateId:w,data:W,staleId:P,delay:F}=T,ee=w==null;ee&&d++;let Q={...g,style:g.toastStyle,key:c++,...Object.fromEntries(Object.entries(T).filter(([ne,ce])=>ce!=null)),toastId:N,updateId:w,data:W,isIn:!1,className:xc(T.className||g.toastClassName),progressClassName:xc(T.progressClassName||g.progressClassName),autoClose:T.isLoading?!1:mv(T.autoClose,g.autoClose),closeToast(ne){v.get(N).removalReason=ne,D(N)},deleteToast(){let ne=v.get(N);if(ne!=null){if(u($h(ne,"removed")),v.delete(N),d--,d<0&&(d=0),f.length>0){O(f.shift());return}b()}}};Q.closeButton=g.closeButton,T.closeButton===!1||bc(T.closeButton)?Q.closeButton=T.closeButton:T.closeButton===!0&&(Q.closeButton=bc(g.closeButton)?g.closeButton:!0);let $={content:R,props:Q,staleId:P};g.limit&&g.limit>0&&d>g.limit&&ee?f.push($):ns(F)?setTimeout(()=>{O($)},F):O($)},setProps(R){g=R},setToggle:(R,T)=>{let N=v.get(R);N&&(N.toggle=T)},isToastActive:R=>{var T;return(T=v.get(R))==null?void 0:T.isActive},getSnapshot:()=>m}}var nt=new Map,es=[],jc=new Set,jv=n=>jc.forEach(r=>r(n)),Dm=()=>nt.size>0;function Sv(){es.forEach(n=>zm(n.content,n.options)),es=[]}var Nv=(n,{containerId:r})=>{var u;return(u=nt.get(r||1))==null?void 0:u.toasts.get(n)};function Mm(n,r){var u;if(r)return!!((u=nt.get(r))!=null&&u.isToastActive(n));let c=!1;return nt.forEach(d=>{d.isToastActive(n)&&(c=!0)}),c}function Tv(n){if(!Dm()){es=es.filter(r=>n!=null&&r.options.toastId!==n);return}if(n==null||hv(n))nt.forEach(r=>{r.removeToast(n)});else if(n&&("containerId"in n||"id"in n)){let r=nt.get(n.containerId);r?r.removeToast(n.id):nt.forEach(u=>{u.removeToast(n.id)})}}var Ev=(n={})=>{nt.forEach(r=>{r.props.limit&&(!n.containerId||r.id===n.containerId)&&r.clearQueue()})};function zm(n,r){bc(n)&&(Dm()||es.push({content:n,options:r}),nt.forEach(u=>{u.buildToast(n,r)}))}function _v(n){var r;(r=nt.get(n.containerId||1))==null||r.setToggle(n.id,n.fn)}function Um(n,r){nt.forEach(u=>{(r==null||!(r!=null&&r.containerId)||(r==null?void 0:r.containerId)===u.id)&&u.toggle(n,r==null?void 0:r.id)})}function wv(n){let r=n.containerId||1;return{subscribe(u){let c=bv(r,n,jv);nt.set(r,c);let d=c.observe(u);return Sv(),()=>{d(),nt.delete(r)}},setProps(u){var c;(c=nt.get(r))==null||c.setProps(u)},getSnapshot(){var u;return(u=nt.get(r))==null?void 0:u.getSnapshot()}}}function Cv(n){return jc.add(n),()=>{jc.delete(n)}}function Rv(n){return n&&(nl(n.toastId)||ns(n.toastId))?n.toastId:Om()}function ss(n,r){return zm(n,r),r.toastId}function zi(n,r){return{...r,type:r&&r.type||n,toastId:Rv(r)}}function Ui(n){return(r,u)=>ss(r,zi(n,u))}function Y(n,r){return ss(n,zi("default",r))}Y.loading=(n,r)=>ss(n,zi("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...r}));function Av(n,{pending:r,error:u,success:c},d){let f;r&&(f=nl(r)?Y.loading(r,d):Y.loading(r.render,{...d,...r}));let m={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},g=(p,x,b)=>{if(x==null){Y.dismiss(f);return}let _={type:p,...m,...d,data:b},U=nl(x)?{render:x}:x;return f?Y.update(f,{..._,...U}):Y(U.render,{..._,...U}),b},v=ua(n)?n():n;return v.then(p=>g("success",c,p)).catch(p=>g("error",u,p)),v}Y.promise=Av;Y.success=Ui("success");Y.info=Ui("info");Y.error=Ui("error");Y.warning=Ui("warning");Y.warn=Y.warning;Y.dark=(n,r)=>ss(n,zi("default",{theme:"dark",...r}));function Ov(n){Tv(n)}Y.dismiss=Ov;Y.clearWaitingQueue=Ev;Y.isActive=Mm;Y.update=(n,r={})=>{let u=Nv(n,r);if(u){let{props:c,content:d}=u,f={delay:100,...c,...r,toastId:r.toastId||n,updateId:Om()};f.toastId!==n&&(f.staleId=n);let m=f.render||d;delete f.render,ss(m,f)}};Y.done=n=>{Y.update(n,{progress:1})};Y.onChange=Cv;Y.play=n=>Um(!0,n);Y.pause=n=>Um(!1,n);function Dv(n){var r;let{subscribe:u,getSnapshot:c,setProps:d}=E.useRef(wv(n)).current;d(n);let f=(r=E.useSyncExternalStore(u,c,c))==null?void 0:r.slice();function m(g){if(!f)return[];let v=new Map;return n.newestOnTop&&f.reverse(),f.forEach(p=>{let{position:x}=p.props;v.has(x)||v.set(x,[]),v.get(x).push(p)}),Array.from(v,p=>g(p[0],p[1]))}return{getToastToRender:m,isToastActive:Mm,count:f==null?void 0:f.length}}function Mv(n){let[r,u]=E.useState(!1),[c,d]=E.useState(!1),f=E.useRef(null),m=E.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:g,pauseOnHover:v,closeToast:p,onClick:x,closeOnClick:b}=n;_v({id:n.toastId,containerId:n.containerId,fn:u}),E.useEffect(()=>{if(n.pauseOnFocusLoss)return _(),()=>{U()}},[n.pauseOnFocusLoss]);function _(){document.hasFocus()||O(),window.addEventListener("focus",M),window.addEventListener("blur",O)}function U(){window.removeEventListener("focus",M),window.removeEventListener("blur",O)}function C(P){if(n.draggable===!0||n.draggable===P.pointerType){R();let F=f.current;m.canCloseOnClick=!0,m.canDrag=!0,F.style.transition="none",n.draggableDirection==="x"?(m.start=P.clientX,m.removalDistance=F.offsetWidth*(n.draggablePercent/100)):(m.start=P.clientY,m.removalDistance=F.offsetHeight*(n.draggablePercent===80?n.draggablePercent*1.5:n.draggablePercent)/100)}}function D(P){let{top:F,bottom:ee,left:Q,right:$}=f.current.getBoundingClientRect();P.nativeEvent.type!=="touchend"&&n.pauseOnHover&&P.clientX>=Q&&P.clientX<=$&&P.clientY>=F&&P.clientY<=ee?O():M()}function M(){u(!0)}function O(){u(!1)}function R(){m.didMove=!1,document.addEventListener("pointermove",N),document.addEventListener("pointerup",w)}function T(){document.removeEventListener("pointermove",N),document.removeEventListener("pointerup",w)}function N(P){let F=f.current;if(m.canDrag&&F){m.didMove=!0,r&&O(),n.draggableDirection==="x"?m.delta=P.clientX-m.start:m.delta=P.clientY-m.start,m.start!==P.clientX&&(m.canCloseOnClick=!1);let ee=n.draggableDirection==="x"?`${m.delta}px, var(--y)`:`0, calc(${m.delta}px + var(--y))`;F.style.transform=`translate3d(${ee},0)`,F.style.opacity=`${1-Math.abs(m.delta/m.removalDistance)}`}}function w(){T();let P=f.current;if(m.canDrag&&m.didMove&&P){if(m.canDrag=!1,Math.abs(m.delta)>m.removalDistance){d(!0),n.closeToast(!0),n.collapseAll();return}P.style.transition="transform 0.2s, opacity 0.2s",P.style.removeProperty("transform"),P.style.removeProperty("opacity")}}let W={onPointerDown:C,onPointerUp:D};return g&&v&&(W.onMouseEnter=O,n.stacked||(W.onMouseLeave=M)),b&&(W.onClick=P=>{x&&x(P),m.canCloseOnClick&&p(!0)}),{playToast:M,pauseToast:O,isRunning:r,preventExitTransition:c,toastRef:f,eventHandlers:W}}var zv=typeof window<"u"?E.useLayoutEffect:E.useEffect,Bi=({theme:n,type:r,isLoading:u,...c})=>Me.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:n==="colored"?"currentColor":`var(--toastify-icon-color-${r})`,...c});function Uv(n){return Me.createElement(Bi,{...n},Me.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function Bv(n){return Me.createElement(Bi,{...n},Me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function Lv(n){return Me.createElement(Bi,{...n},Me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function Hv(n){return Me.createElement(Bi,{...n},Me.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function kv(){return Me.createElement("div",{className:"Toastify__spinner"})}var Sc={info:Bv,warning:Uv,success:Lv,error:Hv,spinner:kv},qv=n=>n in Sc;function Yv({theme:n,type:r,isLoading:u,icon:c}){let d=null,f={theme:n,type:r};return c===!1||(ua(c)?d=c({...f,isLoading:u}):E.isValidElement(c)?d=E.cloneElement(c,f):u?d=Sc.spinner():qv(r)&&(d=Sc[r](f))),d}var Gv=n=>{let{isRunning:r,preventExitTransition:u,toastRef:c,eventHandlers:d,playToast:f}=Mv(n),{closeButton:m,children:g,autoClose:v,onClick:p,type:x,hideProgressBar:b,closeToast:_,transition:U,position:C,className:D,style:M,progressClassName:O,updateId:R,role:T,progress:N,rtl:w,toastId:W,deleteToast:P,isIn:F,isLoading:ee,closeOnClick:Q,theme:$,ariaLabel:ne}=n,ce=al("Toastify__toast",`Toastify__toast-theme--${$}`,`Toastify__toast--${x}`,{"Toastify__toast--rtl":w},{"Toastify__toast--close-on-click":Q}),re=ua(D)?D({rtl:w,position:C,type:x,defaultClassName:ce}):al(ce,D),ye=Yv(n),X=!!N||!v,te={closeToast:_,type:x,theme:$},q=null;return m===!1||(ua(m)?q=m(te):E.isValidElement(m)?q=E.cloneElement(m,te):q=gv(te)),Me.createElement(U,{isIn:F,done:P,position:C,preventExitTransition:u,nodeRef:c,playToast:f},Me.createElement("div",{id:W,tabIndex:0,onClick:p,"data-in":F,className:re,...d,style:M,ref:c,...F&&{role:T,"aria-label":ne}},ye!=null&&Me.createElement("div",{className:al("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!ee})},ye),Am(g,n,!r),q,!n.customProgressBar&&Me.createElement(vv,{...R&&!X?{key:`p-${R}`}:{},rtl:w,theme:$,delay:v,isRunning:r,isIn:F,closeToast:_,hide:b,type:x,className:O,controlledProgress:X,progress:N||0})))},Xv=(n,r=!1)=>({enter:`Toastify--animate Toastify__${n}-enter`,exit:`Toastify--animate Toastify__${n}-exit`,appendPosition:r}),Vv=yv(Xv("bounce",!0)),Qv={position:"top-right",transition:Vv,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:n=>n.altKey&&n.code==="KeyT"};function Zv(n){let r={...Qv,...n},u=n.stacked,[c,d]=E.useState(!0),f=E.useRef(null),{getToastToRender:m,isToastActive:g,count:v}=Dv(r),{className:p,style:x,rtl:b,containerId:_,hotKeys:U}=r;function C(M){let O=al("Toastify__toast-container",`Toastify__toast-container--${M}`,{"Toastify__toast-container--rtl":b});return ua(p)?p({position:M,rtl:b,defaultClassName:O}):al(O,xc(p))}function D(){u&&(d(!0),Y.play())}return zv(()=>{var M;if(u){let O=f.current.querySelectorAll('[data-in="true"]'),R=12,T=(M=r.position)==null?void 0:M.includes("top"),N=0,w=0;Array.from(O).reverse().forEach((W,P)=>{let F=W;F.classList.add("Toastify__toast--stacked"),P>0&&(F.dataset.collapsed=`${c}`),F.dataset.pos||(F.dataset.pos=T?"top":"bot");let ee=N*(c?.2:1)+(c?0:R*P);F.style.setProperty("--y",`${T?ee:ee*-1}px`),F.style.setProperty("--g",`${R}`),F.style.setProperty("--s",`${1-(c?w:0)}`),N+=F.offsetHeight,w+=.025})}},[c,v,u]),E.useEffect(()=>{function M(O){var R;let T=f.current;U(O)&&((R=T.querySelector('[tabIndex="0"]'))==null||R.focus(),d(!1),Y.pause()),O.key==="Escape"&&(document.activeElement===T||T!=null&&T.contains(document.activeElement))&&(d(!0),Y.play())}return document.addEventListener("keydown",M),()=>{document.removeEventListener("keydown",M)}},[U]),Me.createElement("section",{ref:f,className:"Toastify",id:_,onMouseEnter:()=>{u&&(d(!1),Y.pause())},onMouseLeave:D,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":r["aria-label"]},m((M,O)=>{let R=O.length?{...x}:{...x,pointerEvents:"none"};return Me.createElement("div",{tabIndex:-1,className:C(M),"data-stacked":u,style:R,key:`c-${M}`},O.map(({content:T,props:N})=>Me.createElement(Gv,{...N,stacked:u,collapseAll:D,isIn:g(N.toastId,N.containerId),key:`t-${N.key}`},T)))}))}const Fv=({setSidebarOpen:n})=>{const r=()=>{n(u=>!u)};return s.jsx("nav",{className:"navbar",children:s.jsxs("div",{className:"navbar-content",children:[s.jsxs("div",{className:"navbar-left",children:[s.jsx("button",{className:"sidebar-toggle",onClick:r,"aria-label":"Toggle sidebar",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),s.jsx("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),s.jsx("line",{x1:"3",y1:"18",x2:"21",y2:"18"})]})}),s.jsx("div",{className:"navbar-brand",children:s.jsx("h1",{children:"EatZone Admin"})})]}),s.jsx("div",{className:"navbar-right",children:s.jsxs("div",{className:"admin-profile",children:[s.jsx("div",{className:"admin-avatar",children:s.jsx("span",{children:"A"})}),s.jsx("span",{className:"admin-name",children:"Admin"})]})})]})})},Kv=({isOpen:n,setSidebarOpen:r})=>{const u=[{path:"/dashboard",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("rect",{x:"3",y:"3",width:"7",height:"7"}),s.jsx("rect",{x:"14",y:"3",width:"7",height:"7"}),s.jsx("rect",{x:"14",y:"14",width:"7",height:"7"}),s.jsx("rect",{x:"3",y:"14",width:"7",height:"7"})]}),label:"Dashboard",description:"Overview & Analytics"},{path:"/add",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),s.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),label:"Add Food Items",description:"Create new menu items"},{path:"/list",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M9 12l2 2 4-4"}),s.jsx("path",{d:"M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"}),s.jsx("path",{d:"M21 19c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"})]}),label:"List Items",description:"Manage menu items"},{path:"/categories",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("rect",{x:"3",y:"3",width:"7",height:"7"}),s.jsx("rect",{x:"14",y:"3",width:"7",height:"7"}),s.jsx("rect",{x:"14",y:"14",width:"7",height:"7"}),s.jsx("rect",{x:"3",y:"14",width:"7",height:"7"})]}),label:"Food Categories",description:"Manage food categories"},{path:"/add-restaurant",icon:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),label:"Add Restaurant",description:"Create new restaurant"},{path:"/restaurant-list",icon:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),label:"Restaurant List",description:"Manage restaurants"},{path:"/orders",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),label:"Orders",description:"Manage customer orders"},{path:"/analytics",icon:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("polyline",{points:"22,12 18,12 15,21 9,3 6,12 2,12"})}),label:"Analytics",description:"Sales & performance"},{path:"/delivery-partners",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"9",cy:"7",r:"4"}),s.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),s.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]}),label:"Delivery Partners",description:"Manage delivery team"},{path:"/feedback",icon:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})}),label:"Feedback & Complaints",description:"Customer feedback"},{path:"/debug",icon:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M12 2L2 7l10 5 10-5-10-5z"}),s.jsx("path",{d:"M2 17l10 5 10-5"}),s.jsx("path",{d:"M2 12l10 5 10-5"})]}),label:"API Debug",description:"Debug API connections"}];return s.jsxs(s.Fragment,{children:[isMobileOpen&&s.jsx("div",{className:"mobile-overlay active",onClick:onMobileClose}),s.jsxs("div",{className:`sidebar ${isMobileOpen?"mobile-open":""}`,children:[s.jsxs("div",{className:"sidebar-header",children:[s.jsx("h3",{children:"Navigation"}),s.jsx("span",{children:"Manage your platform"})]}),s.jsx("div",{className:"sidebar-menu",children:u.map((c,d)=>s.jsxs(wm,{to:c.path,className:({isActive:f})=>`sidebar-option ${f?"active":""}`,"data-tooltip":c.label,onClick:onMobileClose,children:[s.jsx("div",{className:"sidebar-option-icon",children:c.icon}),s.jsxs("div",{className:"sidebar-option-content",children:[s.jsx("span",{className:"sidebar-option-label",children:c.label}),s.jsx("span",{className:"sidebar-option-description",children:c.description})]})]},d))}),s.jsx("div",{className:"sidebar-footer",children:s.jsxs("div",{className:"sidebar-help",children:[s.jsx("div",{className:"help-icon",children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),s.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]})}),s.jsxs("div",{children:[s.jsx("span",{className:"help-title",children:"Need Help?"}),s.jsx("span",{className:"help-subtitle",children:"Contact support"})]})]})})]})]})};function Bm(n,r){return function(){return n.apply(r,arguments)}}const{toString:Jv}=Object.prototype,{getPrototypeOf:Lc}=Object,{iterator:Li,toStringTag:Lm}=Symbol,Hi=(n=>r=>{const u=Jv.call(r);return n[u]||(n[u]=u.slice(8,-1).toLowerCase())})(Object.create(null)),Lt=n=>(n=n.toLowerCase(),r=>Hi(r)===n),ki=n=>r=>typeof r===n,{isArray:Kl}=Array,ts=ki("undefined");function $v(n){return n!==null&&!ts(n)&&n.constructor!==null&&!ts(n.constructor)&&ut(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Hm=Lt("ArrayBuffer");function Wv(n){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(n):r=n&&n.buffer&&Hm(n.buffer),r}const Pv=ki("string"),ut=ki("function"),km=ki("number"),qi=n=>n!==null&&typeof n=="object",Iv=n=>n===!0||n===!1,_i=n=>{if(Hi(n)!=="object")return!1;const r=Lc(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Lm in n)&&!(Li in n)},e1=Lt("Date"),t1=Lt("File"),a1=Lt("Blob"),l1=Lt("FileList"),n1=n=>qi(n)&&ut(n.pipe),s1=n=>{let r;return n&&(typeof FormData=="function"&&n instanceof FormData||ut(n.append)&&((r=Hi(n))==="formdata"||r==="object"&&ut(n.toString)&&n.toString()==="[object FormData]"))},i1=Lt("URLSearchParams"),[r1,o1,c1,u1]=["ReadableStream","Request","Response","Headers"].map(Lt),d1=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function is(n,r,{allOwnKeys:u=!1}={}){if(n===null||typeof n>"u")return;let c,d;if(typeof n!="object"&&(n=[n]),Kl(n))for(c=0,d=n.length;c<d;c++)r.call(null,n[c],c,n);else{const f=u?Object.getOwnPropertyNames(n):Object.keys(n),m=f.length;let g;for(c=0;c<m;c++)g=f[c],r.call(null,n[g],g,n)}}function qm(n,r){r=r.toLowerCase();const u=Object.keys(n);let c=u.length,d;for(;c-- >0;)if(d=u[c],r===d.toLowerCase())return d;return null}const tl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ym=n=>!ts(n)&&n!==tl;function Nc(){const{caseless:n}=Ym(this)&&this||{},r={},u=(c,d)=>{const f=n&&qm(r,d)||d;_i(r[f])&&_i(c)?r[f]=Nc(r[f],c):_i(c)?r[f]=Nc({},c):Kl(c)?r[f]=c.slice():r[f]=c};for(let c=0,d=arguments.length;c<d;c++)arguments[c]&&is(arguments[c],u);return r}const f1=(n,r,u,{allOwnKeys:c}={})=>(is(r,(d,f)=>{u&&ut(d)?n[f]=Bm(d,u):n[f]=d},{allOwnKeys:c}),n),h1=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),m1=(n,r,u,c)=>{n.prototype=Object.create(r.prototype,c),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:r.prototype}),u&&Object.assign(n.prototype,u)},p1=(n,r,u,c)=>{let d,f,m;const g={};if(r=r||{},n==null)return r;do{for(d=Object.getOwnPropertyNames(n),f=d.length;f-- >0;)m=d[f],(!c||c(m,n,r))&&!g[m]&&(r[m]=n[m],g[m]=!0);n=u!==!1&&Lc(n)}while(n&&(!u||u(n,r))&&n!==Object.prototype);return r},y1=(n,r,u)=>{n=String(n),(u===void 0||u>n.length)&&(u=n.length),u-=r.length;const c=n.indexOf(r,u);return c!==-1&&c===u},g1=n=>{if(!n)return null;if(Kl(n))return n;let r=n.length;if(!km(r))return null;const u=new Array(r);for(;r-- >0;)u[r]=n[r];return u},v1=(n=>r=>n&&r instanceof n)(typeof Uint8Array<"u"&&Lc(Uint8Array)),x1=(n,r)=>{const c=(n&&n[Li]).call(n);let d;for(;(d=c.next())&&!d.done;){const f=d.value;r.call(n,f[0],f[1])}},b1=(n,r)=>{let u;const c=[];for(;(u=n.exec(r))!==null;)c.push(u);return c},j1=Lt("HTMLFormElement"),S1=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(u,c,d){return c.toUpperCase()+d}),Wh=(({hasOwnProperty:n})=>(r,u)=>n.call(r,u))(Object.prototype),N1=Lt("RegExp"),Gm=(n,r)=>{const u=Object.getOwnPropertyDescriptors(n),c={};is(u,(d,f)=>{let m;(m=r(d,f,n))!==!1&&(c[f]=m||d)}),Object.defineProperties(n,c)},T1=n=>{Gm(n,(r,u)=>{if(ut(n)&&["arguments","caller","callee"].indexOf(u)!==-1)return!1;const c=n[u];if(ut(c)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+u+"'")})}})},E1=(n,r)=>{const u={},c=d=>{d.forEach(f=>{u[f]=!0})};return Kl(n)?c(n):c(String(n).split(r)),u},_1=()=>{},w1=(n,r)=>n!=null&&Number.isFinite(n=+n)?n:r;function C1(n){return!!(n&&ut(n.append)&&n[Lm]==="FormData"&&n[Li])}const R1=n=>{const r=new Array(10),u=(c,d)=>{if(qi(c)){if(r.indexOf(c)>=0)return;if(!("toJSON"in c)){r[d]=c;const f=Kl(c)?[]:{};return is(c,(m,g)=>{const v=u(m,d+1);!ts(v)&&(f[g]=v)}),r[d]=void 0,f}}return c};return u(n,0)},A1=Lt("AsyncFunction"),O1=n=>n&&(qi(n)||ut(n))&&ut(n.then)&&ut(n.catch),Xm=((n,r)=>n?setImmediate:r?((u,c)=>(tl.addEventListener("message",({source:d,data:f})=>{d===tl&&f===u&&c.length&&c.shift()()},!1),d=>{c.push(d),tl.postMessage(u,"*")}))(`axios@${Math.random()}`,[]):u=>setTimeout(u))(typeof setImmediate=="function",ut(tl.postMessage)),D1=typeof queueMicrotask<"u"?queueMicrotask.bind(tl):typeof process<"u"&&process.nextTick||Xm,M1=n=>n!=null&&ut(n[Li]),G={isArray:Kl,isArrayBuffer:Hm,isBuffer:$v,isFormData:s1,isArrayBufferView:Wv,isString:Pv,isNumber:km,isBoolean:Iv,isObject:qi,isPlainObject:_i,isReadableStream:r1,isRequest:o1,isResponse:c1,isHeaders:u1,isUndefined:ts,isDate:e1,isFile:t1,isBlob:a1,isRegExp:N1,isFunction:ut,isStream:n1,isURLSearchParams:i1,isTypedArray:v1,isFileList:l1,forEach:is,merge:Nc,extend:f1,trim:d1,stripBOM:h1,inherits:m1,toFlatObject:p1,kindOf:Hi,kindOfTest:Lt,endsWith:y1,toArray:g1,forEachEntry:x1,matchAll:b1,isHTMLForm:j1,hasOwnProperty:Wh,hasOwnProp:Wh,reduceDescriptors:Gm,freezeMethods:T1,toObjectSet:E1,toCamelCase:S1,noop:_1,toFiniteNumber:w1,findKey:qm,global:tl,isContextDefined:Ym,isSpecCompliantForm:C1,toJSONObject:R1,isAsyncFn:A1,isThenable:O1,setImmediate:Xm,asap:D1,isIterable:M1};function pe(n,r,u,c,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",r&&(this.code=r),u&&(this.config=u),c&&(this.request=c),d&&(this.response=d,this.status=d.status?d.status:null)}G.inherits(pe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:G.toJSONObject(this.config),code:this.code,status:this.status}}});const Vm=pe.prototype,Qm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{Qm[n]={value:n}});Object.defineProperties(pe,Qm);Object.defineProperty(Vm,"isAxiosError",{value:!0});pe.from=(n,r,u,c,d,f)=>{const m=Object.create(Vm);return G.toFlatObject(n,m,function(v){return v!==Error.prototype},g=>g!=="isAxiosError"),pe.call(m,n.message,r,u,c,d),m.cause=n,m.name=n.name,f&&Object.assign(m,f),m};const z1=null;function Tc(n){return G.isPlainObject(n)||G.isArray(n)}function Zm(n){return G.endsWith(n,"[]")?n.slice(0,-2):n}function Ph(n,r,u){return n?n.concat(r).map(function(d,f){return d=Zm(d),!u&&f?"["+d+"]":d}).join(u?".":""):r}function U1(n){return G.isArray(n)&&!n.some(Tc)}const B1=G.toFlatObject(G,{},null,function(r){return/^is[A-Z]/.test(r)});function Yi(n,r,u){if(!G.isObject(n))throw new TypeError("target must be an object");r=r||new FormData,u=G.toFlatObject(u,{metaTokens:!0,dots:!1,indexes:!1},!1,function(D,M){return!G.isUndefined(M[D])});const c=u.metaTokens,d=u.visitor||x,f=u.dots,m=u.indexes,v=(u.Blob||typeof Blob<"u"&&Blob)&&G.isSpecCompliantForm(r);if(!G.isFunction(d))throw new TypeError("visitor must be a function");function p(C){if(C===null)return"";if(G.isDate(C))return C.toISOString();if(!v&&G.isBlob(C))throw new pe("Blob is not supported. Use a Buffer instead.");return G.isArrayBuffer(C)||G.isTypedArray(C)?v&&typeof Blob=="function"?new Blob([C]):Buffer.from(C):C}function x(C,D,M){let O=C;if(C&&!M&&typeof C=="object"){if(G.endsWith(D,"{}"))D=c?D:D.slice(0,-2),C=JSON.stringify(C);else if(G.isArray(C)&&U1(C)||(G.isFileList(C)||G.endsWith(D,"[]"))&&(O=G.toArray(C)))return D=Zm(D),O.forEach(function(T,N){!(G.isUndefined(T)||T===null)&&r.append(m===!0?Ph([D],N,f):m===null?D:D+"[]",p(T))}),!1}return Tc(C)?!0:(r.append(Ph(M,D,f),p(C)),!1)}const b=[],_=Object.assign(B1,{defaultVisitor:x,convertValue:p,isVisitable:Tc});function U(C,D){if(!G.isUndefined(C)){if(b.indexOf(C)!==-1)throw Error("Circular reference detected in "+D.join("."));b.push(C),G.forEach(C,function(O,R){(!(G.isUndefined(O)||O===null)&&d.call(r,O,G.isString(R)?R.trim():R,D,_))===!0&&U(O,D?D.concat(R):[R])}),b.pop()}}if(!G.isObject(n))throw new TypeError("data must be an object");return U(n),r}function Ih(n){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(c){return r[c]})}function Hc(n,r){this._pairs=[],n&&Yi(n,this,r)}const Fm=Hc.prototype;Fm.append=function(r,u){this._pairs.push([r,u])};Fm.toString=function(r){const u=r?function(c){return r.call(this,c,Ih)}:Ih;return this._pairs.map(function(d){return u(d[0])+"="+u(d[1])},"").join("&")};function L1(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Km(n,r,u){if(!r)return n;const c=u&&u.encode||L1;G.isFunction(u)&&(u={serialize:u});const d=u&&u.serialize;let f;if(d?f=d(r,u):f=G.isURLSearchParams(r)?r.toString():new Hc(r,u).toString(c),f){const m=n.indexOf("#");m!==-1&&(n=n.slice(0,m)),n+=(n.indexOf("?")===-1?"?":"&")+f}return n}class em{constructor(){this.handlers=[]}use(r,u,c){return this.handlers.push({fulfilled:r,rejected:u,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){G.forEach(this.handlers,function(c){c!==null&&r(c)})}}const Jm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},H1=typeof URLSearchParams<"u"?URLSearchParams:Hc,k1=typeof FormData<"u"?FormData:null,q1=typeof Blob<"u"?Blob:null,Y1={isBrowser:!0,classes:{URLSearchParams:H1,FormData:k1,Blob:q1},protocols:["http","https","file","blob","url","data"]},kc=typeof window<"u"&&typeof document<"u",Ec=typeof navigator=="object"&&navigator||void 0,G1=kc&&(!Ec||["ReactNative","NativeScript","NS"].indexOf(Ec.product)<0),X1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",V1=kc&&window.location.href||"http://localhost",Q1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:kc,hasStandardBrowserEnv:G1,hasStandardBrowserWebWorkerEnv:X1,navigator:Ec,origin:V1},Symbol.toStringTag,{value:"Module"})),at={...Q1,...Y1};function Z1(n,r){return Yi(n,new at.classes.URLSearchParams,Object.assign({visitor:function(u,c,d,f){return at.isNode&&G.isBuffer(u)?(this.append(c,u.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},r))}function F1(n){return G.matchAll(/\w+|\[(\w*)]/g,n).map(r=>r[0]==="[]"?"":r[1]||r[0])}function K1(n){const r={},u=Object.keys(n);let c;const d=u.length;let f;for(c=0;c<d;c++)f=u[c],r[f]=n[f];return r}function $m(n){function r(u,c,d,f){let m=u[f++];if(m==="__proto__")return!0;const g=Number.isFinite(+m),v=f>=u.length;return m=!m&&G.isArray(d)?d.length:m,v?(G.hasOwnProp(d,m)?d[m]=[d[m],c]:d[m]=c,!g):((!d[m]||!G.isObject(d[m]))&&(d[m]=[]),r(u,c,d[m],f)&&G.isArray(d[m])&&(d[m]=K1(d[m])),!g)}if(G.isFormData(n)&&G.isFunction(n.entries)){const u={};return G.forEachEntry(n,(c,d)=>{r(F1(c),d,u,0)}),u}return null}function J1(n,r,u){if(G.isString(n))try{return(r||JSON.parse)(n),G.trim(n)}catch(c){if(c.name!=="SyntaxError")throw c}return(u||JSON.stringify)(n)}const rs={transitional:Jm,adapter:["xhr","http","fetch"],transformRequest:[function(r,u){const c=u.getContentType()||"",d=c.indexOf("application/json")>-1,f=G.isObject(r);if(f&&G.isHTMLForm(r)&&(r=new FormData(r)),G.isFormData(r))return d?JSON.stringify($m(r)):r;if(G.isArrayBuffer(r)||G.isBuffer(r)||G.isStream(r)||G.isFile(r)||G.isBlob(r)||G.isReadableStream(r))return r;if(G.isArrayBufferView(r))return r.buffer;if(G.isURLSearchParams(r))return u.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let g;if(f){if(c.indexOf("application/x-www-form-urlencoded")>-1)return Z1(r,this.formSerializer).toString();if((g=G.isFileList(r))||c.indexOf("multipart/form-data")>-1){const v=this.env&&this.env.FormData;return Yi(g?{"files[]":r}:r,v&&new v,this.formSerializer)}}return f||d?(u.setContentType("application/json",!1),J1(r)):r}],transformResponse:[function(r){const u=this.transitional||rs.transitional,c=u&&u.forcedJSONParsing,d=this.responseType==="json";if(G.isResponse(r)||G.isReadableStream(r))return r;if(r&&G.isString(r)&&(c&&!this.responseType||d)){const m=!(u&&u.silentJSONParsing)&&d;try{return JSON.parse(r)}catch(g){if(m)throw g.name==="SyntaxError"?pe.from(g,pe.ERR_BAD_RESPONSE,this,null,this.response):g}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};G.forEach(["delete","get","head","post","put","patch"],n=>{rs.headers[n]={}});const $1=G.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),W1=n=>{const r={};let u,c,d;return n&&n.split(`
`).forEach(function(m){d=m.indexOf(":"),u=m.substring(0,d).trim().toLowerCase(),c=m.substring(d+1).trim(),!(!u||r[u]&&$1[u])&&(u==="set-cookie"?r[u]?r[u].push(c):r[u]=[c]:r[u]=r[u]?r[u]+", "+c:c)}),r},tm=Symbol("internals");function Pn(n){return n&&String(n).trim().toLowerCase()}function wi(n){return n===!1||n==null?n:G.isArray(n)?n.map(wi):String(n)}function P1(n){const r=Object.create(null),u=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=u.exec(n);)r[c[1]]=c[2];return r}const I1=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function mc(n,r,u,c,d){if(G.isFunction(c))return c.call(this,r,u);if(d&&(r=u),!!G.isString(r)){if(G.isString(c))return r.indexOf(c)!==-1;if(G.isRegExp(c))return c.test(r)}}function ex(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,u,c)=>u.toUpperCase()+c)}function tx(n,r){const u=G.toCamelCase(" "+r);["get","set","has"].forEach(c=>{Object.defineProperty(n,c+u,{value:function(d,f,m){return this[c].call(this,r,d,f,m)},configurable:!0})})}let dt=class{constructor(r){r&&this.set(r)}set(r,u,c){const d=this;function f(g,v,p){const x=Pn(v);if(!x)throw new Error("header name must be a non-empty string");const b=G.findKey(d,x);(!b||d[b]===void 0||p===!0||p===void 0&&d[b]!==!1)&&(d[b||v]=wi(g))}const m=(g,v)=>G.forEach(g,(p,x)=>f(p,x,v));if(G.isPlainObject(r)||r instanceof this.constructor)m(r,u);else if(G.isString(r)&&(r=r.trim())&&!I1(r))m(W1(r),u);else if(G.isObject(r)&&G.isIterable(r)){let g={},v,p;for(const x of r){if(!G.isArray(x))throw TypeError("Object iterator must return a key-value pair");g[p=x[0]]=(v=g[p])?G.isArray(v)?[...v,x[1]]:[v,x[1]]:x[1]}m(g,u)}else r!=null&&f(u,r,c);return this}get(r,u){if(r=Pn(r),r){const c=G.findKey(this,r);if(c){const d=this[c];if(!u)return d;if(u===!0)return P1(d);if(G.isFunction(u))return u.call(this,d,c);if(G.isRegExp(u))return u.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,u){if(r=Pn(r),r){const c=G.findKey(this,r);return!!(c&&this[c]!==void 0&&(!u||mc(this,this[c],c,u)))}return!1}delete(r,u){const c=this;let d=!1;function f(m){if(m=Pn(m),m){const g=G.findKey(c,m);g&&(!u||mc(c,c[g],g,u))&&(delete c[g],d=!0)}}return G.isArray(r)?r.forEach(f):f(r),d}clear(r){const u=Object.keys(this);let c=u.length,d=!1;for(;c--;){const f=u[c];(!r||mc(this,this[f],f,r,!0))&&(delete this[f],d=!0)}return d}normalize(r){const u=this,c={};return G.forEach(this,(d,f)=>{const m=G.findKey(c,f);if(m){u[m]=wi(d),delete u[f];return}const g=r?ex(f):String(f).trim();g!==f&&delete u[f],u[g]=wi(d),c[g]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const u=Object.create(null);return G.forEach(this,(c,d)=>{c!=null&&c!==!1&&(u[d]=r&&G.isArray(c)?c.join(", "):c)}),u}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,u])=>r+": "+u).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...u){const c=new this(r);return u.forEach(d=>c.set(d)),c}static accessor(r){const c=(this[tm]=this[tm]={accessors:{}}).accessors,d=this.prototype;function f(m){const g=Pn(m);c[g]||(tx(d,m),c[g]=!0)}return G.isArray(r)?r.forEach(f):f(r),this}};dt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);G.reduceDescriptors(dt.prototype,({value:n},r)=>{let u=r[0].toUpperCase()+r.slice(1);return{get:()=>n,set(c){this[u]=c}}});G.freezeMethods(dt);function pc(n,r){const u=this||rs,c=r||u,d=dt.from(c.headers);let f=c.data;return G.forEach(n,function(g){f=g.call(u,f,d.normalize(),r?r.status:void 0)}),d.normalize(),f}function Wm(n){return!!(n&&n.__CANCEL__)}function Jl(n,r,u){pe.call(this,n??"canceled",pe.ERR_CANCELED,r,u),this.name="CanceledError"}G.inherits(Jl,pe,{__CANCEL__:!0});function Pm(n,r,u){const c=u.config.validateStatus;!u.status||!c||c(u.status)?n(u):r(new pe("Request failed with status code "+u.status,[pe.ERR_BAD_REQUEST,pe.ERR_BAD_RESPONSE][Math.floor(u.status/100)-4],u.config,u.request,u))}function ax(n){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return r&&r[1]||""}function lx(n,r){n=n||10;const u=new Array(n),c=new Array(n);let d=0,f=0,m;return r=r!==void 0?r:1e3,function(v){const p=Date.now(),x=c[f];m||(m=p),u[d]=v,c[d]=p;let b=f,_=0;for(;b!==d;)_+=u[b++],b=b%n;if(d=(d+1)%n,d===f&&(f=(f+1)%n),p-m<r)return;const U=x&&p-x;return U?Math.round(_*1e3/U):void 0}}function nx(n,r){let u=0,c=1e3/r,d,f;const m=(p,x=Date.now())=>{u=x,d=null,f&&(clearTimeout(f),f=null),n.apply(null,p)};return[(...p)=>{const x=Date.now(),b=x-u;b>=c?m(p,x):(d=p,f||(f=setTimeout(()=>{f=null,m(d)},c-b)))},()=>d&&m(d)]}const Ai=(n,r,u=3)=>{let c=0;const d=lx(50,250);return nx(f=>{const m=f.loaded,g=f.lengthComputable?f.total:void 0,v=m-c,p=d(v),x=m<=g;c=m;const b={loaded:m,total:g,progress:g?m/g:void 0,bytes:v,rate:p||void 0,estimated:p&&g&&x?(g-m)/p:void 0,event:f,lengthComputable:g!=null,[r?"download":"upload"]:!0};n(b)},u)},am=(n,r)=>{const u=n!=null;return[c=>r[0]({lengthComputable:u,total:n,loaded:c}),r[1]]},lm=n=>(...r)=>G.asap(()=>n(...r)),sx=at.hasStandardBrowserEnv?((n,r)=>u=>(u=new URL(u,at.origin),n.protocol===u.protocol&&n.host===u.host&&(r||n.port===u.port)))(new URL(at.origin),at.navigator&&/(msie|trident)/i.test(at.navigator.userAgent)):()=>!0,ix=at.hasStandardBrowserEnv?{write(n,r,u,c,d,f){const m=[n+"="+encodeURIComponent(r)];G.isNumber(u)&&m.push("expires="+new Date(u).toGMTString()),G.isString(c)&&m.push("path="+c),G.isString(d)&&m.push("domain="+d),f===!0&&m.push("secure"),document.cookie=m.join("; ")},read(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function rx(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function ox(n,r){return r?n.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):n}function Im(n,r,u){let c=!rx(r);return n&&(c||u==!1)?ox(n,r):r}const nm=n=>n instanceof dt?{...n}:n;function sl(n,r){r=r||{};const u={};function c(p,x,b,_){return G.isPlainObject(p)&&G.isPlainObject(x)?G.merge.call({caseless:_},p,x):G.isPlainObject(x)?G.merge({},x):G.isArray(x)?x.slice():x}function d(p,x,b,_){if(G.isUndefined(x)){if(!G.isUndefined(p))return c(void 0,p,b,_)}else return c(p,x,b,_)}function f(p,x){if(!G.isUndefined(x))return c(void 0,x)}function m(p,x){if(G.isUndefined(x)){if(!G.isUndefined(p))return c(void 0,p)}else return c(void 0,x)}function g(p,x,b){if(b in r)return c(p,x);if(b in n)return c(void 0,p)}const v={url:f,method:f,data:f,baseURL:m,transformRequest:m,transformResponse:m,paramsSerializer:m,timeout:m,timeoutMessage:m,withCredentials:m,withXSRFToken:m,adapter:m,responseType:m,xsrfCookieName:m,xsrfHeaderName:m,onUploadProgress:m,onDownloadProgress:m,decompress:m,maxContentLength:m,maxBodyLength:m,beforeRedirect:m,transport:m,httpAgent:m,httpsAgent:m,cancelToken:m,socketPath:m,responseEncoding:m,validateStatus:g,headers:(p,x,b)=>d(nm(p),nm(x),b,!0)};return G.forEach(Object.keys(Object.assign({},n,r)),function(x){const b=v[x]||d,_=b(n[x],r[x],x);G.isUndefined(_)&&b!==g||(u[x]=_)}),u}const ep=n=>{const r=sl({},n);let{data:u,withXSRFToken:c,xsrfHeaderName:d,xsrfCookieName:f,headers:m,auth:g}=r;r.headers=m=dt.from(m),r.url=Km(Im(r.baseURL,r.url,r.allowAbsoluteUrls),n.params,n.paramsSerializer),g&&m.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let v;if(G.isFormData(u)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)m.setContentType(void 0);else if((v=m.getContentType())!==!1){const[p,...x]=v?v.split(";").map(b=>b.trim()).filter(Boolean):[];m.setContentType([p||"multipart/form-data",...x].join("; "))}}if(at.hasStandardBrowserEnv&&(c&&G.isFunction(c)&&(c=c(r)),c||c!==!1&&sx(r.url))){const p=d&&f&&ix.read(f);p&&m.set(d,p)}return r},cx=typeof XMLHttpRequest<"u",ux=cx&&function(n){return new Promise(function(u,c){const d=ep(n);let f=d.data;const m=dt.from(d.headers).normalize();let{responseType:g,onUploadProgress:v,onDownloadProgress:p}=d,x,b,_,U,C;function D(){U&&U(),C&&C(),d.cancelToken&&d.cancelToken.unsubscribe(x),d.signal&&d.signal.removeEventListener("abort",x)}let M=new XMLHttpRequest;M.open(d.method.toUpperCase(),d.url,!0),M.timeout=d.timeout;function O(){if(!M)return;const T=dt.from("getAllResponseHeaders"in M&&M.getAllResponseHeaders()),w={data:!g||g==="text"||g==="json"?M.responseText:M.response,status:M.status,statusText:M.statusText,headers:T,config:n,request:M};Pm(function(P){u(P),D()},function(P){c(P),D()},w),M=null}"onloadend"in M?M.onloadend=O:M.onreadystatechange=function(){!M||M.readyState!==4||M.status===0&&!(M.responseURL&&M.responseURL.indexOf("file:")===0)||setTimeout(O)},M.onabort=function(){M&&(c(new pe("Request aborted",pe.ECONNABORTED,n,M)),M=null)},M.onerror=function(){c(new pe("Network Error",pe.ERR_NETWORK,n,M)),M=null},M.ontimeout=function(){let N=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const w=d.transitional||Jm;d.timeoutErrorMessage&&(N=d.timeoutErrorMessage),c(new pe(N,w.clarifyTimeoutError?pe.ETIMEDOUT:pe.ECONNABORTED,n,M)),M=null},f===void 0&&m.setContentType(null),"setRequestHeader"in M&&G.forEach(m.toJSON(),function(N,w){M.setRequestHeader(w,N)}),G.isUndefined(d.withCredentials)||(M.withCredentials=!!d.withCredentials),g&&g!=="json"&&(M.responseType=d.responseType),p&&([_,C]=Ai(p,!0),M.addEventListener("progress",_)),v&&M.upload&&([b,U]=Ai(v),M.upload.addEventListener("progress",b),M.upload.addEventListener("loadend",U)),(d.cancelToken||d.signal)&&(x=T=>{M&&(c(!T||T.type?new Jl(null,n,M):T),M.abort(),M=null)},d.cancelToken&&d.cancelToken.subscribe(x),d.signal&&(d.signal.aborted?x():d.signal.addEventListener("abort",x)));const R=ax(d.url);if(R&&at.protocols.indexOf(R)===-1){c(new pe("Unsupported protocol "+R+":",pe.ERR_BAD_REQUEST,n));return}M.send(f||null)})},dx=(n,r)=>{const{length:u}=n=n?n.filter(Boolean):[];if(r||u){let c=new AbortController,d;const f=function(p){if(!d){d=!0,g();const x=p instanceof Error?p:this.reason;c.abort(x instanceof pe?x:new Jl(x instanceof Error?x.message:x))}};let m=r&&setTimeout(()=>{m=null,f(new pe(`timeout ${r} of ms exceeded`,pe.ETIMEDOUT))},r);const g=()=>{n&&(m&&clearTimeout(m),m=null,n.forEach(p=>{p.unsubscribe?p.unsubscribe(f):p.removeEventListener("abort",f)}),n=null)};n.forEach(p=>p.addEventListener("abort",f));const{signal:v}=c;return v.unsubscribe=()=>G.asap(g),v}},fx=function*(n,r){let u=n.byteLength;if(u<r){yield n;return}let c=0,d;for(;c<u;)d=c+r,yield n.slice(c,d),c=d},hx=async function*(n,r){for await(const u of mx(n))yield*fx(u,r)},mx=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const r=n.getReader();try{for(;;){const{done:u,value:c}=await r.read();if(u)break;yield c}}finally{await r.cancel()}},sm=(n,r,u,c)=>{const d=hx(n,r);let f=0,m,g=v=>{m||(m=!0,c&&c(v))};return new ReadableStream({async pull(v){try{const{done:p,value:x}=await d.next();if(p){g(),v.close();return}let b=x.byteLength;if(u){let _=f+=b;u(_)}v.enqueue(new Uint8Array(x))}catch(p){throw g(p),p}},cancel(v){return g(v),d.return()}},{highWaterMark:2})},Gi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",tp=Gi&&typeof ReadableStream=="function",px=Gi&&(typeof TextEncoder=="function"?(n=>r=>n.encode(r))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),ap=(n,...r)=>{try{return!!n(...r)}catch{return!1}},yx=tp&&ap(()=>{let n=!1;const r=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!r}),im=64*1024,_c=tp&&ap(()=>G.isReadableStream(new Response("").body)),Oi={stream:_c&&(n=>n.body)};Gi&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Oi[r]&&(Oi[r]=G.isFunction(n[r])?u=>u[r]():(u,c)=>{throw new pe(`Response type '${r}' is not supported`,pe.ERR_NOT_SUPPORT,c)})})})(new Response);const gx=async n=>{if(n==null)return 0;if(G.isBlob(n))return n.size;if(G.isSpecCompliantForm(n))return(await new Request(at.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(G.isArrayBufferView(n)||G.isArrayBuffer(n))return n.byteLength;if(G.isURLSearchParams(n)&&(n=n+""),G.isString(n))return(await px(n)).byteLength},vx=async(n,r)=>{const u=G.toFiniteNumber(n.getContentLength());return u??gx(r)},xx=Gi&&(async n=>{let{url:r,method:u,data:c,signal:d,cancelToken:f,timeout:m,onDownloadProgress:g,onUploadProgress:v,responseType:p,headers:x,withCredentials:b="same-origin",fetchOptions:_}=ep(n);p=p?(p+"").toLowerCase():"text";let U=dx([d,f&&f.toAbortSignal()],m),C;const D=U&&U.unsubscribe&&(()=>{U.unsubscribe()});let M;try{if(v&&yx&&u!=="get"&&u!=="head"&&(M=await vx(x,c))!==0){let w=new Request(r,{method:"POST",body:c,duplex:"half"}),W;if(G.isFormData(c)&&(W=w.headers.get("content-type"))&&x.setContentType(W),w.body){const[P,F]=am(M,Ai(lm(v)));c=sm(w.body,im,P,F)}}G.isString(b)||(b=b?"include":"omit");const O="credentials"in Request.prototype;C=new Request(r,{..._,signal:U,method:u.toUpperCase(),headers:x.normalize().toJSON(),body:c,duplex:"half",credentials:O?b:void 0});let R=await fetch(C);const T=_c&&(p==="stream"||p==="response");if(_c&&(g||T&&D)){const w={};["status","statusText","headers"].forEach(ee=>{w[ee]=R[ee]});const W=G.toFiniteNumber(R.headers.get("content-length")),[P,F]=g&&am(W,Ai(lm(g),!0))||[];R=new Response(sm(R.body,im,P,()=>{F&&F(),D&&D()}),w)}p=p||"text";let N=await Oi[G.findKey(Oi,p)||"text"](R,n);return!T&&D&&D(),await new Promise((w,W)=>{Pm(w,W,{data:N,headers:dt.from(R.headers),status:R.status,statusText:R.statusText,config:n,request:C})})}catch(O){throw D&&D(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new pe("Network Error",pe.ERR_NETWORK,n,C),{cause:O.cause||O}):pe.from(O,O&&O.code,n,C)}}),wc={http:z1,xhr:ux,fetch:xx};G.forEach(wc,(n,r)=>{if(n){try{Object.defineProperty(n,"name",{value:r})}catch{}Object.defineProperty(n,"adapterName",{value:r})}});const rm=n=>`- ${n}`,bx=n=>G.isFunction(n)||n===null||n===!1,lp={getAdapter:n=>{n=G.isArray(n)?n:[n];const{length:r}=n;let u,c;const d={};for(let f=0;f<r;f++){u=n[f];let m;if(c=u,!bx(u)&&(c=wc[(m=String(u)).toLowerCase()],c===void 0))throw new pe(`Unknown adapter '${m}'`);if(c)break;d[m||"#"+f]=c}if(!c){const f=Object.entries(d).map(([g,v])=>`adapter ${g} `+(v===!1?"is not supported by the environment":"is not available in the build"));let m=r?f.length>1?`since :
`+f.map(rm).join(`
`):" "+rm(f[0]):"as no adapter specified";throw new pe("There is no suitable adapter to dispatch the request "+m,"ERR_NOT_SUPPORT")}return c},adapters:wc};function yc(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new Jl(null,n)}function om(n){return yc(n),n.headers=dt.from(n.headers),n.data=pc.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),lp.getAdapter(n.adapter||rs.adapter)(n).then(function(c){return yc(n),c.data=pc.call(n,n.transformResponse,c),c.headers=dt.from(c.headers),c},function(c){return Wm(c)||(yc(n),c&&c.response&&(c.response.data=pc.call(n,n.transformResponse,c.response),c.response.headers=dt.from(c.response.headers))),Promise.reject(c)})}const np="1.9.0",Xi={};["object","boolean","number","function","string","symbol"].forEach((n,r)=>{Xi[n]=function(c){return typeof c===n||"a"+(r<1?"n ":" ")+n}});const cm={};Xi.transitional=function(r,u,c){function d(f,m){return"[Axios v"+np+"] Transitional option '"+f+"'"+m+(c?". "+c:"")}return(f,m,g)=>{if(r===!1)throw new pe(d(m," has been removed"+(u?" in "+u:"")),pe.ERR_DEPRECATED);return u&&!cm[m]&&(cm[m]=!0,console.warn(d(m," has been deprecated since v"+u+" and will be removed in the near future"))),r?r(f,m,g):!0}};Xi.spelling=function(r){return(u,c)=>(console.warn(`${c} is likely a misspelling of ${r}`),!0)};function jx(n,r,u){if(typeof n!="object")throw new pe("options must be an object",pe.ERR_BAD_OPTION_VALUE);const c=Object.keys(n);let d=c.length;for(;d-- >0;){const f=c[d],m=r[f];if(m){const g=n[f],v=g===void 0||m(g,f,n);if(v!==!0)throw new pe("option "+f+" must be "+v,pe.ERR_BAD_OPTION_VALUE);continue}if(u!==!0)throw new pe("Unknown option "+f,pe.ERR_BAD_OPTION)}}const Ci={assertOptions:jx,validators:Xi},Vt=Ci.validators;let ll=class{constructor(r){this.defaults=r||{},this.interceptors={request:new em,response:new em}}async request(r,u){try{return await this._request(r,u)}catch(c){if(c instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const f=d.stack?d.stack.replace(/^.+\n/,""):"";try{c.stack?f&&!String(c.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+f):c.stack=f}catch{}}throw c}}_request(r,u){typeof r=="string"?(u=u||{},u.url=r):u=r||{},u=sl(this.defaults,u);const{transitional:c,paramsSerializer:d,headers:f}=u;c!==void 0&&Ci.assertOptions(c,{silentJSONParsing:Vt.transitional(Vt.boolean),forcedJSONParsing:Vt.transitional(Vt.boolean),clarifyTimeoutError:Vt.transitional(Vt.boolean)},!1),d!=null&&(G.isFunction(d)?u.paramsSerializer={serialize:d}:Ci.assertOptions(d,{encode:Vt.function,serialize:Vt.function},!0)),u.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?u.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:u.allowAbsoluteUrls=!0),Ci.assertOptions(u,{baseUrl:Vt.spelling("baseURL"),withXsrfToken:Vt.spelling("withXSRFToken")},!0),u.method=(u.method||this.defaults.method||"get").toLowerCase();let m=f&&G.merge(f.common,f[u.method]);f&&G.forEach(["delete","get","head","post","put","patch","common"],C=>{delete f[C]}),u.headers=dt.concat(m,f);const g=[];let v=!0;this.interceptors.request.forEach(function(D){typeof D.runWhen=="function"&&D.runWhen(u)===!1||(v=v&&D.synchronous,g.unshift(D.fulfilled,D.rejected))});const p=[];this.interceptors.response.forEach(function(D){p.push(D.fulfilled,D.rejected)});let x,b=0,_;if(!v){const C=[om.bind(this),void 0];for(C.unshift.apply(C,g),C.push.apply(C,p),_=C.length,x=Promise.resolve(u);b<_;)x=x.then(C[b++],C[b++]);return x}_=g.length;let U=u;for(b=0;b<_;){const C=g[b++],D=g[b++];try{U=C(U)}catch(M){D.call(this,M);break}}try{x=om.call(this,U)}catch(C){return Promise.reject(C)}for(b=0,_=p.length;b<_;)x=x.then(p[b++],p[b++]);return x}getUri(r){r=sl(this.defaults,r);const u=Im(r.baseURL,r.url,r.allowAbsoluteUrls);return Km(u,r.params,r.paramsSerializer)}};G.forEach(["delete","get","head","options"],function(r){ll.prototype[r]=function(u,c){return this.request(sl(c||{},{method:r,url:u,data:(c||{}).data}))}});G.forEach(["post","put","patch"],function(r){function u(c){return function(f,m,g){return this.request(sl(g||{},{method:r,headers:c?{"Content-Type":"multipart/form-data"}:{},url:f,data:m}))}}ll.prototype[r]=u(),ll.prototype[r+"Form"]=u(!0)});let Sx=class sp{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let u;this.promise=new Promise(function(f){u=f});const c=this;this.promise.then(d=>{if(!c._listeners)return;let f=c._listeners.length;for(;f-- >0;)c._listeners[f](d);c._listeners=null}),this.promise.then=d=>{let f;const m=new Promise(g=>{c.subscribe(g),f=g}).then(d);return m.cancel=function(){c.unsubscribe(f)},m},r(function(f,m,g){c.reason||(c.reason=new Jl(f,m,g),u(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const u=this._listeners.indexOf(r);u!==-1&&this._listeners.splice(u,1)}toAbortSignal(){const r=new AbortController,u=c=>{r.abort(c)};return this.subscribe(u),r.signal.unsubscribe=()=>this.unsubscribe(u),r.signal}static source(){let r;return{token:new sp(function(d){r=d}),cancel:r}}};function Nx(n){return function(u){return n.apply(null,u)}}function Tx(n){return G.isObject(n)&&n.isAxiosError===!0}const Cc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Cc).forEach(([n,r])=>{Cc[r]=n});function ip(n){const r=new ll(n),u=Bm(ll.prototype.request,r);return G.extend(u,ll.prototype,r,{allOwnKeys:!0}),G.extend(u,r,null,{allOwnKeys:!0}),u.create=function(d){return ip(sl(n,d))},u}const me=ip(rs);me.Axios=ll;me.CanceledError=Jl;me.CancelToken=Sx;me.isCancel=Wm;me.VERSION=np;me.toFormData=Yi;me.AxiosError=pe;me.Cancel=me.CanceledError;me.all=function(r){return Promise.all(r)};me.spread=Nx;me.isAxiosError=Tx;me.mergeConfig=sl;me.AxiosHeaders=dt;me.formToJSON=n=>$m(G.isHTMLForm(n)?new FormData(n):n);me.getAdapter=lp.getAdapter;me.HttpStatusCode=Cc;me.default=me;const{Axios:Fx,AxiosError:Kx,CanceledError:Jx,isCancel:$x,CancelToken:Wx,VERSION:Px,all:Ix,Cancel:e2,isAxiosError:t2,spread:a2,toFormData:l2,AxiosHeaders:n2,HttpStatusCode:s2,formToJSON:i2,getAdapter:r2,mergeConfig:o2}=me,um=({url:n})=>{const[r,u]=E.useState({totalOrders:0,totalRevenue:0,totalFoodItems:0,totalUsers:0,todayOrders:0,todayRevenue:0,weekRevenue:0,pendingOrders:0,deliveredOrders:0}),[c,d]=E.useState([]),[f,m]=E.useState([]),[g,v]=E.useState(!0);E.useEffect(()=>{p()},[]);const p=async()=>{try{v(!0);const[_,U,C]=await Promise.all([me.get(`${n}/api/order/list`),me.get(`${n}/api/food/list`),me.get(`${n}/api/user/list`).catch(()=>({data:{success:!1,data:[]}}))]),D=_.data.success?_.data.data:[],M=U.data.success?U.data.data:[],O=C.data.success?C.data.data:[],R=new Date().toDateString(),T=D.filter(re=>new Date(re.date).toDateString()===R),N=new Date;N.setDate(N.getDate()-7);const w=D.filter(re=>new Date(re.date)>=N),W=D.reduce((re,ye)=>re+ye.amount,0),P=T.reduce((re,ye)=>re+ye.amount,0),F=w.reduce((re,ye)=>re+ye.amount,0),ee=D.filter(re=>re.status==="Food Processing"||re.status==="Out for delivery").length,Q=D.filter(re=>re.status==="Delivered").length;u({totalOrders:D.length,totalRevenue:W,totalFoodItems:M.length,totalUsers:O.length,todayOrders:T.length,todayRevenue:P,weekRevenue:F,pendingOrders:ee,deliveredOrders:Q});const $=D.sort((re,ye)=>new Date(ye.date)-new Date(re.date));d($.slice(0,10));const ne={};D.forEach(re=>{var ye;(ye=re.items)==null||ye.forEach(X=>{ne[X._id]=(ne[X._id]||0)+X.quantity})});const ce=M.map(re=>({...re,orderCount:ne[re._id]||0})).sort((re,ye)=>ye.orderCount-re.orderCount).slice(0,5);m(ce)}catch(_){console.error("Error fetching dashboard data:",_)}finally{v(!1)}},x=_=>`₹${_}`,b=({title:_,value:U,icon:C,color:D,change:M})=>s.jsx("div",{className:"stat-card",children:s.jsxs("div",{className:"stat-card-header",children:[s.jsx("div",{className:`stat-icon ${D}`,children:C}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:U}),s.jsx("p",{children:_}),M&&s.jsxs("span",{className:`stat-change ${M.type}`,children:[M.type==="increase"?"↗":"↘"," ",M.value,"%"]})]})]})});return g?s.jsx("div",{className:"dashboard",children:s.jsxs("div",{className:"dashboard-loading",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading dashboard..."})]})}):s.jsxs("div",{className:"dashboard",children:[s.jsxs("div",{className:"dashboard-header",children:[s.jsx("h1",{children:"Dashboard Overview"}),s.jsx("p",{children:"Welcome back! Here's what's happening with your restaurant today."})]}),s.jsxs("div",{className:"stats-grid",children:[s.jsx(b,{title:"Total Orders",value:r.totalOrders,color:"primary",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),change:{type:"increase",value:12}}),s.jsx(b,{title:"Total Revenue",value:x(r.totalRevenue),color:"success",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"12",y1:"1",x2:"12",y2:"23"}),s.jsx("path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})]}),change:{type:"increase",value:8}}),s.jsx(b,{title:"Food Items",value:r.totalFoodItems,color:"warning",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M18 8h1a4 4 0 0 1 0 8h-1"}),s.jsx("path",{d:"M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"}),s.jsx("line",{x1:"6",y1:"1",x2:"6",y2:"4"}),s.jsx("line",{x1:"10",y1:"1",x2:"10",y2:"4"}),s.jsx("line",{x1:"14",y1:"1",x2:"14",y2:"4"})]})}),s.jsx(b,{title:"Today's Orders",value:r.todayOrders,color:"info",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("polyline",{points:"12,6 12,12 16,14"})]}),change:{type:"increase",value:5}}),s.jsx(b,{title:"Total Users",value:r.totalUsers,color:"purple",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"12",cy:"7",r:"4"})]}),change:{type:"increase",value:5}}),s.jsx(b,{title:"Week Revenue",value:x(r.weekRevenue),color:"success",icon:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M3 3v18h18"}),s.jsx("path",{d:"M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"})]}),change:{type:"increase",value:22}})]}),s.jsxs("div",{className:"dashboard-content",children:[s.jsx("div",{className:"dashboard-left",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Recent Orders"}),s.jsx("p",{className:"card-subtitle",children:"Latest customer orders"})]}),s.jsx("div",{className:"recent-orders",children:c.length>0?c.map((_,U)=>s.jsxs("div",{className:"order-item",children:[s.jsxs("div",{className:"order-info",children:[s.jsxs("h4",{children:["Order #",_._id.slice(-6)]}),s.jsxs("p",{children:[_.address.firstName," ",_.address.lastName]}),s.jsx("span",{className:"order-time",children:new Date(_.date).toLocaleDateString()})]}),s.jsxs("div",{className:"order-details",children:[s.jsx("span",{className:"order-amount",children:x(_.amount)}),s.jsx("span",{className:`badge badge-${_.status==="Delivered"?"success":_.status==="Out for delivery"?"warning":"info"}`,children:_.status})]})]},U)):s.jsx("p",{className:"no-data",children:"No recent orders"})})]})}),s.jsxs("div",{className:"dashboard-right",children:[s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Popular Items"}),s.jsx("p",{className:"card-subtitle",children:"Best selling food items"})]}),s.jsx("div",{className:"top-items",children:f.length>0?f.map((_,U)=>s.jsxs("div",{className:"food-item",children:[s.jsx("img",{src:`${n}/images/${_.image}`,alt:_.name,className:"food-image"}),s.jsxs("div",{className:"food-info",children:[s.jsx("h4",{children:_.name}),s.jsx("p",{children:_.category}),s.jsx("span",{className:"food-price",children:x(_.price)})]}),s.jsxs("div",{className:"food-rank",children:["#",U+1]})]},U)):s.jsx("p",{className:"no-data",children:"No food items"})})]}),s.jsxs("div",{className:"card",children:[s.jsx("div",{className:"card-header",children:s.jsx("h3",{className:"card-title",children:"Quick Stats"})}),s.jsxs("div",{className:"quick-stats",children:[s.jsxs("div",{className:"quick-stat",children:[s.jsx("span",{className:"stat-label",children:"Pending Orders"}),s.jsx("span",{className:"stat-value",children:r.pendingOrders})]}),s.jsxs("div",{className:"quick-stat",children:[s.jsx("span",{className:"stat-label",children:"Delivered Today"}),s.jsx("span",{className:"stat-value",children:r.deliveredOrders})]}),s.jsxs("div",{className:"quick-stat",children:[s.jsx("span",{className:"stat-label",children:"Today's Revenue"}),s.jsx("span",{className:"stat-value",children:x(r.todayRevenue)})]})]})]})]})]})]})},qc=async(n,r="eatzone",u={})=>{var c;try{const d=new FormData;d.append("file",n),d.append("upload_preset","eatzone_admin"),r&&d.append("folder",r),u.tags&&d.append("tags",Array.isArray(u.tags)?u.tags.join(","):u.tags),u.transformation&&d.append("transformation",JSON.stringify(u.transformation));const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const v=await f.json();throw new Error(((c=v.error)==null?void 0:c.message)||"Upload failed")}const m=await f.json(),g=m.secure_url.replace("/upload/","/upload/f_auto,q_auto,w_800,h_600,c_fill/");return{success:!0,url:m.secure_url,optimizedUrl:g,publicId:m.public_id}}catch(d){return console.error("Cloudinary upload error:",d),{success:!1,error:d.message}}},oa={food:{folder:"eatzone/food",tags:["food","menu"],transformation:{width:800,height:600,crop:"fill",quality:"auto",format:"auto"}},restaurant:{folder:"eatzone/restaurants",tags:["restaurant","cover"],transformation:{width:1200,height:800,crop:"fill",quality:"auto",format:"auto"}},category:{folder:"eatzone/categories",tags:["category","icon"],transformation:{width:200,height:200,crop:"fill",quality:"auto",format:"auto"}}},Ex=(n,r={})=>{const{maxSize:u=5*1024*1024,allowedTypes:c=["image/jpeg","image/jpg","image/png","image/webp"],minWidth:d=100,minHeight:f=100}=r;return n.size>u?{valid:!1,error:`File size must be less than ${Math.round(u/(1024*1024))}MB`}:c.includes(n.type)?{valid:!0}:{valid:!1,error:`File type must be one of: ${c.join(", ")}`}},_x=({url:n})=>{const[r,u]=E.useState(!1),[c,d]=E.useState(!1),[f,m]=E.useState(""),[g,v]=E.useState([]),[p,x]=E.useState([]),[b,_]=E.useState({name:"",description:"",price:"",category:"",restaurantId:"",discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""});E.useEffect(()=>{U(),C()},[]);const U=async()=>{try{const R=await me.get(`${n}/api/restaurant/list`);R.data.success&&v(R.data.data)}catch(R){console.error("Error fetching restaurants:",R),Y.error("Failed to load restaurants")}},C=async()=>{try{const R=await me.get(`${n}/api/category/list`);R.data.success&&(x(R.data.data),R.data.data.length>0&&!b.category&&_(T=>({...T,category:R.data.data[0].name})))}catch(R){console.error("Error fetching categories:",R),Y.error("Failed to load categories")}},D=R=>{const T=R.target.name,N=R.target.type==="checkbox"?R.target.checked:R.target.value;_(w=>({...w,[T]:N}))},M=async R=>{if(R){d(!0),Y.info("Uploading image to Cloudinary...");try{const T=await qc(R,oa.food.folder,{tags:oa.food.tags,transformation:oa.food.transformation});T.success?(console.log("Cloudinary upload successful:",T.url),m(T.url),u(!1),Y.success("Image uploaded successfully!")):(console.error("Cloudinary upload failed:",T.error),Y.error(T.error||"Failed to upload image"))}catch(T){console.error("Image upload error:",T),Y.error("Failed to upload image")}finally{d(!1)}}},O=async R=>{R.preventDefault();try{if(!f){Y.error("Please upload an image to Cloudinary first");return}if(!b.name||!b.description||!b.price){Y.error("Please fill all required fields");return}if(console.log("Form submission data:",{name:b.name,description:b.description,price:b.price,category:b.category,cloudinaryUrl:f,restaurantId:b.restaurantId}),r&&!f){Y.error("Please upload the image to Cloudinary first by clicking the 'Upload to Cloudinary' button");return}const T=new FormData;T.append("name",b.name),T.append("description",b.description),T.append("price",Number(b.price)),T.append("category",b.category),T.append("restaurantId",b.restaurantId),T.append("image",f),b.discountPercentage&&T.append("discountPercentage",Number(b.discountPercentage)),b.discountLabel&&T.append("discountLabel",b.discountLabel),T.append("isPopular",b.isPopular),T.append("isFeatured",b.isFeatured),b.tags&&T.append("tags",b.tags),Y.info("Adding food item...");const N=await me.post(`${n}/api/food/add`,T);N.data.success?(_({name:"",description:"",price:"",category:p.length>0?p[0].name:"",restaurantId:"",discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),u(!1),m(""),Y.success(N.data.message||"Food item added successfully!")):Y.error(N.data.message||"Failed to add food item")}catch(T){console.error("Error adding food item:",T),Y.error(T.response&&T.response.data&&T.response.data.message||"An error occurred while adding the food item")}};return s.jsx("div",{className:"add",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h2",{className:"card-title",children:"Add New Food Item"}),s.jsx("p",{className:"card-subtitle",children:"Create a new menu item for your restaurant"})]}),s.jsxs("form",{onSubmit:O,children:[s.jsxs("div",{className:"add-img-upload",children:[s.jsx("label",{className:"form-label required",children:"Food Image"}),s.jsxs("label",{htmlFor:"image",children:[f?s.jsx("img",{src:f,alt:"Food preview from Cloudinary",onError:R=>{console.error("Failed to load Cloudinary image:",f),R.target.style.display="none";const T=R.target.nextElementSibling;T&&(T.style.display="block")},onLoad:()=>{console.log("Cloudinary image loaded successfully:",f)}}):r?s.jsx("img",{src:URL.createObjectURL(r),alt:"Food preview",onError:R=>{console.error("Failed to load local image preview"),R.target.style.display="none"}}):s.jsxs("div",{className:"upload-text",children:[s.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),s.jsx("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),s.jsx("polyline",{points:"21,15 16,10 5,21"})]}),s.jsx("h3",{children:"Upload Food Image"}),s.jsx("p",{children:"Click to browse or drag and drop your image here"}),s.jsx("p",{children:"Recommended: 400x400px, JPG or PNG"})]}),f&&s.jsx("div",{className:"error-fallback",style:{display:"none"},children:s.jsxs("div",{className:"upload-text",children:[s.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),s.jsx("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),s.jsx("polyline",{points:"21,15 16,10 5,21"})]}),s.jsx("h3",{children:"Image Upload Complete"}),s.jsx("p",{children:"Image uploaded to Cloudinary but preview failed to load"}),s.jsx("p",{children:"The image will display correctly on the website"})]})})]}),s.jsx("input",{onChange:R=>{const T=R.target.files[0];T&&(console.log("File selected:",T.name,T.size,T.type),u(T),m(""))},type:"file",id:"image",accept:"image/*",hidden:!0,required:!0}),r&&!f&&s.jsx("div",{className:"upload-actions",children:s.jsx("button",{type:"button",onClick:()=>M(r),disabled:c,className:"upload-btn",children:c?"Uploading...":"Upload to Cloudinary"})}),f&&s.jsxs("div",{className:"upload-success",children:[s.jsx("span",{className:"success-text",children:"✅ Image uploaded to Cloudinary successfully!"}),s.jsx("div",{className:"cloudinary-url",children:s.jsxs("small",{children:["URL: ",f]})}),s.jsxs("div",{className:"test-actions",children:[s.jsx("button",{type:"button",onClick:()=>{window.open(f,"_blank")},className:"test-btn",children:"🔗 Test Image URL"}),s.jsx("button",{type:"button",onClick:()=>{const R=document.querySelector(".add-img-upload img");R&&(R.src=f+"?t="+Date.now())},className:"test-btn",children:"🔄 Refresh Preview"})]})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"add-product-name",children:[s.jsx("label",{className:"form-label required",children:"Product Name"}),s.jsx("input",{onChange:D,value:b.name,type:"text",name:"name",placeholder:"Enter food item name",required:!0})]}),s.jsxs("div",{className:"add-category",children:[s.jsx("label",{className:"form-label required",children:"Category"}),s.jsxs("select",{onChange:D,name:"category",value:b.category,required:!0,children:[s.jsx("option",{value:"",children:"Select Category"}),p.map(R=>s.jsx("option",{value:R.name,children:R.name},R._id))]}),p.length===0&&s.jsx("small",{className:"form-help",children:"No categories available. Please add categories first in the Categories section."})]})]}),s.jsx("div",{className:"form-row",children:s.jsxs("div",{className:"add-restaurant",children:[s.jsx("label",{className:"form-label",children:"Restaurant (Optional)"}),s.jsxs("select",{onChange:D,name:"restaurantId",value:b.restaurantId,children:[s.jsx("option",{value:"",children:"Select Restaurant (Optional)"}),g.map(R=>s.jsx("option",{value:R._id,children:R.name},R._id))]}),s.jsx("small",{className:"form-help",children:"Leave empty to add as general food item, or select a restaurant to associate this item with a specific restaurant."})]})}),s.jsxs("div",{className:"add-product-description",children:[s.jsx("label",{className:"form-label required",children:"Description"}),s.jsx("textarea",{onChange:D,value:b.description,name:"description",rows:"4",placeholder:"Describe your food item, ingredients, and special features...",required:!0})]}),s.jsxs("div",{className:"add-category-price",children:[s.jsxs("div",{className:"add-price",children:[s.jsx("label",{className:"form-label required",children:"Price (INR)"}),s.jsx("input",{onChange:D,value:b.price,type:"number",name:"price",placeholder:"₹0",min:"1",required:!0})]}),s.jsxs("div",{className:"add-discount",children:[s.jsx("label",{className:"form-label",children:"Discount (%)"}),s.jsx("input",{onChange:D,value:b.discountPercentage,type:"number",name:"discountPercentage",placeholder:"0",min:"0",max:"100"})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"add-discount-label",children:[s.jsx("label",{className:"form-label",children:"Discount Label"}),s.jsx("input",{onChange:D,value:b.discountLabel,type:"text",name:"discountLabel",placeholder:"e.g., MEGA SALE, LIMITED TIME"})]}),s.jsxs("div",{className:"add-tags",children:[s.jsx("label",{className:"form-label",children:"Tags"}),s.jsx("input",{onChange:D,value:b.tags,type:"text",name:"tags",placeholder:"e.g., Bestseller, New, Spicy (comma separated)"})]})]}),s.jsx("div",{className:"form-row",children:s.jsxs("div",{className:"add-checkboxes",children:[s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",name:"isPopular",checked:b.isPopular,onChange:D}),s.jsx("span",{className:"checkmark"}),"Mark as Popular Item"]}),s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",name:"isFeatured",checked:b.isFeatured,onChange:D}),s.jsx("span",{className:"checkmark"}),"Mark as Featured Item"]})]})}),s.jsx("div",{className:"form-submit",children:s.jsxs("button",{type:"submit",className:"add-btn",children:[s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),s.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),"Add Food Item"]})})]})]})})},wx=(n,r)=>{if(!n)return"/api/placeholder/50/50";const u=String(n);if(u.includes("cloudinary.com")||u.startsWith("http")||u.startsWith("/")||u.startsWith("data:"))return u;if(u.includes(".png")||u.includes(".jpg")||u.includes(".jpeg")){const c=u.startsWith("/")?u.substring(1):u;return`${r}/images/${c}`}return`${r}/images/${u}`},Cx=({url:n})=>{const[r,u]=E.useState([]),[c,d]=E.useState([]),[f,m]=E.useState(!0),[g,v]=E.useState(""),[p,x]=E.useState("all"),[b,_]=E.useState(null),[U,C]=E.useState([]),[D,M]=E.useState([]),[O,R]=E.useState({name:"",description:"",price:"",category:"",restaurantId:"",image:null,discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),[T,N]=E.useState(!1),[w,W]=E.useState(""),P=async()=>{try{console.log("Testing API connection to:",n);const q=await me.get(`${n}/test`);console.log("API test response:",q.data),Y.success("API connection successful!",{autoClose:2e3})}catch(q){console.error("API connection test failed:",q),Y.error("API connection failed. Check console for details.",{autoClose:5e3})}},F=async()=>{var q,se,S;try{m(!0),console.log("Fetching food list from:",`${n}/api/food/list`);const V=await me.get(`${n}/api/food/list`);console.log("Food list response:",V.data),V.data.success?(u(V.data.data),d(V.data.data),console.log("Food items loaded:",V.data.data.length,"items"),Y.success(`Food items loaded successfully (${V.data.data.length} items)`,{autoClose:2e3})):(console.error("Failed to load food items:",V.data.message),Y.error(V.data.message||"Failed to load food items"))}catch(V){console.error("Error fetching food list:",V),console.error("Error details:",(q=V.response)==null?void 0:q.data),Y.error(((S=(se=V.response)==null?void 0:se.data)==null?void 0:S.message)||"An error occurred while fetching food items")}finally{m(!1)}},ee=async()=>{var q;try{console.log("Fetching restaurants from:",`${n}/api/restaurant/list`);const se=await me.get(`${n}/api/restaurant/list`);console.log("Restaurants response:",se.data),se.data.success?(C(se.data.data),console.log("Restaurants loaded:",se.data.data.length,"restaurants")):console.error("Failed to load restaurants:",se.data.message)}catch(se){console.error("Error fetching restaurants:",se),console.error("Error details:",(q=se.response)==null?void 0:q.data)}},Q=async q=>{var se,S;if(window.confirm("Are you sure you want to delete this food item?"))try{Y.info("Removing food item...",{autoClose:1e3});const V=await me.post(`${n}/api/food/remove`,{id:q});V.data.success?(Y.success(V.data.message||"Food item removed successfully"),await F()):Y.error(V.data.message||"Failed to remove food item")}catch(V){console.error("Error removing food item:",V),Y.error(((S=(se=V.response)==null?void 0:se.data)==null?void 0:S.message)||"An error occurred while removing food item")}},$=async()=>{var q,se;if(window.confirm(`⚠️ WARNING: Are you sure you want to delete ALL food items?

This action cannot be undone and will remove all items from your database.`))try{Y.info("Clearing all food items...",{autoClose:2e3});const S=await me.post(`${n}/api/food/clear-all`);S.data.success?(Y.success(S.data.message||"All food items cleared successfully"),await F()):Y.error(S.data.message||"Failed to clear food items")}catch(S){console.error("Error clearing food items:",S),Y.error(((se=(q=S.response)==null?void 0:q.data)==null?void 0:se.message)||"An error occurred while clearing food items")}},ne=q=>{var se,S;_(q),R({name:q.name,description:q.description||"",price:(q.originalPrice||q.price).toString(),category:q.category,restaurantId:((se=q.restaurantId)==null?void 0:se._id)||q.restaurantId||"",image:null,discountPercentage:((S=q.discountPercentage)==null?void 0:S.toString())||"",discountLabel:q.discountLabel||"",isPopular:q.isPopular||!1,isFeatured:q.isFeatured||!1,tags:q.tags?q.tags.join(", "):""}),W(""),N(!1)},ce=()=>{_(null),R({name:"",description:"",price:"",category:"",restaurantId:"",image:null,discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),W(""),N(!1)},re=q=>{const{name:se,value:S,files:V,type:le,checked:ae}=q.target;R(se==="image"?I=>({...I,image:V[0]}):le==="checkbox"?I=>({...I,[se]:ae}):I=>({...I,[se]:S}))},ye=async q=>{if(!q)return;const se=Ex(q);if(!se.valid){Y.error(se.error);return}N(!0),Y.info("Uploading food image to Cloudinary...");try{const S=await qc(q,oa.food.folder,{tags:oa.food.tags,transformation:oa.food.transformation});S.success?(W(S.url),Y.success("Food image uploaded successfully!")):Y.error(S.error||"Failed to upload food image")}catch(S){console.error("Food image upload error:",S),Y.error("Failed to upload food image")}finally{N(!1)}},X=async q=>{var se,S,V,le,ae;if(q.preventDefault(),!O.name||!O.price||!O.category){Y.error("Please fill in all required fields");return}if(isNaN(O.price)||Number(O.price)<=0){Y.error("Please enter a valid price");return}try{console.log("Updating food item:",b._id),console.log("Form data:",O),Y.info("Updating food item...",{autoClose:1e3});const I=new FormData;I.append("id",b._id),I.append("name",O.name.trim()),I.append("description",O.description.trim()),I.append("price",O.price),I.append("category",O.category),O.restaurantId&&O.restaurantId!=="none"?I.append("restaurantId",O.restaurantId):I.append("restaurantId",""),O.discountPercentage?I.append("discountPercentage",Number(O.discountPercentage)):I.append("discountPercentage",0),O.discountLabel&&I.append("discountLabel",O.discountLabel),I.append("isPopular",O.isPopular),I.append("isFeatured",O.isFeatured),O.tags&&I.append("tags",O.tags),w&&(console.log("Using Cloudinary URL:",w),I.append("image",w));for(let he of I.entries())console.log(he[0]+": "+he[1]);const xe=await me.post(`${n}/api/food/update`,I,{headers:{"Content-Type":"multipart/form-data"},timeout:3e4});console.log("Update response:",xe.data),xe.data.success?(Y.success(xe.data.message||"Food item updated successfully"),await F(),ce()):(console.error("Update failed:",xe.data.message),Y.error(xe.data.message||"Failed to update food item"))}catch(I){console.error("Error updating food item:",I),console.error("Error response:",(se=I.response)==null?void 0:se.data),((S=I.response)==null?void 0:S.status)===404?Y.error("Food item not found"):((V=I.response)==null?void 0:V.status)===400?Y.error(I.response.data.message||"Invalid data provided"):(ae=(le=I.response)==null?void 0:le.data)!=null&&ae.message?Y.error(I.response.data.message):Y.error("An error occurred while updating food item")}};E.useEffect(()=>{let q=r;g&&(q=q.filter(se=>se.name.toLowerCase().includes(g.toLowerCase())||se.category.toLowerCase().includes(g.toLowerCase()))),p!=="all"&&(q=q.filter(se=>se.category===p)),d(q)},[r,g,p]),E.useEffect(()=>{P(),F(),ee(),te()},[]);const te=async()=>{try{const q=await me.get(`${n}/api/category/list`);q.data.success&&M(q.data.data)}catch(q){console.error("Error fetching categories:",q)}};return f?s.jsx("div",{className:"list",children:s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading food items..."})]})}):s.jsxs("div",{className:"list",children:[s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"card-title",children:"Food Items"}),s.jsx("p",{className:"card-subtitle",children:"Manage your restaurant menu items"})]}),s.jsxs("div",{className:"card-actions",children:[s.jsx("button",{className:"btn btn-primary",onClick:P,title:"Test API connection",style:{marginRight:"1rem"},children:"🔗 Test API"}),s.jsx("button",{className:"btn btn-danger",onClick:$,title:"Clear all food items from database",children:"🗑️ Clear All Items"})]})]}),s.jsxs("div",{className:"list-stats",children:[s.jsxs("div",{className:"list-stat",children:[s.jsx("span",{className:"list-stat-value",children:r.length}),s.jsx("span",{className:"list-stat-label",children:"Total Items"})]}),s.jsxs("div",{className:"list-stat",children:[s.jsx("span",{className:"list-stat-value",children:D.length}),s.jsx("span",{className:"list-stat-label",children:"Categories"})]}),s.jsxs("div",{className:"list-stat",children:[s.jsxs("span",{className:"list-stat-value",children:["₹",r.reduce((q,se)=>q+se.price,0)]}),s.jsx("span",{className:"list-stat-label",children:"Total Value"})]})]}),s.jsxs("div",{className:"list-filters",children:[s.jsx("input",{type:"text",placeholder:"Search food items...",value:g,onChange:q=>v(q.target.value),className:"search-input"}),s.jsxs("select",{value:p,onChange:q=>x(q.target.value),className:"filter-select",children:[s.jsx("option",{value:"all",children:"All Categories"}),D.map(q=>s.jsx("option",{value:q.name,children:q.name},q._id))]})]}),s.jsxs("div",{className:"list-table",children:[s.jsxs("div",{className:"list-table-format title",children:[s.jsx("span",{children:"Image"}),s.jsx("span",{children:"Food Details"}),s.jsx("span",{children:"Restaurant"}),s.jsx("span",{children:"Category"}),s.jsx("span",{children:"Price"}),s.jsx("span",{children:"Status"}),s.jsx("span",{children:"Actions"})]}),c.length>0?c.map((q,se)=>{var S;return s.jsxs("div",{className:"list-table-format",children:[s.jsx("img",{src:wx(q.image,n),alt:q.name,className:"food-image",onError:V=>{console.error("Failed to load image:",q.image),V.target.src="/api/placeholder/50/50"}}),s.jsxs("div",{className:"food-info",children:[s.jsx("h4",{className:"food-name",children:q.name}),s.jsx("p",{className:"food-description",children:q.description||"No description available"})]}),s.jsx("span",{className:"food-restaurant",children:((S=q.restaurantId)==null?void 0:S.name)||"General Item"}),s.jsx("span",{className:"food-category",children:q.category}),s.jsxs("div",{className:"food-price-container",children:[q.isOnSale&&q.originalPrice?s.jsxs("div",{className:"price-with-discount",children:[s.jsxs("span",{className:"original-price",children:["₹",q.originalPrice]}),s.jsxs("span",{className:"discounted-price",children:["₹",q.price]}),s.jsxs("span",{className:"discount-badge-small",children:[q.discountPercentage,"% OFF"]})]}):s.jsxs("span",{className:"food-price",children:["₹",q.price]}),q.isPopular&&s.jsx("span",{className:"item-tag popular",children:"🔥 Popular"}),q.isFeatured&&s.jsx("span",{className:"item-tag featured",children:"⭐ Featured"})]}),s.jsx("span",{className:"badge badge-success",children:"Available"}),s.jsxs("div",{className:"food-actions",children:[s.jsx("button",{className:"action-btn edit-btn",title:"Edit Item",onClick:()=>ne(q),children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),s.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),s.jsx("button",{className:"action-btn delete-btn",title:"Delete Item",onClick:()=>Q(q._id),children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"3,6 5,6 21,6"}),s.jsx("path",{d:"M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"})]})})]})]},se)}):s.jsxs("div",{className:"no-items",children:[s.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"11",cy:"11",r:"8"}),s.jsx("path",{d:"M21 21l-4.35-4.35"})]}),s.jsx("h3",{children:"No items found"}),s.jsx("p",{children:"Try adjusting your search or filter criteria"})]})]})]}),b&&s.jsx("div",{className:"edit-modal-overlay",onClick:ce,children:s.jsxs("div",{className:"edit-modal",onClick:q=>q.stopPropagation(),children:[s.jsxs("div",{className:"edit-modal-header",children:[s.jsx("h3",{children:"Edit Food Item"}),s.jsx("button",{className:"close-btn",onClick:ce,children:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),s.jsxs("form",{onSubmit:X,className:"edit-form",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-name",children:"Food Name *"}),s.jsx("input",{type:"text",id:"edit-name",name:"name",value:O.name,onChange:re,placeholder:"Enter food name",required:!0})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-description",children:"Description"}),s.jsx("textarea",{id:"edit-description",name:"description",value:O.description,onChange:re,placeholder:"Enter food description",rows:"3"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-restaurant",children:"Restaurant"}),s.jsxs("select",{id:"edit-restaurant",name:"restaurantId",value:O.restaurantId,onChange:re,children:[s.jsx("option",{value:"",children:"No Restaurant (General Item)"}),U.map(q=>s.jsx("option",{value:q._id,children:q.name},q._id))]}),s.jsx("small",{children:"Select a restaurant to associate this food item with"})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-category",children:"Category *"}),s.jsxs("select",{id:"edit-category",name:"category",value:O.category,onChange:re,required:!0,children:[s.jsx("option",{value:"",children:"Select Category"}),D.map(q=>s.jsx("option",{value:q.name,children:q.name},q._id))]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-price",children:"Price (₹) *"}),s.jsx("input",{type:"number",id:"edit-price",name:"price",value:O.price,onChange:re,placeholder:"Enter price",min:"1",required:!0}),s.jsx("small",{children:"This is the original price before discount"})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-discount",children:"Discount (%)"}),s.jsx("input",{type:"number",id:"edit-discount",name:"discountPercentage",value:O.discountPercentage,onChange:re,placeholder:"0",min:"0",max:"100"}),s.jsx("small",{children:"Enter 0 to remove discount"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-discount-label",children:"Discount Label"}),s.jsx("input",{type:"text",id:"edit-discount-label",name:"discountLabel",value:O.discountLabel,onChange:re,placeholder:"e.g., MEGA SALE, LIMITED TIME"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-tags",children:"Tags"}),s.jsx("input",{type:"text",id:"edit-tags",name:"tags",value:O.tags,onChange:re,placeholder:"e.g., Bestseller, New, Spicy (comma separated)"})]}),s.jsxs("div",{className:"form-row",children:[s.jsx("div",{className:"form-group checkbox-group",children:s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",name:"isPopular",checked:O.isPopular,onChange:re}),s.jsx("span",{className:"checkmark"}),"Mark as Popular Item"]})}),s.jsx("div",{className:"form-group checkbox-group",children:s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",name:"isFeatured",checked:O.isFeatured,onChange:re}),s.jsx("span",{className:"checkmark"}),"Mark as Featured Item"]})})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-image",children:"Update Image (Optional)"}),s.jsxs("div",{className:"image-upload-section",children:[w&&s.jsxs("div",{className:"current-image",children:[s.jsx("img",{src:w,alt:"Food preview",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"8px"}}),s.jsx("p",{children:"New image uploaded to Cloudinary"})]}),s.jsx("input",{type:"file",id:"edit-image",name:"image",onChange:re,accept:"image/*"}),O.image&&!w&&s.jsx("div",{className:"upload-actions",children:s.jsx("button",{type:"button",onClick:()=>ye(O.image),disabled:T,className:"upload-btn",style:{marginTop:"10px",padding:"8px 16px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:T?"not-allowed":"pointer"},children:T?"Uploading...":"Upload to Cloudinary"})}),s.jsx("small",{children:w?"Image uploaded to Cloudinary successfully":"Select an image and click 'Upload to Cloudinary' for faster loading, or leave empty to keep current image"})]})]}),s.jsxs("div",{className:"form-actions",children:[s.jsx("button",{type:"button",className:"btn btn-secondary",onClick:ce,children:"Cancel"}),s.jsx("button",{type:"submit",className:"btn btn-primary",children:"Update Item"})]})]})]})})]})},Rx=n=>`₹${n}`,Ax=({url:n})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,m]=E.useState("all"),[g,v]=E.useState(null),[p,x]=E.useState(!1),b=E.useCallback(async()=>{var N,w;try{d(!0);const W=await me.get(`${n}/api/order/list`);if(W.data.success){const P=(W.data.data||[]).sort((F,ee)=>new Date(ee.date)-new Date(F.date));u(P)}else Y.error(W.data.message||"Failed to load orders")}catch(W){console.log(W),Y.error(((w=(N=W.response)==null?void 0:N.data)==null?void 0:w.message)||"An error occurred while fetching orders")}finally{d(!1)}},[n]),_=async(N,w)=>{try{(await me.post(n+"/api/order/status",{orderId:w,status:N.target.value})).data.success?(Y.success("Status updated successfully"),b()):Y.error("Failed to update status")}catch(W){console.error("Error updating status:",W),Y.error("An error occurred while updating status")}},U=async N=>{if(window.confirm("Are you sure you want to delete this order? This action cannot be undone."))try{const w=await me.post(n+"/api/order/delete",{orderId:N});w.data.success?(Y.success("Order deleted successfully"),b()):Y.error(w.data.message||"Failed to delete order")}catch(w){console.error("Error deleting order:",w),Y.error("An error occurred while deleting order")}},C=N=>{v({_id:N._id,status:N.status,amount:N.amount,address:{...N.address}}),x(!0)},D=async()=>{try{const N=await me.post(n+"/api/order/edit",{orderId:g._id,status:g.status,amount:g.amount,address:g.address});N.data.success?(Y.success("Order updated successfully"),x(!1),v(null),b()):Y.error(N.data.message||"Failed to update order")}catch(N){console.error("Error updating order:",N),Y.error("An error occurred while updating order")}};E.useEffect(()=>{b()},[b]);const M=r.filter(N=>f==="all"?!0:N.status.toLowerCase().replace(" ","_")===f),O={total:r.length,processing:r.filter(N=>N.status==="Food Processing").length,delivery:r.filter(N=>N.status==="Out for delivery").length,delivered:r.filter(N=>N.status==="Delivered").length},R=N=>{switch(N){case"Food Processing":return"processing";case"Out for delivery":return"delivery";case"Delivered":return"delivered";default:return"processing"}},T=N=>{const w=new Date(N);return w.toLocaleDateString()+" "+w.toLocaleTimeString()};return c?s.jsx("div",{className:"orders",children:s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading orders..."})]})}):s.jsxs("div",{className:"orders",children:[s.jsxs("div",{className:"card",children:[s.jsx("div",{className:"card-header",children:s.jsxs("div",{children:[s.jsx("h2",{className:"card-title",children:"Orders Management"}),s.jsx("p",{className:"card-subtitle",children:"Track and manage customer orders"})]})}),s.jsxs("div",{className:"orders-stats",children:[s.jsxs("div",{className:"orders-stat-card",children:[s.jsx("div",{className:"stat-icon total",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:O.total}),s.jsx("p",{children:"Total Orders"})]})]}),s.jsxs("div",{className:"orders-stat-card",children:[s.jsx("div",{className:"stat-icon processing",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("polyline",{points:"12,6 12,12 16,14"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:O.processing}),s.jsx("p",{children:"Processing"})]})]}),s.jsxs("div",{className:"orders-stat-card",children:[s.jsx("div",{className:"stat-icon delivery",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M1 3h15l-1 9H2l2-7z"}),s.jsx("path",{d:"M16 8v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V8"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:O.delivery}),s.jsx("p",{children:"Out for Delivery"})]})]}),s.jsxs("div",{className:"orders-stat-card",children:[s.jsx("div",{className:"stat-icon delivered",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"9,11 12,14 22,4"}),s.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:O.delivered}),s.jsx("p",{children:"Delivered"})]})]})]}),s.jsx("div",{className:"orders-filters",children:s.jsxs("select",{value:f,onChange:N=>m(N.target.value),className:"filter-select",children:[s.jsxs("option",{value:"all",children:["All Orders (",O.total,")"]}),s.jsxs("option",{value:"food_processing",children:["Processing (",O.processing,")"]}),s.jsxs("option",{value:"out_for_delivery",children:["Out for Delivery (",O.delivery,")"]}),s.jsxs("option",{value:"delivered",children:["Delivered (",O.delivered,")"]})]})}),s.jsx("div",{className:"orders-list",children:M.length>0?M.map((N,w)=>s.jsxs("div",{className:`order-item ${R(N.status)}`,children:[s.jsxs("div",{className:"order-header",children:[s.jsxs("div",{children:[s.jsxs("h3",{className:"order-id",children:["Order #",N._id.slice(-6)]}),s.jsx("p",{className:"order-time",children:T(N.date)})]}),s.jsx("span",{className:`order-status-badge ${R(N.status)}`,children:N.status})]}),s.jsxs("div",{className:"order-content",children:[s.jsx("div",{className:"order-icon",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),s.jsx("circle",{cx:"12",cy:"10",r:"3"})]})}),s.jsxs("div",{className:"order-details",children:[s.jsx("p",{className:"order-item-food",children:N.items.map((W,P)=>P===N.items.length-1?W.name+" x "+W.quantity:W.name+" x "+W.quantity+", ")}),s.jsx("h4",{className:"order-item-name",children:N.address.firstName+" "+N.address.lastName}),s.jsxs("div",{className:"order-item-address",children:[s.jsxs("p",{children:[N.address.street,","]}),s.jsxs("p",{children:[N.address.city,", ",N.address.state,", ",N.address.zipCode]})]}),s.jsxs("p",{className:"order-item-phone",children:["📞 ",N.address.phone]})]}),s.jsxs("div",{className:"order-summary",children:[s.jsxs("p",{className:"order-items-count",children:["Items: ",N.items.length]}),s.jsx("p",{className:"order-amount",children:Rx(N.amount)})]}),s.jsxs("div",{className:"order-actions",children:[s.jsxs("select",{onChange:W=>_(W,N._id),value:N.status,className:"status-select",children:[s.jsx("option",{value:"Food Processing",children:"Food Processing"}),s.jsx("option",{value:"Out for delivery",children:"Out for delivery"}),s.jsx("option",{value:"Delivered",children:"Delivered"})]}),s.jsx("button",{className:"edit-order-btn",onClick:()=>C(N),title:"Edit Order",children:"✏️ Edit"}),s.jsx("button",{className:"delete-order-btn",onClick:()=>U(N._id),title:"Delete Order",children:"🗑️ Delete"})]})]})]},w)):s.jsxs("div",{className:"no-orders",children:[s.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),s.jsx("h3",{children:"No orders found"}),s.jsx("p",{children:"No orders match the selected filter criteria"})]})})]}),p&&g&&s.jsx("div",{className:"modal-overlay",children:s.jsxs("div",{className:"edit-modal",children:[s.jsxs("div",{className:"modal-header",children:[s.jsxs("h3",{children:["Edit Order #",g._id.slice(-6)]}),s.jsx("button",{className:"close-modal-btn",onClick:()=>{x(!1),v(null)},children:"✕"})]}),s.jsxs("div",{className:"modal-content",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Order Status:"}),s.jsxs("select",{value:g.status,onChange:N=>v({...g,status:N.target.value}),className:"edit-select",children:[s.jsx("option",{value:"Food Processing",children:"Food Processing"}),s.jsx("option",{value:"Out for delivery",children:"Out for delivery"}),s.jsx("option",{value:"Delivered",children:"Delivered"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Order Amount (₹):"}),s.jsx("input",{type:"number",value:g.amount,onChange:N=>v({...g,amount:parseFloat(N.target.value)}),className:"edit-input",min:"0",step:"0.01"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Customer Name:"}),s.jsxs("div",{className:"name-inputs",children:[s.jsx("input",{type:"text",placeholder:"First Name",value:g.address.firstName,onChange:N=>v({...g,address:{...g.address,firstName:N.target.value}}),className:"edit-input"}),s.jsx("input",{type:"text",placeholder:"Last Name",value:g.address.lastName,onChange:N=>v({...g,address:{...g.address,lastName:N.target.value}}),className:"edit-input"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Phone Number:"}),s.jsx("input",{type:"text",value:g.address.phone,onChange:N=>v({...g,address:{...g.address,phone:N.target.value}}),className:"edit-input"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Delivery Address:"}),s.jsx("input",{type:"text",placeholder:"Street",value:g.address.street,onChange:N=>v({...g,address:{...g.address,street:N.target.value}}),className:"edit-input"}),s.jsxs("div",{className:"address-inputs",children:[s.jsx("input",{type:"text",placeholder:"City",value:g.address.city,onChange:N=>v({...g,address:{...g.address,city:N.target.value}}),className:"edit-input"}),s.jsx("input",{type:"text",placeholder:"State",value:g.address.state,onChange:N=>v({...g,address:{...g.address,state:N.target.value}}),className:"edit-input"}),s.jsx("input",{type:"text",placeholder:"ZIP Code",value:g.address.zipCode,onChange:N=>v({...g,address:{...g.address,zipCode:N.target.value}}),className:"edit-input"})]})]})]}),s.jsxs("div",{className:"modal-actions",children:[s.jsx("button",{className:"cancel-btn",onClick:()=>{x(!1),v(null)},children:"Cancel"}),s.jsx("button",{className:"save-btn",onClick:D,children:"Save Changes"})]})]})})]})},Ox=({url:n})=>{const[r,u]=E.useState({salesTrend:[],categoryStats:[],orderStats:{total:0,completed:0,pending:0,cancelled:0},revenueStats:{today:0,week:0,month:0,total:0},topItems:[],customerStats:{newCustomers:0,returningCustomers:0,totalCustomers:0}}),[c,d]=E.useState(!0),[f,m]=E.useState("week");E.useEffect(()=>{g()},[f]);const g=async()=>{try{d(!0);const x=await me.get(`${n}/api/order/list`),b=x.data.success?x.data.data:[],_=await me.get(`${n}/api/food/list`),U=_.data.success?_.data.data:[];v(b,U)}catch(x){console.error("Error fetching analytics data:",x)}finally{d(!1)}},v=(x,b)=>{const _=new Date,U=new Date(_.getFullYear(),_.getMonth(),_.getDate()),C=new Date(U.getTime()-7*24*60*60*1e3),D=new Date(U.getTime()-30*24*60*60*1e3),M={total:x.length,completed:x.filter(Q=>Q.status==="Delivered").length,pending:x.filter(Q=>Q.status==="Food Processing"||Q.status==="Out for delivery").length,cancelled:0},O=x.filter(Q=>new Date(Q.date)>=U),R=x.filter(Q=>new Date(Q.date)>=C),T=x.filter(Q=>new Date(Q.date)>=D),N={today:O.reduce((Q,$)=>Q+$.amount,0),week:R.reduce((Q,$)=>Q+$.amount,0),month:T.reduce((Q,$)=>Q+$.amount,0),total:x.reduce((Q,$)=>Q+$.amount,0)},w={};b.forEach(Q=>{w[Q.category]||(w[Q.category]={count:0,revenue:0}),w[Q.category].count++}),x.forEach(Q=>{Q.items.forEach($=>{const ne=b.find(ce=>ce.name===$.name);ne&&w[ne.category]&&(w[ne.category].revenue+=$.price*$.quantity)})});const W=Object.entries(w).map(([Q,$])=>({category:Q,count:$.count,revenue:$.revenue,percentage:($.revenue/N.total*100).toFixed(1)})).sort((Q,$)=>$.revenue-Q.revenue),P=[];for(let Q=6;Q>=0;Q--){const $=new Date(U.getTime()-Q*24*60*60*1e3),ne=x.filter(ce=>new Date(ce.date).toDateString()===$.toDateString());P.push({date:$.toLocaleDateString("en-US",{weekday:"short"}),orders:ne.length,revenue:ne.reduce((ce,re)=>ce+re.amount,0)})}const F={};x.forEach(Q=>{Q.items.forEach($=>{F[$.name]||(F[$.name]={quantity:0,revenue:0}),F[$.name].quantity+=$.quantity,F[$.name].revenue+=$.price*$.quantity})});const ee=Object.entries(F).map(([Q,$])=>({name:Q,...$})).sort((Q,$)=>$.quantity-Q.quantity).slice(0,5);u({salesTrend:P,categoryStats:W,orderStats:M,revenueStats:N,topItems:ee,customerStats:{newCustomers:Math.floor(x.length*.3),returningCustomers:Math.floor(x.length*.7),totalCustomers:x.length}})},p=x=>`₹${x}`;return c?s.jsx("div",{className:"analytics",children:s.jsxs("div",{className:"analytics-loading",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading analytics..."})]})}):s.jsxs("div",{className:"analytics",children:[s.jsxs("div",{className:"analytics-header",children:[s.jsxs("div",{children:[s.jsx("h1",{children:"Analytics Dashboard"}),s.jsx("p",{children:"Detailed insights into your business performance"})]}),s.jsxs("div",{className:"time-range-selector",children:[s.jsx("button",{className:f==="week"?"active":"",onClick:()=>m("week"),children:"Week"}),s.jsx("button",{className:f==="month"?"active":"",onClick:()=>m("month"),children:"Month"}),s.jsx("button",{className:f==="year"?"active":"",onClick:()=>m("year"),children:"Year"})]})]}),s.jsxs("div",{className:"metrics-grid",children:[s.jsxs("div",{className:"metric-card",children:[s.jsx("div",{className:"metric-icon revenue",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"12",y1:"1",x2:"12",y2:"23"}),s.jsx("path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})]})}),s.jsxs("div",{className:"metric-info",children:[s.jsx("h3",{children:p(r.revenueStats.total)}),s.jsx("p",{children:"Total Revenue"}),s.jsx("span",{className:"metric-change positive",children:"+12.5%"})]})]}),s.jsxs("div",{className:"metric-card",children:[s.jsx("div",{className:"metric-icon orders",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]})}),s.jsxs("div",{className:"metric-info",children:[s.jsx("h3",{children:r.orderStats.total}),s.jsx("p",{children:"Total Orders"}),s.jsx("span",{className:"metric-change positive",children:"+8.2%"})]})]}),s.jsxs("div",{className:"metric-card",children:[s.jsx("div",{className:"metric-icon customers",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"9",cy:"7",r:"4"}),s.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),s.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),s.jsxs("div",{className:"metric-info",children:[s.jsx("h3",{children:r.customerStats.totalCustomers}),s.jsx("p",{children:"Total Customers"}),s.jsx("span",{className:"metric-change positive",children:"+15.3%"})]})]}),s.jsxs("div",{className:"metric-card",children:[s.jsx("div",{className:"metric-icon completion",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"9,11 12,14 22,4"}),s.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})}),s.jsxs("div",{className:"metric-info",children:[s.jsxs("h3",{children:[(r.orderStats.completed/r.orderStats.total*100).toFixed(1),"%"]}),s.jsx("p",{children:"Completion Rate"}),s.jsx("span",{className:"metric-change positive",children:"+2.1%"})]})]})]}),s.jsxs("div",{className:"charts-section",children:[s.jsx("div",{className:"chart-container",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Sales Trend"}),s.jsx("p",{className:"card-subtitle",children:"Daily sales over the last week"})]}),s.jsx("div",{className:"sales-chart",children:r.salesTrend.map((x,b)=>s.jsxs("div",{className:"chart-bar",children:[s.jsx("div",{className:"bar",style:{height:`${x.revenue/Math.max(...r.salesTrend.map(_=>_.revenue))*100}%`}}),s.jsx("span",{className:"bar-label",children:x.date}),s.jsx("span",{className:"bar-value",children:p(x.revenue)})]},b))})]})}),s.jsx("div",{className:"chart-container",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Category Performance"}),s.jsx("p",{className:"card-subtitle",children:"Revenue by food category"})]}),s.jsx("div",{className:"category-chart",children:r.categoryStats.slice(0,5).map((x,b)=>s.jsxs("div",{className:"category-item",children:[s.jsxs("div",{className:"category-info",children:[s.jsx("span",{className:"category-name",children:x.category}),s.jsx("span",{className:"category-revenue",children:p(x.revenue)})]}),s.jsx("div",{className:"category-bar",children:s.jsx("div",{className:"category-fill",style:{width:`${x.percentage}%`}})}),s.jsxs("span",{className:"category-percentage",children:[x.percentage,"%"]})]},b))})]})})]}),s.jsxs("div",{className:"bottom-section",children:[s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Top Selling Items"}),s.jsx("p",{className:"card-subtitle",children:"Most popular food items"})]}),s.jsxs("div",{className:"top-items-table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("span",{children:"Rank"}),s.jsx("span",{children:"Item Name"}),s.jsx("span",{children:"Quantity Sold"}),s.jsx("span",{children:"Revenue"})]}),r.topItems.map((x,b)=>s.jsxs("div",{className:"table-row",children:[s.jsxs("span",{className:"rank",children:["#",b+1]}),s.jsx("span",{className:"item-name",children:x.name}),s.jsx("span",{className:"quantity",children:x.quantity}),s.jsx("span",{className:"revenue",children:p(x.revenue)})]},b))]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Order Status"}),s.jsx("p",{className:"card-subtitle",children:"Current order distribution"})]}),s.jsxs("div",{className:"order-status",children:[s.jsxs("div",{className:"status-item",children:[s.jsx("div",{className:"status-circle completed"}),s.jsxs("div",{className:"status-info",children:[s.jsx("span",{className:"status-count",children:r.orderStats.completed}),s.jsx("span",{className:"status-label",children:"Completed"})]})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("div",{className:"status-circle pending"}),s.jsxs("div",{className:"status-info",children:[s.jsx("span",{className:"status-count",children:r.orderStats.pending}),s.jsx("span",{className:"status-label",children:"Pending"})]})]}),s.jsxs("div",{className:"status-item",children:[s.jsx("div",{className:"status-circle cancelled"}),s.jsxs("div",{className:"status-info",children:[s.jsx("span",{className:"status-count",children:r.orderStats.cancelled}),s.jsx("span",{className:"status-label",children:"Cancelled"})]})]})]})]})]})]})},Dx=async(n,r="eatzone/categories")=>{var u;try{if(!n)throw new Error("No file provided");if(!n.type.startsWith("image/"))throw new Error("Only image files are allowed");const c=5*1024*1024;if(n.size>c)throw new Error("File size must be less than 5MB");console.log("Uploading category image to Cloudinary:",{name:n.name,size:n.size,type:n.type,folder:r});const d=new FormData;d.append("file",n),d.append("upload_preset","eatzone_admin"),d.append("folder",r),d.append("tags","category,menu");const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const g=await f.json();throw console.error("Cloudinary upload error:",g),new Error(((u=g.error)==null?void 0:u.message)||`Upload failed with status ${f.status}`)}const m=await f.json();return console.log("Cloudinary upload successful:",m),{success:!0,url:m.secure_url,publicId:m.public_id}}catch(c){return console.error("Cloudinary upload error:",c),{success:!1,error:c.message}}},Mx=(n,r)=>n?n.startsWith("http")?n:`${r}/images/${n}`:rp("default"),rp=n=>{const r={Rolls:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/rolls.jpg",Salad:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/salad.jpg",Deserts:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/desserts.jpg",Sandwich:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sandwich.jpg",Cake:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/cake.jpg",Veg:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/veg.jpg",Pizza:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pizza.jpg",Pasta:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pasta.jpg",Noodles:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/noodles.jpg","Main Course":"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/main-course.jpg",Appetizer:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/appetizer.jpg",Sushi:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sushi.jpg",default:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/default-food.jpg"};return r[n]||r.default},zx=({url:n})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!1),[f,m]=E.useState(!1),[g,v]=E.useState(null),[p,x]=E.useState({name:"",description:"",image:null,order:0,isActive:!0}),[b,_]=E.useState(""),[U,C]=E.useState(""),[D,M]=E.useState(!1),O=async()=>{var Q;try{d(!0),console.log("🔄 Fetching categories from:",`${n}/api/category/list-all`);const $=await me.get(`${n}/api/category/list-all`,{timeout:1e4,headers:{Accept:"application/json","Content-Type":"application/json"}});console.log("📦 Categories response:",$),$.data.success?(u($.data.data),console.log("✅ Categories loaded successfully:",$.data.data.length,"categories"),$.data.data.length===0&&Y.info("No categories found. You can add your first category!")):(console.error("❌ API returned error:",$.data.message),Y.error(`Failed to fetch categories: ${$.data.message}`))}catch($){console.error("❌ Error fetching categories:",$),$.code==="ECONNABORTED"?Y.error("Request timeout - Server might be slow or unreachable"):$.response?Y.error(`Server error: ${$.response.status} - ${((Q=$.response.data)==null?void 0:Q.message)||"Unknown error"}`):$.request?Y.error("Network error - Cannot reach server. Check your connection and server status."):Y.error(`Error: ${$.message}`)}finally{d(!1)}};E.useEffect(()=>{O()},[n]);const R=Q=>{const{name:$,value:ne,type:ce,checked:re}=Q.target;x(ye=>({...ye,[$]:ce==="checkbox"?re:ne}))},T=async Q=>{const $=Q.target.files[0];if($){x(ce=>({...ce,image:$}));const ne=new FileReader;ne.onloadend=()=>{_(ne.result)},ne.readAsDataURL($),M(!0),Y.info("Uploading image to Cloudinary...");try{const ce=await Dx($,"eatzone/categories");ce.success?(console.log("Category image uploaded successfully:",ce.url),C(ce.url),_(ce.url),Y.success("Image uploaded successfully!")):(console.error("Cloudinary upload failed:",ce.error),Y.error(ce.error||"Failed to upload image"))}catch(ce){console.error("Image upload error:",ce),Y.error("Failed to upload image")}finally{M(!1)}}},N=()=>{x({name:"",description:"",image:null,order:0,isActive:!0}),_(""),C(""),v(null),M(!1)},w=()=>{N(),m(!0)},W=Q=>{v(Q),x({name:Q.name,description:Q.description||"",image:null,order:Q.order||0,isActive:Q.isActive});const $=Q.image.startsWith("http")?Q.image:`${n}/images/${Q.image}`;_($),Q.image.startsWith("http")?C(Q.image):C(""),m(!0)},P=()=>{m(!1),N()},F=async Q=>{var $,ne;if(Q.preventDefault(),!p.name.trim()){Y.error("Category name is required");return}if(!g&&!U){Y.error("Category image is required. Please upload an image.");return}if(D){Y.error("Please wait for image upload to complete");return}try{d(!0);const ce=new FormData;ce.append("name",p.name.trim()),ce.append("description",p.description.trim()),ce.append("order",p.order),ce.append("isActive",p.isActive),U?ce.append("image",U):g&&g.image&&ce.append("image",g.image);let re;if(g?(ce.append("id",g._id),re=await me.post(`${n}/api/category/update`,ce)):re=await me.post(`${n}/api/category/add`,ce),re.data.success){Y.success(g?"Category updated successfully!":"Category added successfully!"),O(),P();try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"CATEGORY_UPDATED"},"*"),window.dispatchEvent(new CustomEvent("categoryUpdated"))}catch(ye){console.log("Could not notify client of category update:",ye)}}else Y.error(re.data.message||"Operation failed")}catch(ce){console.error("Error submitting category:",ce),Y.error(((ne=($=ce.response)==null?void 0:$.data)==null?void 0:ne.message)||"Error submitting category")}finally{d(!1)}},ee=async Q=>{if(window.confirm("Are you sure you want to delete this category?"))try{if(d(!0),(await me.post(`${n}/api/category/remove`,{id:Q})).data.success){Y.success("Category deleted successfully!"),O();try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"CATEGORY_UPDATED"},"*"),window.dispatchEvent(new CustomEvent("categoryUpdated"))}catch(ne){console.log("Could not notify client of category update:",ne)}}else Y.error("Failed to delete category")}catch($){console.error("Error deleting category:",$),Y.error("Error deleting category")}finally{d(!1)}};return s.jsxs("div",{className:"categories-container",children:[s.jsxs("div",{className:"categories-header",children:[s.jsx("h2",{children:"Food Categories Management"}),s.jsx("button",{className:"add-btn",onClick:w,children:"+ Add New Category"})]}),c&&s.jsx("div",{className:"loading",children:"Loading..."}),s.jsx("div",{className:"categories-grid",children:r.map(Q=>s.jsxs("div",{className:`category-card ${Q.isActive?"":"inactive"}`,children:[s.jsx("div",{className:"category-image",children:s.jsx("img",{src:Mx(Q.image,n),alt:Q.name,onError:$=>{console.log(`Failed to load admin image for ${Q.name}:`,Q.image),$.target.src=rp(Q.name)},onLoad:()=>{console.log(`Successfully loaded admin image for ${Q.name}`)}})}),s.jsxs("div",{className:"category-info",children:[s.jsx("h3",{children:Q.name}),s.jsx("p",{children:Q.description}),s.jsxs("div",{className:"category-meta",children:[s.jsxs("span",{children:["Order: ",Q.order]}),s.jsx("span",{className:`status ${Q.isActive?"active":"inactive"}`,children:Q.isActive?"Active":"Inactive"})]})]}),s.jsxs("div",{className:"category-actions",children:[s.jsx("button",{className:"edit-btn",onClick:()=>W(Q),children:"Edit"}),s.jsx("button",{className:"delete-btn",onClick:()=>ee(Q._id),children:"Delete"})]})]},Q._id))}),r.length===0&&!c&&s.jsx("div",{className:"no-categories",children:s.jsx("p",{children:"No categories found. Add your first category!"})}),f&&s.jsx("div",{className:"modal-overlay",onClick:P,children:s.jsxs("div",{className:"modal-content",onClick:Q=>Q.stopPropagation(),children:[s.jsxs("div",{className:"modal-header",children:[s.jsx("h3",{children:g?"Edit Category":"Add New Category"}),s.jsx("button",{className:"close-btn",onClick:P,children:"×"})]}),s.jsxs("form",{onSubmit:F,className:"category-form",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Category Name *"}),s.jsx("input",{type:"text",name:"name",value:p.name,onChange:R,placeholder:"Enter category name",required:!0})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Description"}),s.jsx("textarea",{name:"description",value:p.description,onChange:R,placeholder:"Enter category description",rows:"3"})]}),s.jsxs("div",{className:"form-group",children:[s.jsxs("label",{children:["Category Image ",!g&&"*"]}),s.jsx("input",{type:"file",accept:"image/*",onChange:T,disabled:D}),D&&s.jsx("div",{className:"upload-status",children:s.jsx("p",{children:"🔄 Uploading image to Cloudinary..."})}),b&&!D&&s.jsxs("div",{className:"image-preview",children:[s.jsx("img",{src:b,alt:"Preview",onError:Q=>{Q.target.src="/placeholder-food.jpg"}}),U&&s.jsxs("div",{className:"upload-success",children:[s.jsx("span",{className:"success-text",children:"✅ Image uploaded to Cloudinary!"}),s.jsxs("small",{children:["URL: ",U]})]})]}),s.jsx("small",{className:"form-help",children:"Recommended: 400x400px, JPG or PNG, max 5MB. Images will be automatically uploaded to Cloudinary."})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Display Order"}),s.jsx("input",{type:"number",name:"order",value:p.order,onChange:R,min:"0"})]}),s.jsx("div",{className:"form-group",children:s.jsxs("label",{className:"checkbox-label",children:[s.jsx("input",{type:"checkbox",name:"isActive",checked:p.isActive,onChange:R}),"Active"]})})]}),s.jsxs("div",{className:"form-actions",children:[s.jsx("button",{type:"button",onClick:P,className:"cancel-btn",children:"Cancel"}),s.jsx("button",{type:"submit",className:"submit-btn",disabled:c,children:c?"Saving...":g?"Update Category":"Add Category"})]})]})]})})]})},Ux=({url:n})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,m]=E.useState(!1),[g,v]=E.useState(null),[p,x]=E.useState({name:"",phone:"",email:"",vehicleType:"bike",vehicleNumber:"",status:"available"}),b=[{_id:"1",name:"Rajesh Kumar",phone:"+91 9876543210",email:"<EMAIL>",vehicleType:"bike",vehicleNumber:"MH12AB1234",status:"available",assignedOrders:0,completedOrders:45,rating:4.8,joinDate:"2024-01-15"},{_id:"2",name:"Priya Sharma",phone:"+91 9876543211",email:"<EMAIL>",vehicleType:"scooter",vehicleNumber:"MH12CD5678",status:"busy",assignedOrders:2,completedOrders:67,rating:4.9,joinDate:"2024-02-20"},{_id:"3",name:"Amit Patel",phone:"+91 9876543212",email:"<EMAIL>",vehicleType:"bike",vehicleNumber:"MH12EF9012",status:"offline",assignedOrders:0,completedOrders:23,rating:4.6,joinDate:"2024-03-10"},{_id:"4",name:"Sneha Reddy",phone:"+91 9876543213",email:"<EMAIL>",vehicleType:"scooter",vehicleNumber:"MH12GH3456",status:"available",assignedOrders:0,completedOrders:89,rating:4.7,joinDate:"2024-01-05"}];E.useEffect(()=>{_()},[]);const _=async()=>{try{d(!0),setTimeout(()=>{u(b),d(!1)},1e3)}catch(N){console.error("Error fetching delivery partners:",N),Y.error("Failed to load delivery partners"),d(!1)}},U=N=>{const{name:w,value:W}=N.target;x(P=>({...P,[w]:W}))},C=async N=>{N.preventDefault();try{if(g){const w=r.map(W=>W._id===g._id?{...W,...p}:W);u(w),Y.success("Partner updated successfully")}else{const w={_id:Date.now().toString(),...p,assignedOrders:0,completedOrders:0,rating:0,joinDate:new Date().toISOString().split("T")[0]};u(W=>[...W,w]),Y.success("Partner added successfully")}D()}catch(w){console.error("Error saving partner:",w),Y.error("Failed to save partner")}},D=()=>{x({name:"",phone:"",email:"",vehicleType:"bike",vehicleNumber:"",status:"available"}),m(!1),v(null)},M=N=>{v(N),x({name:N.name,phone:N.phone,email:N.email,vehicleType:N.vehicleType,vehicleNumber:N.vehicleNumber,status:N.status}),m(!0)},O=async N=>{if(window.confirm("Are you sure you want to delete this partner?"))try{u(w=>w.filter(W=>W._id!==N)),Y.success("Partner deleted successfully")}catch(w){console.error("Error deleting partner:",w),Y.error("Failed to delete partner")}},R=async(N,w)=>{try{const W=r.map(P=>P._id===N?{...P,status:w}:P);u(W),Y.success("Status updated successfully")}catch(W){console.error("Error updating status:",W),Y.error("Failed to update status")}},T=N=>{switch(N){case"available":return s.jsx("div",{className:"status-dot available"});case"busy":return s.jsx("div",{className:"status-dot busy"});case"offline":return s.jsx("div",{className:"status-dot offline"});default:return s.jsx("div",{className:"status-dot"})}};return c?s.jsx("div",{className:"delivery-partners",children:s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading delivery partners..."})]})}):s.jsxs("div",{className:"delivery-partners",children:[s.jsxs("div",{className:"page-header",children:[s.jsxs("div",{children:[s.jsx("h1",{children:"Delivery Partners"}),s.jsx("p",{children:"Manage your delivery team and track their performance"})]}),s.jsxs("button",{className:"btn btn-primary",onClick:()=>m(!0),children:[s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),s.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),"Add Partner"]})]}),s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon available",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"9",cy:"7",r:"4"}),s.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),s.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(N=>N.status==="available").length}),s.jsx("p",{children:"Available"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon busy",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("polyline",{points:"12,6 12,12 16,14"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(N=>N.status==="busy").length}),s.jsx("p",{children:"Busy"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon offline",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"4.93",y1:"4.93",x2:"19.07",y2:"19.07"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(N=>N.status==="offline").length}),s.jsx("p",{children:"Offline"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon total",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"9",cy:"7",r:"4"}),s.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),s.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.length}),s.jsx("p",{children:"Total Partners"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"All Delivery Partners"}),s.jsx("p",{className:"card-subtitle",children:"Manage and monitor your delivery team"})]}),s.jsxs("div",{className:"partners-table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("span",{children:"Partner"}),s.jsx("span",{children:"Contact"}),s.jsx("span",{children:"Vehicle"}),s.jsx("span",{children:"Status"}),s.jsx("span",{children:"Orders"}),s.jsx("span",{children:"Rating"}),s.jsx("span",{children:"Actions"})]}),r.map(N=>s.jsxs("div",{className:"table-row",children:[s.jsxs("div",{className:"partner-info",children:[s.jsx("div",{className:"partner-avatar",children:N.name.charAt(0).toUpperCase()}),s.jsxs("div",{children:[s.jsx("h4",{children:N.name}),s.jsxs("p",{children:["Joined ",new Date(N.joinDate).toLocaleDateString()]})]})]}),s.jsxs("div",{className:"contact-info",children:[s.jsx("p",{children:N.phone}),s.jsx("p",{children:N.email})]}),s.jsxs("div",{className:"vehicle-info",children:[s.jsx("p",{children:N.vehicleType.charAt(0).toUpperCase()+N.vehicleType.slice(1)}),s.jsx("p",{children:N.vehicleNumber})]}),s.jsxs("div",{className:"status-info",children:[T(N.status),s.jsxs("select",{value:N.status,onChange:w=>R(N._id,w.target.value),className:"status-select",children:[s.jsx("option",{value:"available",children:"Available"}),s.jsx("option",{value:"busy",children:"Busy"}),s.jsx("option",{value:"offline",children:"Offline"})]})]}),s.jsxs("div",{className:"orders-info",children:[s.jsxs("p",{children:["Active: ",N.assignedOrders]}),s.jsxs("p",{children:["Completed: ",N.completedOrders]})]}),s.jsx("div",{className:"rating-info",children:s.jsxs("div",{className:"rating",children:[s.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:s.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})}),N.rating]})}),s.jsxs("div",{className:"actions",children:[s.jsx("button",{className:"btn-icon edit",onClick:()=>M(N),title:"Edit Partner",children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),s.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),s.jsx("button",{className:"btn-icon delete",onClick:()=>O(N._id),title:"Delete Partner",children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"3,6 5,6 21,6"}),s.jsx("path",{d:"M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"})]})})]})]},N._id))]})]}),f&&s.jsx("div",{className:"modal-overlay",onClick:D,children:s.jsxs("div",{className:"modal",onClick:N=>N.stopPropagation(),children:[s.jsxs("div",{className:"modal-header",children:[s.jsx("h3",{children:g?"Edit Partner":"Add New Partner"}),s.jsx("button",{className:"modal-close",onClick:D,children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),s.jsxs("form",{onSubmit:C,className:"modal-form",children:[s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Name *"}),s.jsx("input",{type:"text",name:"name",value:p.name,onChange:U,className:"form-input",required:!0})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Phone *"}),s.jsx("input",{type:"tel",name:"phone",value:p.phone,onChange:U,className:"form-input",required:!0})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Email *"}),s.jsx("input",{type:"email",name:"email",value:p.email,onChange:U,className:"form-input",required:!0})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Vehicle Type *"}),s.jsxs("select",{name:"vehicleType",value:p.vehicleType,onChange:U,className:"form-select",required:!0,children:[s.jsx("option",{value:"bike",children:"Bike"}),s.jsx("option",{value:"scooter",children:"Scooter"}),s.jsx("option",{value:"car",children:"Car"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Vehicle Number *"}),s.jsx("input",{type:"text",name:"vehicleNumber",value:p.vehicleNumber,onChange:U,className:"form-input",required:!0})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Status"}),s.jsxs("select",{name:"status",value:p.status,onChange:U,className:"form-select",children:[s.jsx("option",{value:"available",children:"Available"}),s.jsx("option",{value:"busy",children:"Busy"}),s.jsx("option",{value:"offline",children:"Offline"})]})]}),s.jsxs("div",{className:"modal-actions",children:[s.jsx("button",{type:"button",className:"btn btn-secondary",onClick:D,children:"Cancel"}),s.jsx("button",{type:"submit",className:"btn btn-primary",children:g?"Update Partner":"Add Partner"})]})]})]})})]})},Bx=({url:n})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,m]=E.useState("all"),[g,v]=E.useState(null),[p,x]=E.useState(!1),b=[{_id:"1",customerName:"Rahul Sharma",customerEmail:"<EMAIL>",orderId:"ORD001",type:"complaint",subject:"Late Delivery",message:"My order was delivered 45 minutes late. The food was cold when it arrived.",rating:2,status:"pending",priority:"high",createdAt:"2024-01-15T10:30:00Z",resolvedAt:null,response:null,category:"delivery"},{_id:"2",customerName:"Priya Patel",customerEmail:"<EMAIL>",orderId:"ORD002",type:"feedback",subject:"Great Service",message:"Excellent food quality and fast delivery. Really impressed with the service!",rating:5,status:"resolved",priority:"low",createdAt:"2024-01-14T15:20:00Z",resolvedAt:"2024-01-14T16:00:00Z",response:"Thank you for your positive feedback! We appreciate your business.",category:"service"},{_id:"3",customerName:"Amit Kumar",customerEmail:"<EMAIL>",orderId:"ORD003",type:"complaint",subject:"Wrong Order",message:"I ordered chicken biryani but received vegetable biryani instead.",rating:1,status:"pending",priority:"high",createdAt:"2024-01-13T12:45:00Z",resolvedAt:null,response:null,category:"order"},{_id:"4",customerName:"Sneha Reddy",customerEmail:"<EMAIL>",orderId:"ORD004",type:"suggestion",subject:"App Improvement",message:"It would be great to have a feature to track delivery in real-time.",rating:4,status:"resolved",priority:"medium",createdAt:"2024-01-12T09:15:00Z",resolvedAt:"2024-01-12T14:30:00Z",response:"Thank you for the suggestion. We are working on implementing real-time tracking.",category:"app"}];E.useEffect(()=>{_()},[]);const _=async()=>{try{d(!0),setTimeout(()=>{u(b),d(!1)},1e3)}catch(w){console.error("Error fetching feedbacks:",w),Y.error("Failed to load feedbacks"),d(!1)}},U=async(w,W,P=null)=>{try{const F=r.map(ee=>ee._id===w?{...ee,status:W,resolvedAt:W==="resolved"?new Date().toISOString():null,response:P||ee.response}:ee);u(F),Y.success("Status updated successfully")}catch(F){console.error("Error updating status:",F),Y.error("Failed to update status")}},C=w=>{v(w),x(!0)},D=async(w,W)=>{try{await U(w,"resolved",W),x(!1),v(null)}catch(P){console.error("Error responding to feedback:",P),Y.error("Failed to respond to feedback")}},M=r.filter(w=>f==="all"?!0:w.status===f),O=w=>`badge ${{pending:"badge-warning",resolved:"badge-success",in_progress:"badge-info"}[w]||"badge-info"}`,R=w=>`badge ${{high:"badge-danger",medium:"badge-warning",low:"badge-success"}[w]||"badge-info"}`,T=w=>{switch(w){case"complaint":return s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),s.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]});case"feedback":return s.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{d:"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"})});case"suggestion":return s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),s.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]});default:return null}},N=w=>Array.from({length:5},(W,P)=>s.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:P<w?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",className:P<w?"star-filled":"star-empty",children:s.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})},P));return c?s.jsx("div",{className:"feedback",children:s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Loading feedback..."})]})}):s.jsxs("div",{className:"feedback",children:[s.jsxs("div",{className:"page-header",children:[s.jsxs("div",{children:[s.jsx("h1",{children:"Feedback & Complaints"}),s.jsx("p",{children:"Manage customer feedback and resolve complaints"})]}),s.jsxs("div",{className:"filter-buttons",children:[s.jsxs("button",{className:f==="all"?"active":"",onClick:()=>m("all"),children:["All (",r.length,")"]}),s.jsxs("button",{className:f==="pending"?"active":"",onClick:()=>m("pending"),children:["Pending (",r.filter(w=>w.status==="pending").length,")"]}),s.jsxs("button",{className:f==="resolved"?"active":"",onClick:()=>m("resolved"),children:["Resolved (",r.filter(w=>w.status==="resolved").length,")"]})]})]}),s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon complaints",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),s.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(w=>w.type==="complaint").length}),s.jsx("p",{children:"Complaints"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon feedback-positive",children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{d:"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"})})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(w=>w.type==="feedback").length}),s.jsx("p",{children:"Positive Feedback"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon suggestions",children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),s.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:r.filter(w=>w.type==="suggestion").length}),s.jsx("p",{children:"Suggestions"})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon average-rating",children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})})}),s.jsxs("div",{className:"stat-info",children:[s.jsx("h3",{children:(r.reduce((w,W)=>w+W.rating,0)/r.length).toFixed(1)}),s.jsx("p",{children:"Average Rating"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h3",{className:"card-title",children:"Customer Feedback"}),s.jsx("p",{className:"card-subtitle",children:"Review and respond to customer feedback"})]}),s.jsxs("div",{className:"feedback-table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("span",{children:"Customer"}),s.jsx("span",{children:"Type"}),s.jsx("span",{children:"Subject"}),s.jsx("span",{children:"Rating"}),s.jsx("span",{children:"Priority"}),s.jsx("span",{children:"Status"}),s.jsx("span",{children:"Date"}),s.jsx("span",{children:"Actions"})]}),M.map(w=>s.jsxs("div",{className:"table-row",children:[s.jsxs("div",{className:"customer-info",children:[s.jsx("div",{className:"customer-avatar",children:w.customerName.charAt(0).toUpperCase()}),s.jsxs("div",{children:[s.jsx("h4",{children:w.customerName}),s.jsxs("p",{children:["Order #",w.orderId]})]})]}),s.jsxs("div",{className:"type-info",children:[T(w.type),s.jsx("span",{className:"type-label",children:w.type})]}),s.jsxs("div",{className:"subject-info",children:[s.jsx("h4",{children:w.subject}),s.jsxs("p",{children:[w.message.substring(0,50),"..."]})]}),s.jsxs("div",{className:"rating-info",children:[s.jsx("div",{className:"rating-stars",children:N(w.rating)}),s.jsxs("span",{className:"rating-value",children:[w.rating,"/5"]})]}),s.jsx("div",{className:"priority-info",children:s.jsx("span",{className:R(w.priority),children:w.priority})}),s.jsx("div",{className:"status-info",children:s.jsx("span",{className:O(w.status),children:w.status})}),s.jsxs("div",{className:"date-info",children:[s.jsx("p",{children:new Date(w.createdAt).toLocaleDateString()}),s.jsx("p",{children:new Date(w.createdAt).toLocaleTimeString()})]}),s.jsxs("div",{className:"actions",children:[s.jsx("button",{className:"btn-icon view",onClick:()=>C(w),title:"View Details",children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),s.jsx("circle",{cx:"12",cy:"12",r:"3"})]})}),w.status==="pending"&&s.jsx("button",{className:"btn-icon resolve",onClick:()=>U(w._id,"resolved"),title:"Mark as Resolved",children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"9,11 12,14 22,4"}),s.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})})]})]},w._id))]})]}),p&&g&&s.jsx(Lx,{feedback:g,onClose:()=>{x(!1),v(null)},onRespond:D})]})},Lx=({feedback:n,onClose:r,onRespond:u})=>{const[c,d]=E.useState(n.response||""),f=m=>{m.preventDefault(),c.trim()&&u(n._id,c)};return s.jsx("div",{className:"modal-overlay",onClick:r,children:s.jsxs("div",{className:"modal feedback-modal",onClick:m=>m.stopPropagation(),children:[s.jsxs("div",{className:"modal-header",children:[s.jsx("h3",{children:"Feedback Details"}),s.jsx("button",{className:"modal-close",onClick:r,children:s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),s.jsxs("div",{className:"modal-content",children:[s.jsxs("div",{className:"feedback-details",children:[s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Customer:"}),s.jsxs("span",{children:[n.customerName," (",n.customerEmail,")"]})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Order ID:"}),s.jsx("span",{children:n.orderId})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Type:"}),s.jsx("span",{className:"type-badge",children:n.type})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Subject:"}),s.jsx("span",{children:n.subject})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Rating:"}),s.jsxs("div",{className:"rating-display",children:[Array.from({length:5},(m,g)=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:g<n.rating?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",className:g<n.rating?"star-filled":"star-empty",children:s.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})},g)),s.jsxs("span",{children:["(",n.rating,"/5)"]})]})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Message:"}),s.jsx("div",{className:"message-content",children:n.message})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Date:"}),s.jsx("span",{children:new Date(n.createdAt).toLocaleString()})]}),s.jsxs("div",{className:"detail-row",children:[s.jsx("label",{children:"Status:"}),s.jsx("span",{className:`badge ${n.status==="resolved"?"badge-success":"badge-warning"}`,children:n.status})]})]}),n.status==="pending"&&s.jsxs("form",{onSubmit:f,className:"response-form",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{className:"form-label",children:"Response:"}),s.jsx("textarea",{value:c,onChange:m=>d(m.target.value),className:"form-textarea",rows:"4",placeholder:"Type your response here...",required:!0})]}),s.jsxs("div",{className:"modal-actions",children:[s.jsx("button",{type:"button",className:"btn btn-secondary",onClick:r,children:"Cancel"}),s.jsx("button",{type:"submit",className:"btn btn-primary",children:"Send Response & Mark Resolved"})]})]}),n.status==="resolved"&&n.response&&s.jsxs("div",{className:"existing-response",children:[s.jsx("h4",{children:"Response:"}),s.jsx("div",{className:"response-content",children:n.response}),s.jsxs("p",{className:"response-date",children:["Resolved on ",new Date(n.resolvedAt).toLocaleString()]})]})]})]})})},op=E.createContext({admin:null,token:null,loading:!1,login:()=>{},logout:()=>{},updateAdmin:()=>{},isAuthenticated:!1,url:"https://eatzone.onrender.com"}),Hx=()=>{const n=E.useContext(op);if(!n)throw new Error("useAdmin must be used within an AdminProvider");return n},kx=({children:n})=>{const[r,u]=E.useState(null),[c,d]=E.useState(localStorage.getItem("adminToken")||null),[f,m]=E.useState(!1),g="https://eatzone.onrender.com",v=!!(r&&c),p=async U=>{m(!0);try{const D=await(await fetch(`${g}/api/admin/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(U)})).json();return D.success?(u(D.admin),d(D.token),localStorage.setItem("adminToken",D.token),localStorage.setItem("adminData",JSON.stringify(D.admin)),{success:!0}):{success:!1,message:D.message}}catch(C){return console.error("Login error:",C),{success:!1,message:"Login failed. Please try again."}}finally{m(!1)}},x=()=>{u(null),d(null),localStorage.removeItem("adminToken"),localStorage.removeItem("adminData")},b=U=>{u(U),localStorage.setItem("adminData",JSON.stringify(U))};E.useEffect(()=>{const U=localStorage.getItem("adminToken"),C=localStorage.getItem("adminData");if(U&&C)try{d(U),u(JSON.parse(C))}catch(D){console.error("Error loading saved admin data:",D),x()}},[]);const _={admin:r,token:c,loading:f,login:p,logout:x,updateAdmin:b,isAuthenticated:v,url:g};return s.jsx(op.Provider,{value:_,children:n})},qx=({url:n,token:r})=>{const{admin:u}=Hx(),[c,d]=E.useState(null),[f,m]=E.useState(!1),[g,v]=E.useState(""),[p,x]=E.useState({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[]}),[b,_]=E.useState(""),U=R=>{const T=R.target.name,N=R.target.value;x(w=>({...w,[T]:N}))},C=()=>{b.trim()&&!p.cuisineTypes.includes(b.trim())&&(x(R=>({...R,cuisineTypes:[...R.cuisineTypes,b.trim()]})),_(""))},D=R=>{x(T=>({...T,cuisineTypes:T.cuisineTypes.filter(N=>N!==R)}))},M=async R=>{if(R){m(!0),Y.info("Uploading restaurant image to Cloudinary...");try{const T=await qc(R,oa.restaurant.folder,{tags:oa.restaurant.tags,transformation:oa.restaurant.transformation});T.success?(v(T.url),Y.success("Restaurant image uploaded successfully!")):Y.error(T.error||"Failed to upload restaurant image")}catch(T){console.error("Restaurant image upload error:",T),Y.error("Failed to upload restaurant image")}finally{m(!1)}}},O=async R=>{if(R.preventDefault(),!c&&!g){Y.error("Please select and upload a restaurant image");return}if(c&&!g){await M(c),Y.info("Please submit again after image upload completes");return}const T=new FormData;T.append("name",p.name),T.append("description",p.description),T.append("address",p.address),T.append("phone",p.phone),T.append("email",p.email),T.append("deliveryTime",p.deliveryTime),T.append("deliveryFee",p.deliveryFee),T.append("minimumOrder",p.minimumOrder),T.append("cuisineTypes",JSON.stringify(p.cuisineTypes)),T.append("image",g);try{const w=await(await fetch(`${n}/api/restaurant/add`,{method:"POST",headers:{Authorization:`Bearer ${r}`},body:T})).json();w.success?(x({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[]}),d(null),v(""),Y.success(w.message)):Y.error(w.message)}catch(N){console.error("Error adding restaurant:",N),Y.error("Error adding restaurant")}};return s.jsx("div",{className:"add-restaurant",children:s.jsxs("form",{className:"flex-col",onSubmit:O,children:[s.jsxs("div",{className:"add-img-upload flex-col",children:[s.jsx("p",{children:"Upload Restaurant Image"}),s.jsx("label",{htmlFor:"image",children:s.jsx("img",{src:g||(c?URL.createObjectURL(c):"/api/placeholder/150/150"),alt:"Restaurant"})}),s.jsx("input",{onChange:R=>d(R.target.files[0]),type:"file",id:"image",hidden:!0,required:!0}),c&&!g&&s.jsx("div",{className:"upload-actions",children:s.jsx("button",{type:"button",onClick:()=>M(c),disabled:f,className:"upload-btn",children:f?"Uploading...":"Upload to Cloudinary"})}),g&&s.jsx("div",{className:"upload-success",children:s.jsx("span",{className:"success-text",children:"✅ Restaurant image uploaded to Cloudinary successfully!"})})]}),s.jsxs("div",{className:"add-restaurant-name flex-col",children:[s.jsx("p",{children:"Restaurant Name"}),s.jsx("input",{onChange:U,value:p.name,type:"text",name:"name",placeholder:"Type here",required:!0})]}),s.jsxs("div",{className:"add-restaurant-description flex-col",children:[s.jsx("p",{children:"Restaurant Description"}),s.jsx("textarea",{onChange:U,value:p.description,name:"description",rows:"6",placeholder:"Write restaurant description here",required:!0})]}),s.jsxs("div",{className:"add-restaurant-address flex-col",children:[s.jsx("p",{children:"Restaurant Address"}),s.jsx("input",{onChange:U,value:p.address,type:"text",name:"address",placeholder:"Restaurant address",required:!0})]}),s.jsxs("div",{className:"add-restaurant-contact flex-row",children:[s.jsxs("div",{className:"flex-col",children:[s.jsx("p",{children:"Phone Number"}),s.jsx("input",{onChange:U,value:p.phone,type:"tel",name:"phone",placeholder:"Phone number"})]}),s.jsxs("div",{className:"flex-col",children:[s.jsx("p",{children:"Email"}),s.jsx("input",{onChange:U,value:p.email,type:"email",name:"email",placeholder:"Email address"})]})]}),s.jsxs("div",{className:"add-restaurant-details flex-row",children:[s.jsxs("div",{className:"flex-col",children:[s.jsx("p",{children:"Delivery Time"}),s.jsx("input",{onChange:U,value:p.deliveryTime,type:"text",name:"deliveryTime",placeholder:"e.g., 30-45 mins"})]}),s.jsxs("div",{className:"flex-col",children:[s.jsx("p",{children:"Delivery Fee (₹)"}),s.jsx("input",{onChange:U,value:p.deliveryFee,type:"number",name:"deliveryFee",placeholder:"0",min:"0"})]}),s.jsxs("div",{className:"flex-col",children:[s.jsx("p",{children:"Minimum Order (₹)"}),s.jsx("input",{onChange:U,value:p.minimumOrder,type:"number",name:"minimumOrder",placeholder:"0",min:"0"})]})]}),s.jsxs("div",{className:"add-cuisine-types flex-col",children:[s.jsx("p",{children:"Cuisine Types"}),s.jsxs("div",{className:"cuisine-input-container",children:[s.jsx("input",{value:b,onChange:R=>_(R.target.value),type:"text",placeholder:"e.g., Indian, Chinese, Italian",onKeyPress:R=>R.key==="Enter"&&(R.preventDefault(),C())}),s.jsx("button",{type:"button",onClick:C,className:"add-cuisine-btn",children:"Add"})]}),p.cuisineTypes.length>0&&s.jsx("div",{className:"cuisine-tags",children:p.cuisineTypes.map((R,T)=>s.jsxs("span",{className:"cuisine-tag",children:[R,s.jsx("button",{type:"button",onClick:()=>D(R),className:"remove-cuisine",children:"×"})]},T))})]}),s.jsx("button",{type:"submit",className:"add-btn",children:"ADD RESTAURANT"})]})})},Yx=async(n,r="eatzone",u={})=>{var c;try{const d=new FormData;d.append("file",n),d.append("upload_preset","eatzone_admin"),r&&d.append("folder",r);const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const g=await f.json();throw new Error(((c=g.error)==null?void 0:c.message)||"Upload failed")}const m=await f.json();return{success:!0,url:m.secure_url,publicId:m.public_id}}catch(d){return{success:!1,error:d.message}}},Gx=(n,r)=>{if(!n)return"/api/placeholder/50/50";const u=String(n);if(u.includes("cloudinary.com")||u.startsWith("http")||u.startsWith("/")||u.startsWith("data:"))return u;if(u.includes(".png")||u.includes(".jpg")||u.includes(".jpeg")){const c=u.startsWith("/")?u.substring(1):u;return`${r}/images/${c}`}return`${r}/images/${u}`},Xx=({url:n,token:r})=>{const[u,c]=E.useState([]),[d,f]=E.useState(!0),[m,g]=E.useState(null),[v,p]=E.useState({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[],image:null}),[x,b]=E.useState(!1),[_,U]=E.useState(""),[C,D]=E.useState(""),M=async F=>{if(F){b(!0),Y.info("Uploading restaurant image to Cloudinary...");try{const ee=await Yx(F,"eatzone/restaurants",{tags:["restaurant","cover"]});ee.success?(U(ee.url),Y.success("Restaurant image uploaded successfully!")):Y.error(ee.error||"Failed to upload restaurant image")}catch(ee){console.error("Restaurant image upload error:",ee),Y.error("Failed to upload restaurant image")}finally{b(!1)}}},O=async()=>{try{f(!0);const ee=await(await fetch(`${n}/api/restaurant/list`)).json();ee.success?c(ee.data):Y.error("Error fetching restaurants")}catch(F){console.error("Error fetching restaurants:",F),Y.error("Error fetching restaurants")}finally{f(!1)}};E.useEffect(()=>{O()},[]);const R=async(F,ee)=>{if(window.confirm(`Are you sure you want to delete "${ee}"? This action cannot be undone.`))try{const $=await(await fetch(`${n}/api/restaurant/remove`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify({id:F})})).json();$.success?(Y.success("Restaurant removed successfully"),O()):Y.error($.message||"Error removing restaurant")}catch(Q){console.error("Error removing restaurant:",Q),Y.error("Error removing restaurant")}},T=F=>{var ee,Q;console.log("Starting edit for restaurant:",F),g(F),p({name:F.name||"",description:F.description||"",address:F.address||"",phone:F.phone||"",email:F.email||"",deliveryTime:F.deliveryTime||"30-45 mins",deliveryFee:((ee=F.deliveryFee)==null?void 0:ee.toString())||"0",minimumOrder:((Q=F.minimumOrder)==null?void 0:Q.toString())||"0",cuisineTypes:F.cuisineTypes||[],image:null}),D(F.cuisineTypes?F.cuisineTypes.join(", "):""),U(""),b(!1)},N=()=>{g(null),p({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[],image:null}),D(""),U(""),b(!1)},w=F=>{const{name:ee,value:Q,files:$}=F.target;p(ee==="image"?ne=>({...ne,image:$[0]}):ne=>({...ne,[ee]:Q}))},W=F=>{D(F.target.value);const ee=F.target.value.split(",").map(Q=>Q.trim()).filter(Q=>Q.length>0);p(Q=>({...Q,cuisineTypes:ee}))},P=async F=>{var ee,Q,$;if(F.preventDefault(),!v.name||!v.description||!v.address){Y.error("Please fill in all required fields");return}if(isNaN(v.deliveryFee)||Number(v.deliveryFee)<0){Y.error("Please enter a valid delivery fee");return}if(isNaN(v.minimumOrder)||Number(v.minimumOrder)<0){Y.error("Please enter a valid minimum order amount");return}try{console.log("Updating restaurant:",m._id),console.log("Form data:",v),Y.info("Updating restaurant...",{autoClose:1e3});const ne=new FormData;ne.append("id",m._id),ne.append("name",v.name.trim()),ne.append("description",v.description.trim()),ne.append("address",v.address.trim()),ne.append("phone",v.phone.trim()),ne.append("email",v.email.trim()),ne.append("deliveryTime",v.deliveryTime),ne.append("deliveryFee",v.deliveryFee),ne.append("minimumOrder",v.minimumOrder),ne.append("cuisineTypes",JSON.stringify(v.cuisineTypes)),_&&(console.log("Using Cloudinary URL:",_),ne.append("image",_));for(let ye of ne.entries())console.log(ye[0]+": "+ye[1]);const re=await(await fetch(`${n}/api/restaurant/update`,{method:"POST",headers:{Authorization:`Bearer ${r}`},body:ne})).json();console.log("Update response:",re),re.success?(Y.success(re.message||"Restaurant updated successfully"),await O(),N()):(console.error("Update failed:",re.message),Y.error(re.message||"Failed to update restaurant"))}catch(ne){console.error("Error updating restaurant:",ne),((ee=ne.response)==null?void 0:ee.status)===404?Y.error("Restaurant not found"):((Q=ne.response)==null?void 0:Q.status)===400?Y.error("Invalid data provided"):(($=ne.response)==null?void 0:$.status)===403?Y.error("You don't have permission to update this restaurant"):Y.error("Failed to update restaurant. Please try again.")}};return E.useEffect(()=>{O()},[]),d?s.jsx("div",{className:"restaurant-list",children:s.jsx("p",{children:"Loading restaurants..."})}):s.jsxs("div",{className:"restaurant-list",children:[s.jsxs("div",{className:"restaurant-list-header",children:[s.jsx("h2",{children:"All Restaurants"}),s.jsxs("p",{children:["Total: ",u.length," restaurants"]})]}),u.length===0?s.jsx("div",{className:"no-restaurants",children:s.jsx("p",{children:"No restaurants found. Add your first restaurant!"})}):s.jsxs("div",{className:"restaurant-list-table",children:[s.jsxs("div",{className:"restaurant-list-table-format title",children:[s.jsx("b",{children:"Image"}),s.jsx("b",{children:"Name"}),s.jsx("b",{children:"Description"}),s.jsx("b",{children:"Address"}),s.jsx("b",{children:"Delivery"}),s.jsx("b",{children:"Rating"}),s.jsx("b",{children:"Actions"})]}),u.map((F,ee)=>s.jsxs("div",{className:"restaurant-list-table-format",children:[s.jsx("img",{src:Gx(F.image,n),alt:F.name,onError:Q=>{console.error("Failed to load restaurant image:",F.image),Q.target.src="/api/placeholder/50/50"}}),s.jsx("p",{children:F.name}),s.jsx("p",{className:"description",children:F.description}),s.jsx("p",{className:"address",children:F.address}),s.jsxs("div",{className:"delivery-info",children:[s.jsx("p",{children:F.deliveryTime}),s.jsxs("p",{children:["₹",F.deliveryFee]})]}),s.jsx("div",{className:"rating",children:s.jsxs("span",{children:["⭐ ",F.rating]})}),s.jsxs("div",{className:"restaurant-actions",children:[s.jsx("button",{className:"action-btn edit-btn",title:"Edit Restaurant",onClick:()=>T(F),children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),s.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),s.jsx("button",{className:"action-btn delete-btn",title:"Delete Restaurant",onClick:()=>R(F._id,F.name),children:s.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("polyline",{points:"3,6 5,6 21,6"}),s.jsx("path",{d:"M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"}),s.jsx("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),s.jsx("line",{x1:"14",y1:"11",x2:"14",y2:"17"})]})})]})]},ee))]}),m&&s.jsx("div",{className:"edit-modal-overlay",onClick:N,children:s.jsxs("div",{className:"edit-modal",onClick:F=>F.stopPropagation(),children:[s.jsxs("div",{className:"edit-modal-header",children:[s.jsx("h3",{children:"Edit Restaurant"}),s.jsx("button",{className:"close-btn",onClick:N,children:s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),s.jsxs("form",{onSubmit:P,className:"edit-form",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-name",children:"Restaurant Name *"}),s.jsx("input",{type:"text",id:"edit-name",name:"name",value:v.name,onChange:w,placeholder:"Enter restaurant name",required:!0})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-description",children:"Description *"}),s.jsx("textarea",{id:"edit-description",name:"description",value:v.description,onChange:w,placeholder:"Enter restaurant description",rows:"3",required:!0})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-address",children:"Address *"}),s.jsx("textarea",{id:"edit-address",name:"address",value:v.address,onChange:w,placeholder:"Enter restaurant address",rows:"2",required:!0})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-phone",children:"Phone"}),s.jsx("input",{type:"tel",id:"edit-phone",name:"phone",value:v.phone,onChange:w,placeholder:"Enter phone number"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-email",children:"Email"}),s.jsx("input",{type:"email",id:"edit-email",name:"email",value:v.email,onChange:w,placeholder:"Enter email address"})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-delivery-time",children:"Delivery Time"}),s.jsx("input",{type:"text",id:"edit-delivery-time",name:"deliveryTime",value:v.deliveryTime,onChange:w,placeholder:"e.g., 30-45 mins"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-delivery-fee",children:"Delivery Fee (₹)"}),s.jsx("input",{type:"number",id:"edit-delivery-fee",name:"deliveryFee",value:v.deliveryFee,onChange:w,placeholder:"Enter delivery fee",min:"0"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-minimum-order",children:"Minimum Order (₹)"}),s.jsx("input",{type:"number",id:"edit-minimum-order",name:"minimumOrder",value:v.minimumOrder,onChange:w,placeholder:"Enter minimum order amount",min:"0"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-cuisine-types",children:"Cuisine Types"}),s.jsx("input",{type:"text",id:"edit-cuisine-types",value:C,onChange:W,placeholder:"Enter cuisine types separated by commas (e.g., Indian, Chinese, Italian)"}),s.jsxs("small",{children:["Separate multiple cuisines with commas. Current: ",v.cuisineTypes.length," cuisine(s)"]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"edit-image",children:"Restaurant Image"}),s.jsxs("div",{className:"image-upload-section",children:[_&&s.jsxs("div",{className:"current-image",children:[s.jsx("img",{src:_,alt:"Restaurant preview",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"8px"}}),s.jsx("p",{children:"New image uploaded to Cloudinary"})]}),s.jsx("input",{type:"file",id:"edit-image",name:"image",onChange:w,accept:"image/*"}),v.image&&!_&&s.jsx("div",{className:"upload-actions",children:s.jsx("button",{type:"button",onClick:()=>M(v.image),disabled:x,className:"upload-btn",style:{marginTop:"10px",padding:"8px 16px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:x?"not-allowed":"pointer"},children:x?"Uploading...":"Upload to Cloudinary"})}),s.jsx("small",{children:_?"Image uploaded to Cloudinary successfully":"Select an image and click 'Upload to Cloudinary' for faster loading, or leave empty to keep current image"})]})]}),s.jsxs("div",{className:"form-actions",children:[s.jsx("button",{type:"button",className:"btn-cancel",onClick:N,children:"Cancel"}),s.jsx("button",{type:"submit",className:"btn-save",children:"Update Restaurant"})]})]})]})})]})},Vx=()=>{const[n,r]=E.useState(!1),u="https://eatzone.onrender.com";return s.jsx(kx,{children:s.jsxs("div",{className:"app",children:[s.jsx(Zv,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0}),s.jsx(Fv,{setSidebarOpen:r}),s.jsxs("div",{className:"app-content",children:[s.jsx(Kv,{isOpen:n,setSidebarOpen:r}),s.jsx("main",{className:"main-content",children:s.jsxs(M0,{children:[s.jsx(St,{path:"/",element:s.jsx(um,{url:u})}),s.jsx(St,{path:"/dashboard",element:s.jsx(um,{url:u})}),s.jsx(St,{path:"/add",element:s.jsx(_x,{url:u})}),s.jsx(St,{path:"/list",element:s.jsx(Cx,{url:u})}),s.jsx(St,{path:"/orders",element:s.jsx(Ax,{url:u})}),s.jsx(St,{path:"/analytics",element:s.jsx(Ox,{url:u})}),s.jsx(St,{path:"/categories",element:s.jsx(zx,{url:u})}),s.jsx(St,{path:"/delivery-partners",element:s.jsx(Ux,{url:u})}),s.jsx(St,{path:"/feedback",element:s.jsx(Bx,{url:u})}),s.jsx(St,{path:"/add-restaurant",element:s.jsx(qx,{url:u})}),s.jsx(St,{path:"/restaurants",element:s.jsx(Xx,{url:u})}),s.jsx(St,{path:"*",element:s.jsx(O0,{to:"/dashboard",replace:!0})})]})})]})]})})};function dm(){const n=document.getElementById("root");if(!n){console.error("❌ Root element not found");return}try{Yg.createRoot(n).render(s.jsx(Me.StrictMode,{children:s.jsx(lv,{children:s.jsx(Vx,{})})})),console.log("✅ Admin app initialized successfully")}catch(r){console.error("❌ Failed to initialize admin app:",r)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",dm):dm();
