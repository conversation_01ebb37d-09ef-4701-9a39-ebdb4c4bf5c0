[build]
  base = "client"
  command = "npm install --include=dev && npx vite build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
  VITE_API_BASE_URL = "https://eatzone.onrender.com"
  VITE_APP_ENV = "production"
  NODE_ENV = "production"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
