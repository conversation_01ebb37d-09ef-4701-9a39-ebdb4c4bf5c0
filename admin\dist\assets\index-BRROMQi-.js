(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const f of d)if(f.type==="childList")for(const p of f.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&c(p)}).observe(document,{childList:!0,subtree:!0});function u(d){const f={};return d.integrity&&(f.integrity=d.integrity),d.referrerPolicy&&(f.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?f.credentials="include":d.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function c(d){if(d.ep)return;d.ep=!0;const f=u(d);fetch(d.href,f)}})();function D0(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var rc={exports:{}},Kn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh;function M0(){if(Uh)return Kn;Uh=1;var s=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(c,d,f){var p=null;if(f!==void 0&&(p=""+f),d.key!==void 0&&(p=""+d.key),"key"in d){f={};for(var g in d)g!=="key"&&(f[g]=d[g])}else f=d;return d=f.ref,{$$typeof:s,type:c,key:p,ref:d!==void 0?d:null,props:f}}return Kn.Fragment=r,Kn.jsx=u,Kn.jsxs=u,Kn}var Bh;function z0(){return Bh||(Bh=1,rc.exports=M0()),rc.exports}var n=z0(),oc={exports:{}},ge={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh;function U0(){if(Lh)return ge;Lh=1;var s=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),p=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),b=Symbol.iterator;function _(S){return S===null||typeof S!="object"?null:(S=b&&S[b]||S["@@iterator"],typeof S=="function"?S:null)}var U={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,z={};function D(S,V,le){this.props=S,this.context=V,this.refs=z,this.updater=le||U}D.prototype.isReactComponent={},D.prototype.setState=function(S,V){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,V,"setState")},D.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function C(){}C.prototype=D.prototype;function A(S,V,le){this.props=S,this.context=V,this.refs=z,this.updater=le||U}var T=A.prototype=new C;T.constructor=A,w(T,D.prototype),T.isPureReactComponent=!0;var N=Array.isArray,R={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function P(S,V,le,ae,I,xe){return le=xe.ref,{$$typeof:s,type:S,key:V,ref:le!==void 0?le:null,props:xe}}function F(S,V){return P(S.type,V,void 0,void 0,void 0,S.props)}function ee(S){return typeof S=="object"&&S!==null&&S.$$typeof===s}function Q(S){var V={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(le){return V[le]})}var $=/\/+/g;function ne(S,V){return typeof S=="object"&&S!==null&&S.key!=null?Q(""+S.key):V.toString(36)}function ce(){}function re(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(ce,ce):(S.status="pending",S.then(function(V){S.status==="pending"&&(S.status="fulfilled",S.value=V)},function(V){S.status==="pending"&&(S.status="rejected",S.reason=V)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function ye(S,V,le,ae,I){var xe=typeof S;(xe==="undefined"||xe==="boolean")&&(S=null);var he=!1;if(S===null)he=!0;else switch(xe){case"bigint":case"string":case"number":he=!0;break;case"object":switch(S.$$typeof){case s:case r:he=!0;break;case x:return he=S._init,ye(he(S._payload),V,le,ae,I)}}if(he)return I=I(S),he=ae===""?"."+ne(S,0):ae,N(I)?(le="",he!=null&&(le=he.replace($,"$&/")+"/"),ye(I,V,le,"",function(da){return da})):I!=null&&(ee(I)&&(I=F(I,le+(I.key==null||S&&S.key===I.key?"":(""+I.key).replace($,"$&/")+"/")+he)),V.push(I)),1;he=0;var ft=ae===""?".":ae+":";if(N(S))for(var ze=0;ze<S.length;ze++)ae=S[ze],xe=ft+ne(ae,ze),he+=ye(ae,V,le,xe,I);else if(ze=_(S),typeof ze=="function")for(S=ze.call(S),ze=0;!(ae=S.next()).done;)ae=ae.value,xe=ft+ne(ae,ze++),he+=ye(ae,V,le,xe,I);else if(xe==="object"){if(typeof S.then=="function")return ye(re(S),V,le,ae,I);throw V=String(S),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.")}return he}function X(S,V,le){if(S==null)return S;var ae=[],I=0;return ye(S,ae,"","",function(xe){return V.call(le,xe,I++)}),ae}function te(S){if(S._status===-1){var V=S._result;V=V(),V.then(function(le){(S._status===0||S._status===-1)&&(S._status=1,S._result=le)},function(le){(S._status===0||S._status===-1)&&(S._status=2,S._result=le)}),S._status===-1&&(S._status=0,S._result=V)}if(S._status===1)return S._result.default;throw S._result}var q=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var V=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(V))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function se(){}return ge.Children={map:X,forEach:function(S,V,le){X(S,function(){V.apply(this,arguments)},le)},count:function(S){var V=0;return X(S,function(){V++}),V},toArray:function(S){return X(S,function(V){return V})||[]},only:function(S){if(!ee(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ge.Component=D,ge.Fragment=u,ge.Profiler=d,ge.PureComponent=A,ge.StrictMode=c,ge.Suspense=v,ge.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=R,ge.__COMPILER_RUNTIME={__proto__:null,c:function(S){return R.H.useMemoCache(S)}},ge.cache=function(S){return function(){return S.apply(null,arguments)}},ge.cloneElement=function(S,V,le){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var ae=w({},S.props),I=S.key,xe=void 0;if(V!=null)for(he in V.ref!==void 0&&(xe=void 0),V.key!==void 0&&(I=""+V.key),V)!W.call(V,he)||he==="key"||he==="__self"||he==="__source"||he==="ref"&&V.ref===void 0||(ae[he]=V[he]);var he=arguments.length-2;if(he===1)ae.children=le;else if(1<he){for(var ft=Array(he),ze=0;ze<he;ze++)ft[ze]=arguments[ze+2];ae.children=ft}return P(S.type,I,void 0,void 0,xe,ae)},ge.createContext=function(S){return S={$$typeof:p,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:f,_context:S},S},ge.createElement=function(S,V,le){var ae,I={},xe=null;if(V!=null)for(ae in V.key!==void 0&&(xe=""+V.key),V)W.call(V,ae)&&ae!=="key"&&ae!=="__self"&&ae!=="__source"&&(I[ae]=V[ae]);var he=arguments.length-2;if(he===1)I.children=le;else if(1<he){for(var ft=Array(he),ze=0;ze<he;ze++)ft[ze]=arguments[ze+2];I.children=ft}if(S&&S.defaultProps)for(ae in he=S.defaultProps,he)I[ae]===void 0&&(I[ae]=he[ae]);return P(S,xe,void 0,void 0,null,I)},ge.createRef=function(){return{current:null}},ge.forwardRef=function(S){return{$$typeof:g,render:S}},ge.isValidElement=ee,ge.lazy=function(S){return{$$typeof:x,_payload:{_status:-1,_result:S},_init:te}},ge.memo=function(S,V){return{$$typeof:m,type:S,compare:V===void 0?null:V}},ge.startTransition=function(S){var V=R.T,le={};R.T=le;try{var ae=S(),I=R.S;I!==null&&I(le,ae),typeof ae=="object"&&ae!==null&&typeof ae.then=="function"&&ae.then(se,q)}catch(xe){q(xe)}finally{R.T=V}},ge.unstable_useCacheRefresh=function(){return R.H.useCacheRefresh()},ge.use=function(S){return R.H.use(S)},ge.useActionState=function(S,V,le){return R.H.useActionState(S,V,le)},ge.useCallback=function(S,V){return R.H.useCallback(S,V)},ge.useContext=function(S){return R.H.useContext(S)},ge.useDebugValue=function(){},ge.useDeferredValue=function(S,V){return R.H.useDeferredValue(S,V)},ge.useEffect=function(S,V,le){var ae=R.H;if(typeof le=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ae.useEffect(S,V)},ge.useId=function(){return R.H.useId()},ge.useImperativeHandle=function(S,V,le){return R.H.useImperativeHandle(S,V,le)},ge.useInsertionEffect=function(S,V){return R.H.useInsertionEffect(S,V)},ge.useLayoutEffect=function(S,V){return R.H.useLayoutEffect(S,V)},ge.useMemo=function(S,V){return R.H.useMemo(S,V)},ge.useOptimistic=function(S,V){return R.H.useOptimistic(S,V)},ge.useReducer=function(S,V,le){return R.H.useReducer(S,V,le)},ge.useRef=function(S){return R.H.useRef(S)},ge.useState=function(S){return R.H.useState(S)},ge.useSyncExternalStore=function(S,V,le){return R.H.useSyncExternalStore(S,V,le)},ge.useTransition=function(){return R.H.useTransition()},ge.version="19.1.0",ge}var kh;function Rc(){return kh||(kh=1,oc.exports=U0()),oc.exports}var E=Rc();const Me=D0(E);var cc={exports:{}},Jn={},uc={exports:{}},dc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hh;function B0(){return Hh||(Hh=1,function(s){function r(X,te){var q=X.length;X.push(te);e:for(;0<q;){var se=q-1>>>1,S=X[se];if(0<d(S,te))X[se]=te,X[q]=S,q=se;else break e}}function u(X){return X.length===0?null:X[0]}function c(X){if(X.length===0)return null;var te=X[0],q=X.pop();if(q!==te){X[0]=q;e:for(var se=0,S=X.length,V=S>>>1;se<V;){var le=2*(se+1)-1,ae=X[le],I=le+1,xe=X[I];if(0>d(ae,q))I<S&&0>d(xe,ae)?(X[se]=xe,X[I]=q,se=I):(X[se]=ae,X[le]=q,se=le);else if(I<S&&0>d(xe,q))X[se]=xe,X[I]=q,se=I;else break e}}return te}function d(X,te){var q=X.sortIndex-te.sortIndex;return q!==0?q:X.id-te.id}if(s.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;s.unstable_now=function(){return f.now()}}else{var p=Date,g=p.now();s.unstable_now=function(){return p.now()-g}}var v=[],m=[],x=1,b=null,_=3,U=!1,w=!1,z=!1,D=!1,C=typeof setTimeout=="function"?setTimeout:null,A=typeof clearTimeout=="function"?clearTimeout:null,T=typeof setImmediate<"u"?setImmediate:null;function N(X){for(var te=u(m);te!==null;){if(te.callback===null)c(m);else if(te.startTime<=X)c(m),te.sortIndex=te.expirationTime,r(v,te);else break;te=u(m)}}function R(X){if(z=!1,N(X),!w)if(u(v)!==null)w=!0,W||(W=!0,ne());else{var te=u(m);te!==null&&ye(R,te.startTime-X)}}var W=!1,P=-1,F=5,ee=-1;function Q(){return D?!0:!(s.unstable_now()-ee<F)}function $(){if(D=!1,W){var X=s.unstable_now();ee=X;var te=!0;try{e:{w=!1,z&&(z=!1,A(P),P=-1),U=!0;var q=_;try{t:{for(N(X),b=u(v);b!==null&&!(b.expirationTime>X&&Q());){var se=b.callback;if(typeof se=="function"){b.callback=null,_=b.priorityLevel;var S=se(b.expirationTime<=X);if(X=s.unstable_now(),typeof S=="function"){b.callback=S,N(X),te=!0;break t}b===u(v)&&c(v),N(X)}else c(v);b=u(v)}if(b!==null)te=!0;else{var V=u(m);V!==null&&ye(R,V.startTime-X),te=!1}}break e}finally{b=null,_=q,U=!1}te=void 0}}finally{te?ne():W=!1}}}var ne;if(typeof T=="function")ne=function(){T($)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,re=ce.port2;ce.port1.onmessage=$,ne=function(){re.postMessage(null)}}else ne=function(){C($,0)};function ye(X,te){P=C(function(){X(s.unstable_now())},te)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(X){X.callback=null},s.unstable_forceFrameRate=function(X){0>X||125<X?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<X?Math.floor(1e3/X):5},s.unstable_getCurrentPriorityLevel=function(){return _},s.unstable_next=function(X){switch(_){case 1:case 2:case 3:var te=3;break;default:te=_}var q=_;_=te;try{return X()}finally{_=q}},s.unstable_requestPaint=function(){D=!0},s.unstable_runWithPriority=function(X,te){switch(X){case 1:case 2:case 3:case 4:case 5:break;default:X=3}var q=_;_=X;try{return te()}finally{_=q}},s.unstable_scheduleCallback=function(X,te,q){var se=s.unstable_now();switch(typeof q=="object"&&q!==null?(q=q.delay,q=typeof q=="number"&&0<q?se+q:se):q=se,X){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=q+S,X={id:x++,callback:te,priorityLevel:X,startTime:q,expirationTime:S,sortIndex:-1},q>se?(X.sortIndex=q,r(m,X),u(v)===null&&X===u(m)&&(z?(A(P),P=-1):z=!0,ye(R,q-se))):(X.sortIndex=S,r(v,X),w||U||(w=!0,W||(W=!0,ne()))),X},s.unstable_shouldYield=Q,s.unstable_wrapCallback=function(X){var te=_;return function(){var q=_;_=te;try{return X.apply(this,arguments)}finally{_=q}}}}(dc)),dc}var qh;function L0(){return qh||(qh=1,uc.exports=B0()),uc.exports}var fc={exports:{}},tt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yh;function k0(){if(Yh)return tt;Yh=1;var s=Rc();function r(v){var m="https://react.dev/errors/"+v;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)m+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+v+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var c={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},d=Symbol.for("react.portal");function f(v,m,x){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:b==null?null:""+b,children:v,containerInfo:m,implementation:x}}var p=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(v,m){if(v==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return tt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,tt.createPortal=function(v,m){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(r(299));return f(v,m,null,x)},tt.flushSync=function(v){var m=p.T,x=c.p;try{if(p.T=null,c.p=2,v)return v()}finally{p.T=m,c.p=x,c.d.f()}},tt.preconnect=function(v,m){typeof v=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,c.d.C(v,m))},tt.prefetchDNS=function(v){typeof v=="string"&&c.d.D(v)},tt.preinit=function(v,m){if(typeof v=="string"&&m&&typeof m.as=="string"){var x=m.as,b=g(x,m.crossOrigin),_=typeof m.integrity=="string"?m.integrity:void 0,U=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;x==="style"?c.d.S(v,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:b,integrity:_,fetchPriority:U}):x==="script"&&c.d.X(v,{crossOrigin:b,integrity:_,fetchPriority:U,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},tt.preinitModule=function(v,m){if(typeof v=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var x=g(m.as,m.crossOrigin);c.d.M(v,{crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&c.d.M(v)},tt.preload=function(v,m){if(typeof v=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var x=m.as,b=g(x,m.crossOrigin);c.d.L(v,x,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},tt.preloadModule=function(v,m){if(typeof v=="string")if(m){var x=g(m.as,m.crossOrigin);c.d.m(v,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else c.d.m(v)},tt.requestFormReset=function(v){c.d.r(v)},tt.unstable_batchedUpdates=function(v,m){return v(m)},tt.useFormState=function(v,m,x){return p.H.useFormState(v,m,x)},tt.useFormStatus=function(){return p.H.useHostTransitionStatus()},tt.version="19.1.0",tt}var Gh;function H0(){if(Gh)return fc.exports;Gh=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(r){console.error(r)}}return s(),fc.exports=k0(),fc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xh;function q0(){if(Xh)return Jn;Xh=1;var s=L0(),r=Rc(),u=H0();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function p(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(f(e)!==e)throw Error(c(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,l=t;;){var i=a.return;if(i===null)break;var o=i.alternate;if(o===null){if(l=i.return,l!==null){a=l;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===a)return g(i),e;if(o===l)return g(i),t;o=o.sibling}throw Error(c(188))}if(a.return!==l.return)a=i,l=o;else{for(var h=!1,y=i.child;y;){if(y===a){h=!0,a=i,l=o;break}if(y===l){h=!0,l=i,a=o;break}y=y.sibling}if(!h){for(y=o.child;y;){if(y===a){h=!0,a=o,l=i;break}if(y===l){h=!0,l=o,a=i;break}y=y.sibling}if(!h)throw Error(c(189))}}if(a.alternate!==l)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,b=Symbol.for("react.element"),_=Symbol.for("react.transitional.element"),U=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),A=Symbol.for("react.consumer"),T=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),ee=Symbol.for("react.activity"),Q=Symbol.for("react.memo_cache_sentinel"),$=Symbol.iterator;function ne(e){return e===null||typeof e!="object"?null:(e=$&&e[$]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Symbol.for("react.client.reference");function re(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ce?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case w:return"Fragment";case D:return"Profiler";case z:return"StrictMode";case R:return"Suspense";case W:return"SuspenseList";case ee:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case U:return"Portal";case T:return(e.displayName||"Context")+".Provider";case A:return(e._context.displayName||"Context")+".Consumer";case N:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:re(e.type)||"Memo";case F:t=e._payload,e=e._init;try{return re(e(t))}catch{}}return null}var ye=Array.isArray,X=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q={pending:!1,data:null,method:null,action:null},se=[],S=-1;function V(e){return{current:e}}function le(e){0>S||(e.current=se[S],se[S]=null,S--)}function ae(e,t){S++,se[S]=e.current,e.current=t}var I=V(null),xe=V(null),he=V(null),ft=V(null);function ze(e,t){switch(ae(he,t),ae(xe,e),ae(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ch(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=ch(t),e=uh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}le(I),ae(I,e)}function da(){le(I),le(xe),le(he)}function Qi(e){e.memoizedState!==null&&ae(ft,e);var t=I.current,a=uh(t,e.type);t!==a&&(ae(xe,e),ae(I,a))}function os(e){xe.current===e&&(le(I),le(xe)),ft.current===e&&(le(ft),Xn._currentValue=q)}var Zi=Object.prototype.hasOwnProperty,Fi=s.unstable_scheduleCallback,Ki=s.unstable_cancelCallback,up=s.unstable_shouldYield,dp=s.unstable_requestPaint,kt=s.unstable_now,fp=s.unstable_getCurrentPriorityLevel,Gc=s.unstable_ImmediatePriority,Xc=s.unstable_UserBlockingPriority,cs=s.unstable_NormalPriority,hp=s.unstable_LowPriority,Vc=s.unstable_IdlePriority,mp=s.log,pp=s.unstable_setDisableYieldValue,$l=null,ht=null;function fa(e){if(typeof mp=="function"&&pp(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode($l,e)}catch{}}var mt=Math.clz32?Math.clz32:vp,yp=Math.log,gp=Math.LN2;function vp(e){return e>>>=0,e===0?32:31-(yp(e)/gp|0)|0}var us=256,ds=4194304;function La(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function fs(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var i=0,o=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~o,l!==0?i=La(l):(h&=y,h!==0?i=La(h):a||(a=y&~e,a!==0&&(i=La(a))))):(y=l&~o,y!==0?i=La(y):h!==0?i=La(h):a||(a=l&~e,a!==0&&(i=La(a)))),i===0?0:t!==0&&t!==i&&(t&o)===0&&(o=i&-i,a=t&-t,o>=a||o===32&&(a&4194048)!==0)?t:i}function Wl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function xp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Qc(){var e=us;return us<<=1,(us&4194048)===0&&(us=256),e}function Zc(){var e=ds;return ds<<=1,(ds&62914560)===0&&(ds=4194304),e}function Ji(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Pl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function bp(e,t,a,l,i,o){var h=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var y=e.entanglements,j=e.expirationTimes,L=e.hiddenUpdates;for(a=h&~a;0<a;){var Z=31-mt(a),J=1<<Z;y[Z]=0,j[Z]=-1;var k=L[Z];if(k!==null)for(L[Z]=null,Z=0;Z<k.length;Z++){var H=k[Z];H!==null&&(H.lane&=-536870913)}a&=~J}l!==0&&Fc(e,l,0),o!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=o&~(h&~t))}function Fc(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-mt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Kc(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-mt(a),i=1<<l;i&t|e[l]&t&&(e[l]|=t),a&=~i}}function $i(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Wi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Jc(){var e=te.p;return e!==0?e:(e=window.event,e===void 0?32:Ah(e.type))}function jp(e,t){var a=te.p;try{return te.p=e,t()}finally{te.p=a}}var ha=Math.random().toString(36).slice(2),Ie="__reactFiber$"+ha,st="__reactProps$"+ha,il="__reactContainer$"+ha,Pi="__reactEvents$"+ha,Sp="__reactListeners$"+ha,Np="__reactHandles$"+ha,$c="__reactResources$"+ha,Il="__reactMarker$"+ha;function Ii(e){delete e[Ie],delete e[st],delete e[Pi],delete e[Sp],delete e[Np]}function rl(e){var t=e[Ie];if(t)return t;for(var a=e.parentNode;a;){if(t=a[il]||a[Ie]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=mh(e);e!==null;){if(a=e[Ie])return a;e=mh(e)}return t}e=a,a=e.parentNode}return null}function ol(e){if(e=e[Ie]||e[il]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function en(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function cl(e){var t=e[$c];return t||(t=e[$c]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[Il]=!0}var Wc=new Set,Pc={};function ka(e,t){ul(e,t),ul(e+"Capture",t)}function ul(e,t){for(Pc[e]=t,e=0;e<t.length;e++)Wc.add(t[e])}var Ep=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ic={},eu={};function Tp(e){return Zi.call(eu,e)?!0:Zi.call(Ic,e)?!1:Ep.test(e)?eu[e]=!0:(Ic[e]=!0,!1)}function hs(e,t,a){if(Tp(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function ms(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Zt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var er,tu;function dl(e){if(er===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);er=t&&t[1]||"",tu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+er+e+tu}var tr=!1;function ar(e,t){if(!e||tr)return"";tr=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var J=function(){throw Error()};if(Object.defineProperty(J.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(J,[])}catch(H){var k=H}Reflect.construct(e,[],J)}else{try{J.call()}catch(H){k=H}e.call(J.prototype)}}else{try{throw Error()}catch(H){k=H}(J=e())&&typeof J.catch=="function"&&J.catch(function(){})}}catch(H){if(H&&k&&typeof H.stack=="string")return[H.stack,k.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),h=o[0],y=o[1];if(h&&y){var j=h.split(`
`),L=y.split(`
`);for(i=l=0;l<j.length&&!j[l].includes("DetermineComponentFrameRoot");)l++;for(;i<L.length&&!L[i].includes("DetermineComponentFrameRoot");)i++;if(l===j.length||i===L.length)for(l=j.length-1,i=L.length-1;1<=l&&0<=i&&j[l]!==L[i];)i--;for(;1<=l&&0<=i;l--,i--)if(j[l]!==L[i]){if(l!==1||i!==1)do if(l--,i--,0>i||j[l]!==L[i]){var Z=`
`+j[l].replace(" at new "," at ");return e.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",e.displayName)),Z}while(1<=l&&0<=i);break}}}finally{tr=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?dl(a):""}function _p(e){switch(e.tag){case 26:case 27:case 5:return dl(e.type);case 16:return dl("Lazy");case 13:return dl("Suspense");case 19:return dl("SuspenseList");case 0:case 15:return ar(e.type,!1);case 11:return ar(e.type.render,!1);case 1:return ar(e.type,!0);case 31:return dl("Activity");default:return""}}function au(e){try{var t="";do t+=_p(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Nt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function lu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wp(e){var t=lu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,o=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){l=""+h,o.call(this,h)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ps(e){e._valueTracker||(e._valueTracker=wp(e))}function nu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=lu(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function ys(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Cp=/[\n"\\]/g;function Et(e){return e.replace(Cp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function lr(e,t,a,l,i,o,h,y){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Nt(t)):e.value!==""+Nt(t)&&(e.value=""+Nt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?nr(e,h,Nt(t)):a!=null?nr(e,h,Nt(a)):l!=null&&e.removeAttribute("value"),i==null&&o!=null&&(e.defaultChecked=!!o),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+Nt(y):e.removeAttribute("name")}function su(e,t,a,l,i,o,h,y){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||a!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;a=a!=null?""+Nt(a):"",t=t!=null?""+Nt(t):a,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function nr(e,t,a){t==="number"&&ys(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function fl(e,t,a,l){if(e=e.options,t){t={};for(var i=0;i<a.length;i++)t["$"+a[i]]=!0;for(a=0;a<e.length;a++)i=t.hasOwnProperty("$"+e[a].value),e[a].selected!==i&&(e[a].selected=i),i&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Nt(a),t=null,i=0;i<e.length;i++){if(e[i].value===a){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function iu(e,t,a){if(t!=null&&(t=""+Nt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Nt(a):""}function ru(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(c(92));if(ye(l)){if(1<l.length)throw Error(c(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Nt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function hl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Ap=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ou(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Ap.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function cu(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&a[i]!==l&&ou(e,i,l)}else for(var o in t)t.hasOwnProperty(o)&&ou(e,o,t[o])}function sr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Op=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function gs(e){return Op.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ir=null;function rr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ml=null,pl=null;function uu(e){var t=ol(e);if(t&&(e=t.stateNode)){var a=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(lr(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Et(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var i=l[st]||null;if(!i)throw Error(c(90));lr(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&nu(l)}break e;case"textarea":iu(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&fl(e,!!a.multiple,t,!1)}}}var or=!1;function du(e,t,a){if(or)return e(t,a);or=!0;try{var l=e(t);return l}finally{if(or=!1,(ml!==null||pl!==null)&&(ai(),ml&&(t=ml,e=pl,pl=ml=null,uu(t),e)))for(t=0;t<e.length;t++)uu(e[t])}}function tn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[st]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var Ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),cr=!1;if(Ft)try{var an={};Object.defineProperty(an,"passive",{get:function(){cr=!0}}),window.addEventListener("test",an,an),window.removeEventListener("test",an,an)}catch{cr=!1}var ma=null,ur=null,vs=null;function fu(){if(vs)return vs;var e,t=ur,a=t.length,l,i="value"in ma?ma.value:ma.textContent,o=i.length;for(e=0;e<a&&t[e]===i[e];e++);var h=a-e;for(l=1;l<=h&&t[a-l]===i[o-l];l++);return vs=i.slice(e,1<l?1-l:void 0)}function xs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bs(){return!0}function hu(){return!1}function it(e){function t(a,l,i,o,h){this._reactName=a,this._targetInst=i,this.type=l,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(a=e[y],this[y]=a?a(o):o[y]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?bs:hu,this.isPropagationStopped=hu,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=bs)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=bs)},persist:function(){},isPersistent:bs}),t}var Ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},js=it(Ha),ln=x({},Ha,{view:0,detail:0}),Dp=it(ln),dr,fr,nn,Ss=x({},ln,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nn&&(nn&&e.type==="mousemove"?(dr=e.screenX-nn.screenX,fr=e.screenY-nn.screenY):fr=dr=0,nn=e),dr)},movementY:function(e){return"movementY"in e?e.movementY:fr}}),mu=it(Ss),Mp=x({},Ss,{dataTransfer:0}),zp=it(Mp),Up=x({},ln,{relatedTarget:0}),hr=it(Up),Bp=x({},Ha,{animationName:0,elapsedTime:0,pseudoElement:0}),Lp=it(Bp),kp=x({},Ha,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hp=it(kp),qp=x({},Ha,{data:0}),pu=it(qp),Yp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Xp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Xp[e])?!!t[e]:!1}function mr(){return Vp}var Qp=x({},ln,{key:function(e){if(e.key){var t=Yp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Gp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mr,charCode:function(e){return e.type==="keypress"?xs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Zp=it(Qp),Fp=x({},Ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),yu=it(Fp),Kp=x({},ln,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mr}),Jp=it(Kp),$p=x({},Ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),Wp=it($p),Pp=x({},Ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ip=it(Pp),ey=x({},Ha,{newState:0,oldState:0}),ty=it(ey),ay=[9,13,27,32],pr=Ft&&"CompositionEvent"in window,sn=null;Ft&&"documentMode"in document&&(sn=document.documentMode);var ly=Ft&&"TextEvent"in window&&!sn,gu=Ft&&(!pr||sn&&8<sn&&11>=sn),vu=" ",xu=!1;function bu(e,t){switch(e){case"keyup":return ay.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ju(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yl=!1;function ny(e,t){switch(e){case"compositionend":return ju(t);case"keypress":return t.which!==32?null:(xu=!0,vu);case"textInput":return e=t.data,e===vu&&xu?null:e;default:return null}}function sy(e,t){if(yl)return e==="compositionend"||!pr&&bu(e,t)?(e=fu(),vs=ur=ma=null,yl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return gu&&t.locale!=="ko"?null:t.data;default:return null}}var iy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Su(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!iy[e.type]:t==="textarea"}function Nu(e,t,a,l){ml?pl?pl.push(l):pl=[l]:ml=l,t=oi(t,"onChange"),0<t.length&&(a=new js("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var rn=null,on=null;function ry(e){nh(e,0)}function Ns(e){var t=en(e);if(nu(t))return e}function Eu(e,t){if(e==="change")return t}var Tu=!1;if(Ft){var yr;if(Ft){var gr="oninput"in document;if(!gr){var _u=document.createElement("div");_u.setAttribute("oninput","return;"),gr=typeof _u.oninput=="function"}yr=gr}else yr=!1;Tu=yr&&(!document.documentMode||9<document.documentMode)}function wu(){rn&&(rn.detachEvent("onpropertychange",Cu),on=rn=null)}function Cu(e){if(e.propertyName==="value"&&Ns(on)){var t=[];Nu(t,on,e,rr(e)),du(ry,t)}}function oy(e,t,a){e==="focusin"?(wu(),rn=t,on=a,rn.attachEvent("onpropertychange",Cu)):e==="focusout"&&wu()}function cy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ns(on)}function uy(e,t){if(e==="click")return Ns(t)}function dy(e,t){if(e==="input"||e==="change")return Ns(t)}function fy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:fy;function cn(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var i=a[l];if(!Zi.call(t,i)||!pt(e[i],t[i]))return!1}return!0}function Au(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ru(e,t){var a=Au(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Au(a)}}function Ou(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ou(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Du(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ys(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=ys(e.document)}return t}function vr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var hy=Ft&&"documentMode"in document&&11>=document.documentMode,gl=null,xr=null,un=null,br=!1;function Mu(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;br||gl==null||gl!==ys(l)||(l=gl,"selectionStart"in l&&vr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),un&&cn(un,l)||(un=l,l=oi(xr,"onSelect"),0<l.length&&(t=new js("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=gl)))}function qa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var vl={animationend:qa("Animation","AnimationEnd"),animationiteration:qa("Animation","AnimationIteration"),animationstart:qa("Animation","AnimationStart"),transitionrun:qa("Transition","TransitionRun"),transitionstart:qa("Transition","TransitionStart"),transitioncancel:qa("Transition","TransitionCancel"),transitionend:qa("Transition","TransitionEnd")},jr={},zu={};Ft&&(zu=document.createElement("div").style,"AnimationEvent"in window||(delete vl.animationend.animation,delete vl.animationiteration.animation,delete vl.animationstart.animation),"TransitionEvent"in window||delete vl.transitionend.transition);function Ya(e){if(jr[e])return jr[e];if(!vl[e])return e;var t=vl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in zu)return jr[e]=t[a];return e}var Uu=Ya("animationend"),Bu=Ya("animationiteration"),Lu=Ya("animationstart"),my=Ya("transitionrun"),py=Ya("transitionstart"),yy=Ya("transitioncancel"),ku=Ya("transitionend"),Hu=new Map,Sr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Sr.push("scrollEnd");function Dt(e,t){Hu.set(e,t),ka(t,[e])}var qu=new WeakMap;function Tt(e,t){if(typeof e=="object"&&e!==null){var a=qu.get(e);return a!==void 0?a:(t={value:e,source:t,stack:au(t)},qu.set(e,t),t)}return{value:e,source:t,stack:au(t)}}var _t=[],xl=0,Nr=0;function Es(){for(var e=xl,t=Nr=xl=0;t<e;){var a=_t[t];_t[t++]=null;var l=_t[t];_t[t++]=null;var i=_t[t];_t[t++]=null;var o=_t[t];if(_t[t++]=null,l!==null&&i!==null){var h=l.pending;h===null?i.next=i:(i.next=h.next,h.next=i),l.pending=i}o!==0&&Yu(a,i,o)}}function Ts(e,t,a,l){_t[xl++]=e,_t[xl++]=t,_t[xl++]=a,_t[xl++]=l,Nr|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Er(e,t,a,l){return Ts(e,t,a,l),_s(e)}function bl(e,t){return Ts(e,null,null,t),_s(e)}function Yu(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var i=!1,o=e.return;o!==null;)o.childLanes|=a,l=o.alternate,l!==null&&(l.childLanes|=a),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(i=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,i&&t!==null&&(i=31-mt(a),e=o.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=a|536870912),o):null}function _s(e){if(50<Un)throw Un=0,Oo=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var jl={};function gy(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,a,l){return new gy(e,t,a,l)}function Tr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Kt(e,t){var a=e.alternate;return a===null?(a=yt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Gu(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ws(e,t,a,l,i,o){var h=0;if(l=e,typeof e=="function")Tr(e)&&(h=1);else if(typeof e=="string")h=x0(e,a,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ee:return e=yt(31,a,t,i),e.elementType=ee,e.lanes=o,e;case w:return Ga(a.children,i,o,t);case z:h=8,i|=24;break;case D:return e=yt(12,a,t,i|2),e.elementType=D,e.lanes=o,e;case R:return e=yt(13,a,t,i),e.elementType=R,e.lanes=o,e;case W:return e=yt(19,a,t,i),e.elementType=W,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case C:case T:h=10;break e;case A:h=9;break e;case N:h=11;break e;case P:h=14;break e;case F:h=16,l=null;break e}h=29,a=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=yt(h,a,t,i),t.elementType=e,t.type=l,t.lanes=o,t}function Ga(e,t,a,l){return e=yt(7,e,l,t),e.lanes=a,e}function _r(e,t,a){return e=yt(6,e,null,t),e.lanes=a,e}function wr(e,t,a){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Sl=[],Nl=0,Cs=null,As=0,wt=[],Ct=0,Xa=null,Jt=1,$t="";function Va(e,t){Sl[Nl++]=As,Sl[Nl++]=Cs,Cs=e,As=t}function Xu(e,t,a){wt[Ct++]=Jt,wt[Ct++]=$t,wt[Ct++]=Xa,Xa=e;var l=Jt;e=$t;var i=32-mt(l)-1;l&=~(1<<i),a+=1;var o=32-mt(t)+i;if(30<o){var h=i-i%5;o=(l&(1<<h)-1).toString(32),l>>=h,i-=h,Jt=1<<32-mt(t)+i|a<<i|l,$t=o+e}else Jt=1<<o|a<<i|l,$t=e}function Cr(e){e.return!==null&&(Va(e,1),Xu(e,1,0))}function Ar(e){for(;e===Cs;)Cs=Sl[--Nl],Sl[Nl]=null,As=Sl[--Nl],Sl[Nl]=null;for(;e===Xa;)Xa=wt[--Ct],wt[Ct]=null,$t=wt[--Ct],wt[Ct]=null,Jt=wt[--Ct],wt[Ct]=null}var lt=null,ke=null,Te=!1,Qa=null,Ht=!1,Rr=Error(c(519));function Za(e){var t=Error(c(418,""));throw hn(Tt(t,e)),Rr}function Vu(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Ie]=e,t[st]=l,a){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(a=0;a<Ln.length;a++)Se(Ln[a],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),su(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ps(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),ru(t,l.value,l.defaultValue,l.children),ps(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||oh(t.textContent,a)?(l.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),l.onScroll!=null&&Se("scroll",t),l.onScrollEnd!=null&&Se("scrollend",t),l.onClick!=null&&(t.onclick=ci),t=!0):t=!1,t||Za(e)}function Qu(e){for(lt=e.return;lt;)switch(lt.tag){case 5:case 13:Ht=!1;return;case 27:case 3:Ht=!0;return;default:lt=lt.return}}function dn(e){if(e!==lt)return!1;if(!Te)return Qu(e),Te=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Fo(e.type,e.memoizedProps)),a=!a),a&&ke&&Za(e),Qu(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){ke=zt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}ke=null}}else t===27?(t=ke,Ra(e.type)?(e=Wo,Wo=null,ke=e):ke=t):ke=lt?zt(e.stateNode.nextSibling):null;return!0}function fn(){ke=lt=null,Te=!1}function Zu(){var e=Qa;return e!==null&&(ct===null?ct=e:ct.push.apply(ct,e),Qa=null),e}function hn(e){Qa===null?Qa=[e]:Qa.push(e)}var Or=V(null),Fa=null,Wt=null;function pa(e,t,a){ae(Or,t._currentValue),t._currentValue=a}function Pt(e){e._currentValue=Or.current,le(Or)}function Dr(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Mr(e,t,a,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){var h=i.child;o=o.firstContext;e:for(;o!==null;){var y=o;o=i;for(var j=0;j<t.length;j++)if(y.context===t[j]){o.lanes|=a,y=o.alternate,y!==null&&(y.lanes|=a),Dr(o.return,a,e),l||(h=null);break e}o=y.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(c(341));h.lanes|=a,o=h.alternate,o!==null&&(o.lanes|=a),Dr(h,a,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function mn(e,t,a,l){e=null;for(var i=t,o=!1;i!==null;){if(!o){if((i.flags&524288)!==0)o=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(c(387));if(h=h.memoizedProps,h!==null){var y=i.type;pt(i.pendingProps.value,h.value)||(e!==null?e.push(y):e=[y])}}else if(i===ft.current){if(h=i.alternate,h===null)throw Error(c(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Xn):e=[Xn])}i=i.return}e!==null&&Mr(t,e,a,l),t.flags|=262144}function Rs(e){for(e=e.firstContext;e!==null;){if(!pt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ka(e){Fa=e,Wt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function et(e){return Fu(Fa,e)}function Os(e,t){return Fa===null&&Ka(e),Fu(e,t)}function Fu(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Wt===null){if(e===null)throw Error(c(308));Wt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Wt=Wt.next=t;return a}var vy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},xy=s.unstable_scheduleCallback,by=s.unstable_NormalPriority,Ve={$$typeof:T,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function zr(){return{controller:new vy,data:new Map,refCount:0}}function pn(e){e.refCount--,e.refCount===0&&xy(by,function(){e.controller.abort()})}var yn=null,Ur=0,El=0,Tl=null;function jy(e,t){if(yn===null){var a=yn=[];Ur=0,El=ko(),Tl={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Ur++,t.then(Ku,Ku),t}function Ku(){if(--Ur===0&&yn!==null){Tl!==null&&(Tl.status="fulfilled");var e=yn;yn=null,El=0,Tl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Sy(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<a.length;i++)(0,a[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),l}var Ju=X.S;X.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&jy(e,t),Ju!==null&&Ju(e,t)};var Ja=V(null);function Br(){var e=Ja.current;return e!==null?e:De.pooledCache}function Ds(e,t){t===null?ae(Ja,Ja.current):ae(Ja,t.pool)}function $u(){var e=Br();return e===null?null:{parent:Ve._currentValue,pool:e}}var gn=Error(c(460)),Wu=Error(c(474)),Ms=Error(c(542)),Lr={then:function(){}};function Pu(e){return e=e.status,e==="fulfilled"||e==="rejected"}function zs(){}function Iu(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(zs,zs),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,td(e),e;default:if(typeof t.status=="string")t.then(zs,zs);else{if(e=De,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,td(e),e}throw vn=t,gn}}var vn=null;function ed(){if(vn===null)throw Error(c(459));var e=vn;return vn=null,e}function td(e){if(e===gn||e===Ms)throw Error(c(483))}var ya=!1;function kr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ga(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function va(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(_e&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=_s(e),Yu(e,null,a),t}return Ts(e,l,t,a),_s(e)}function xn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Kc(e,a)}}function qr(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var i=null,o=null;if(a=a.firstBaseUpdate,a!==null){do{var h={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};o===null?i=o=h:o=o.next=h,a=a.next}while(a!==null);o===null?i=o=t:o=o.next=t}else i=o=t;a={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Yr=!1;function bn(){if(Yr){var e=Tl;if(e!==null)throw e}}function jn(e,t,a,l){Yr=!1;var i=e.updateQueue;ya=!1;var o=i.firstBaseUpdate,h=i.lastBaseUpdate,y=i.shared.pending;if(y!==null){i.shared.pending=null;var j=y,L=j.next;j.next=null,h===null?o=L:h.next=L,h=j;var Z=e.alternate;Z!==null&&(Z=Z.updateQueue,y=Z.lastBaseUpdate,y!==h&&(y===null?Z.firstBaseUpdate=L:y.next=L,Z.lastBaseUpdate=j))}if(o!==null){var J=i.baseState;h=0,Z=L=j=null,y=o;do{var k=y.lane&-536870913,H=k!==y.lane;if(H?(Ne&k)===k:(l&k)===k){k!==0&&k===El&&(Yr=!0),Z!==null&&(Z=Z.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var fe=e,ue=y;k=t;var Re=a;switch(ue.tag){case 1:if(fe=ue.payload,typeof fe=="function"){J=fe.call(Re,J,k);break e}J=fe;break e;case 3:fe.flags=fe.flags&-65537|128;case 0:if(fe=ue.payload,k=typeof fe=="function"?fe.call(Re,J,k):fe,k==null)break e;J=x({},J,k);break e;case 2:ya=!0}}k=y.callback,k!==null&&(e.flags|=64,H&&(e.flags|=8192),H=i.callbacks,H===null?i.callbacks=[k]:H.push(k))}else H={lane:k,tag:y.tag,payload:y.payload,callback:y.callback,next:null},Z===null?(L=Z=H,j=J):Z=Z.next=H,h|=k;if(y=y.next,y===null){if(y=i.shared.pending,y===null)break;H=y,y=H.next,H.next=null,i.lastBaseUpdate=H,i.shared.pending=null}}while(!0);Z===null&&(j=J),i.baseState=j,i.firstBaseUpdate=L,i.lastBaseUpdate=Z,o===null&&(i.shared.lanes=0),_a|=h,e.lanes=h,e.memoizedState=J}}function ad(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function ld(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)ad(a[e],t)}var _l=V(null),Us=V(0);function nd(e,t){e=sa,ae(Us,e),ae(_l,t),sa=e|t.baseLanes}function Gr(){ae(Us,sa),ae(_l,_l.current)}function Xr(){sa=Us.current,le(_l),le(Us)}var xa=0,ve=null,Ce=null,Ge=null,Bs=!1,wl=!1,$a=!1,Ls=0,Sn=0,Cl=null,Ny=0;function qe(){throw Error(c(321))}function Vr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!pt(e[a],t[a]))return!1;return!0}function Qr(e,t,a,l,i,o){return xa=o,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,X.H=e===null||e.memoizedState===null?Yd:Gd,$a=!1,o=a(l,i),$a=!1,wl&&(o=id(t,a,l,i)),sd(e),o}function sd(e){X.H=Xs;var t=Ce!==null&&Ce.next!==null;if(xa=0,Ge=Ce=ve=null,Bs=!1,Sn=0,Cl=null,t)throw Error(c(300));e===null||Fe||(e=e.dependencies,e!==null&&Rs(e)&&(Fe=!0))}function id(e,t,a,l){ve=e;var i=0;do{if(wl&&(Cl=null),Sn=0,wl=!1,25<=i)throw Error(c(301));if(i+=1,Ge=Ce=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}X.H=Ry,o=t(a,l)}while(wl);return o}function Ey(){var e=X.H,t=e.useState()[0];return t=typeof t.then=="function"?Nn(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(ve.flags|=1024),t}function Zr(){var e=Ls!==0;return Ls=0,e}function Fr(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Kr(e){if(Bs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Bs=!1}xa=0,Ge=Ce=ve=null,wl=!1,Sn=Ls=0,Cl=null}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ve.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Xe(){if(Ce===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ge===null?ve.memoizedState:Ge.next;if(t!==null)Ge=t,Ce=e;else{if(e===null)throw ve.alternate===null?Error(c(467)):Error(c(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ge===null?ve.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Jr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Nn(e){var t=Sn;return Sn+=1,Cl===null&&(Cl=[]),e=Iu(Cl,e,t),t=ve,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,X.H=t===null||t.memoizedState===null?Yd:Gd),e}function ks(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Nn(e);if(e.$$typeof===T)return et(e)}throw Error(c(438,String(e)))}function $r(e){var t=null,a=ve.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ve.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Jr(),ve.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Q;return t.index++,a}function It(e,t){return typeof t=="function"?t(e):t}function Hs(e){var t=Xe();return Wr(t,Ce,e)}function Wr(e,t,a){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=a;var i=e.baseQueue,o=l.pending;if(o!==null){if(i!==null){var h=i.next;i.next=o.next,o.next=h}t.baseQueue=i=o,l.pending=null}if(o=e.baseState,i===null)e.memoizedState=o;else{t=i.next;var y=h=null,j=null,L=t,Z=!1;do{var J=L.lane&-536870913;if(J!==L.lane?(Ne&J)===J:(xa&J)===J){var k=L.revertLane;if(k===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null}),J===El&&(Z=!0);else if((xa&k)===k){L=L.next,k===El&&(Z=!0);continue}else J={lane:0,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},j===null?(y=j=J,h=o):j=j.next=J,ve.lanes|=k,_a|=k;J=L.action,$a&&a(o,J),o=L.hasEagerState?L.eagerState:a(o,J)}else k={lane:J,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},j===null?(y=j=k,h=o):j=j.next=k,ve.lanes|=J,_a|=J;L=L.next}while(L!==null&&L!==t);if(j===null?h=o:j.next=y,!pt(o,e.memoizedState)&&(Fe=!0,Z&&(a=Tl,a!==null)))throw a;e.memoizedState=o,e.baseState=h,e.baseQueue=j,l.lastRenderedState=o}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Pr(e){var t=Xe(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var l=a.dispatch,i=a.pending,o=t.memoizedState;if(i!==null){a.pending=null;var h=i=i.next;do o=e(o,h.action),h=h.next;while(h!==i);pt(o,t.memoizedState)||(Fe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),a.lastRenderedState=o}return[o,l]}function rd(e,t,a){var l=ve,i=Xe(),o=Te;if(o){if(a===void 0)throw Error(c(407));a=a()}else a=t();var h=!pt((Ce||i).memoizedState,a);h&&(i.memoizedState=a,Fe=!0),i=i.queue;var y=ud.bind(null,l,i,e);if(En(2048,8,y,[e]),i.getSnapshot!==t||h||Ge!==null&&Ge.memoizedState.tag&1){if(l.flags|=2048,Al(9,qs(),cd.bind(null,l,i,a,t),null),De===null)throw Error(c(349));o||(xa&124)!==0||od(l,t,a)}return a}function od(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ve.updateQueue,t===null?(t=Jr(),ve.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function cd(e,t,a,l){t.value=a,t.getSnapshot=l,dd(t)&&fd(e)}function ud(e,t,a){return a(function(){dd(t)&&fd(e)})}function dd(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!pt(e,a)}catch{return!0}}function fd(e){var t=bl(e,2);t!==null&&jt(t,e,2)}function Ir(e){var t=rt();if(typeof e=="function"){var a=e;if(e=a(),$a){fa(!0);try{a()}finally{fa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:e},t}function hd(e,t,a,l){return e.baseState=a,Wr(e,Ce,typeof l=="function"?l:It)}function Ty(e,t,a,l,i){if(Gs(e))throw Error(c(485));if(e=t.action,e!==null){var o={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};X.T!==null?a(!0):o.isTransition=!1,l(o),a=t.pending,a===null?(o.next=t.pending=o,md(t,o)):(o.next=a.next,t.pending=a.next=o)}}function md(e,t){var a=t.action,l=t.payload,i=e.state;if(t.isTransition){var o=X.T,h={};X.T=h;try{var y=a(i,l),j=X.S;j!==null&&j(h,y),pd(e,t,y)}catch(L){eo(e,t,L)}finally{X.T=o}}else try{o=a(i,l),pd(e,t,o)}catch(L){eo(e,t,L)}}function pd(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){yd(e,t,l)},function(l){return eo(e,t,l)}):yd(e,t,a)}function yd(e,t,a){t.status="fulfilled",t.value=a,gd(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,md(e,a)))}function eo(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,gd(t),t=t.next;while(t!==l)}e.action=null}function gd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function vd(e,t){return t}function xd(e,t){if(Te){var a=De.formState;if(a!==null){e:{var l=ve;if(Te){if(ke){t:{for(var i=ke,o=Ht;i.nodeType!==8;){if(!o){i=null;break t}if(i=zt(i.nextSibling),i===null){i=null;break t}}o=i.data,i=o==="F!"||o==="F"?i:null}if(i){ke=zt(i.nextSibling),l=i.data==="F!";break e}}Za(l)}l=!1}l&&(t=a[0])}}return a=rt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:vd,lastRenderedState:t},a.queue=l,a=kd.bind(null,ve,l),l.dispatch=a,l=Ir(!1),o=so.bind(null,ve,!1,l.queue),l=rt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,a=Ty.bind(null,ve,i,o,a),i.dispatch=a,l.memoizedState=e,[t,a,!1]}function bd(e){var t=Xe();return jd(t,Ce,e)}function jd(e,t,a){if(t=Wr(e,t,vd)[0],e=Hs(It)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Nn(t)}catch(h){throw h===gn?Ms:h}else l=t;t=Xe();var i=t.queue,o=i.dispatch;return a!==t.memoizedState&&(ve.flags|=2048,Al(9,qs(),_y.bind(null,i,a),null)),[l,o,e]}function _y(e,t){e.action=t}function Sd(e){var t=Xe(),a=Ce;if(a!==null)return jd(t,a,e);Xe(),t=t.memoizedState,a=Xe();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Al(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ve.updateQueue,t===null&&(t=Jr(),ve.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function qs(){return{destroy:void 0,resource:void 0}}function Nd(){return Xe().memoizedState}function Ys(e,t,a,l){var i=rt();l=l===void 0?null:l,ve.flags|=e,i.memoizedState=Al(1|t,qs(),a,l)}function En(e,t,a,l){var i=Xe();l=l===void 0?null:l;var o=i.memoizedState.inst;Ce!==null&&l!==null&&Vr(l,Ce.memoizedState.deps)?i.memoizedState=Al(t,o,a,l):(ve.flags|=e,i.memoizedState=Al(1|t,o,a,l))}function Ed(e,t){Ys(8390656,8,e,t)}function Td(e,t){En(2048,8,e,t)}function _d(e,t){return En(4,2,e,t)}function wd(e,t){return En(4,4,e,t)}function Cd(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ad(e,t,a){a=a!=null?a.concat([e]):null,En(4,4,Cd.bind(null,t,e),a)}function to(){}function Rd(e,t){var a=Xe();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Vr(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Od(e,t){var a=Xe();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Vr(t,l[1]))return l[0];if(l=e(),$a){fa(!0);try{e()}finally{fa(!1)}}return a.memoizedState=[l,t],l}function ao(e,t,a){return a===void 0||(xa&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Uf(),ve.lanes|=e,_a|=e,a)}function Dd(e,t,a,l){return pt(a,t)?a:_l.current!==null?(e=ao(e,a,l),pt(e,t)||(Fe=!0),e):(xa&42)===0?(Fe=!0,e.memoizedState=a):(e=Uf(),ve.lanes|=e,_a|=e,t)}function Md(e,t,a,l,i){var o=te.p;te.p=o!==0&&8>o?o:8;var h=X.T,y={};X.T=y,so(e,!1,t,a);try{var j=i(),L=X.S;if(L!==null&&L(y,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var Z=Sy(j,l);Tn(e,t,Z,bt(e))}else Tn(e,t,l,bt(e))}catch(J){Tn(e,t,{then:function(){},status:"rejected",reason:J},bt())}finally{te.p=o,X.T=h}}function wy(){}function lo(e,t,a,l){if(e.tag!==5)throw Error(c(476));var i=zd(e).queue;Md(e,i,t,q,a===null?wy:function(){return Ud(e),a(l)})}function zd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:q,baseState:q,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:q},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Ud(e){var t=zd(e).next.queue;Tn(e,t,{},bt())}function no(){return et(Xn)}function Bd(){return Xe().memoizedState}function Ld(){return Xe().memoizedState}function Cy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=bt();e=ga(a);var l=va(t,e,a);l!==null&&(jt(l,t,a),xn(l,t,a)),t={cache:zr()},e.payload=t;return}t=t.return}}function Ay(e,t,a){var l=bt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Gs(e)?Hd(t,a):(a=Er(e,t,a,l),a!==null&&(jt(a,e,l),qd(a,t,l)))}function kd(e,t,a){var l=bt();Tn(e,t,a,l)}function Tn(e,t,a,l){var i={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Gs(e))Hd(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var h=t.lastRenderedState,y=o(h,a);if(i.hasEagerState=!0,i.eagerState=y,pt(y,h))return Ts(e,t,i,0),De===null&&Es(),!1}catch{}finally{}if(a=Er(e,t,i,l),a!==null)return jt(a,e,l),qd(a,t,l),!0}return!1}function so(e,t,a,l){if(l={lane:2,revertLane:ko(),action:l,hasEagerState:!1,eagerState:null,next:null},Gs(e)){if(t)throw Error(c(479))}else t=Er(e,a,l,2),t!==null&&jt(t,e,2)}function Gs(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function Hd(e,t){wl=Bs=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function qd(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Kc(e,a)}}var Xs={readContext:et,use:ks,useCallback:qe,useContext:qe,useEffect:qe,useImperativeHandle:qe,useLayoutEffect:qe,useInsertionEffect:qe,useMemo:qe,useReducer:qe,useRef:qe,useState:qe,useDebugValue:qe,useDeferredValue:qe,useTransition:qe,useSyncExternalStore:qe,useId:qe,useHostTransitionStatus:qe,useFormState:qe,useActionState:qe,useOptimistic:qe,useMemoCache:qe,useCacheRefresh:qe},Yd={readContext:et,use:ks,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:Ed,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ys(4194308,4,Cd.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ys(4194308,4,e,t)},useInsertionEffect:function(e,t){Ys(4,2,e,t)},useMemo:function(e,t){var a=rt();t=t===void 0?null:t;var l=e();if($a){fa(!0);try{e()}finally{fa(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=rt();if(a!==void 0){var i=a(t);if($a){fa(!0);try{a(t)}finally{fa(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=Ay.bind(null,ve,e),[l.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:function(e){e=Ir(e);var t=e.queue,a=kd.bind(null,ve,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:to,useDeferredValue:function(e,t){var a=rt();return ao(a,e,t)},useTransition:function(){var e=Ir(!1);return e=Md.bind(null,ve,e.queue,!0,!1),rt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ve,i=rt();if(Te){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),De===null)throw Error(c(349));(Ne&124)!==0||od(l,t,a)}i.memoizedState=a;var o={value:a,getSnapshot:t};return i.queue=o,Ed(ud.bind(null,l,o,e),[e]),l.flags|=2048,Al(9,qs(),cd.bind(null,l,o,a,t),null),a},useId:function(){var e=rt(),t=De.identifierPrefix;if(Te){var a=$t,l=Jt;a=(l&~(1<<32-mt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ls++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Ny++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:no,useFormState:xd,useActionState:xd,useOptimistic:function(e){var t=rt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=so.bind(null,ve,!0,a),a.dispatch=t,[e,t]},useMemoCache:$r,useCacheRefresh:function(){return rt().memoizedState=Cy.bind(null,ve)}},Gd={readContext:et,use:ks,useCallback:Rd,useContext:et,useEffect:Td,useImperativeHandle:Ad,useInsertionEffect:_d,useLayoutEffect:wd,useMemo:Od,useReducer:Hs,useRef:Nd,useState:function(){return Hs(It)},useDebugValue:to,useDeferredValue:function(e,t){var a=Xe();return Dd(a,Ce.memoizedState,e,t)},useTransition:function(){var e=Hs(It)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:rd,useId:Bd,useHostTransitionStatus:no,useFormState:bd,useActionState:bd,useOptimistic:function(e,t){var a=Xe();return hd(a,Ce,e,t)},useMemoCache:$r,useCacheRefresh:Ld},Ry={readContext:et,use:ks,useCallback:Rd,useContext:et,useEffect:Td,useImperativeHandle:Ad,useInsertionEffect:_d,useLayoutEffect:wd,useMemo:Od,useReducer:Pr,useRef:Nd,useState:function(){return Pr(It)},useDebugValue:to,useDeferredValue:function(e,t){var a=Xe();return Ce===null?ao(a,e,t):Dd(a,Ce.memoizedState,e,t)},useTransition:function(){var e=Pr(It)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:rd,useId:Bd,useHostTransitionStatus:no,useFormState:Sd,useActionState:Sd,useOptimistic:function(e,t){var a=Xe();return Ce!==null?hd(a,Ce,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:$r,useCacheRefresh:Ld},Rl=null,_n=0;function Vs(e){var t=_n;return _n+=1,Rl===null&&(Rl=[]),Iu(Rl,e,t)}function wn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Qs(e,t){throw t.$$typeof===b?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Xd(e){var t=e._init;return t(e._payload)}function Vd(e){function t(M,O){if(e){var B=M.deletions;B===null?(M.deletions=[O],M.flags|=16):B.push(O)}}function a(M,O){if(!e)return null;for(;O!==null;)t(M,O),O=O.sibling;return null}function l(M){for(var O=new Map;M!==null;)M.key!==null?O.set(M.key,M):O.set(M.index,M),M=M.sibling;return O}function i(M,O){return M=Kt(M,O),M.index=0,M.sibling=null,M}function o(M,O,B){return M.index=B,e?(B=M.alternate,B!==null?(B=B.index,B<O?(M.flags|=67108866,O):B):(M.flags|=67108866,O)):(M.flags|=1048576,O)}function h(M){return e&&M.alternate===null&&(M.flags|=67108866),M}function y(M,O,B,K){return O===null||O.tag!==6?(O=_r(B,M.mode,K),O.return=M,O):(O=i(O,B),O.return=M,O)}function j(M,O,B,K){var ie=B.type;return ie===w?Z(M,O,B.props.children,K,B.key):O!==null&&(O.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===F&&Xd(ie)===O.type)?(O=i(O,B.props),wn(O,B),O.return=M,O):(O=ws(B.type,B.key,B.props,null,M.mode,K),wn(O,B),O.return=M,O)}function L(M,O,B,K){return O===null||O.tag!==4||O.stateNode.containerInfo!==B.containerInfo||O.stateNode.implementation!==B.implementation?(O=wr(B,M.mode,K),O.return=M,O):(O=i(O,B.children||[]),O.return=M,O)}function Z(M,O,B,K,ie){return O===null||O.tag!==7?(O=Ga(B,M.mode,K,ie),O.return=M,O):(O=i(O,B),O.return=M,O)}function J(M,O,B){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return O=_r(""+O,M.mode,B),O.return=M,O;if(typeof O=="object"&&O!==null){switch(O.$$typeof){case _:return B=ws(O.type,O.key,O.props,null,M.mode,B),wn(B,O),B.return=M,B;case U:return O=wr(O,M.mode,B),O.return=M,O;case F:var K=O._init;return O=K(O._payload),J(M,O,B)}if(ye(O)||ne(O))return O=Ga(O,M.mode,B,null),O.return=M,O;if(typeof O.then=="function")return J(M,Vs(O),B);if(O.$$typeof===T)return J(M,Os(M,O),B);Qs(M,O)}return null}function k(M,O,B,K){var ie=O!==null?O.key:null;if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return ie!==null?null:y(M,O,""+B,K);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case _:return B.key===ie?j(M,O,B,K):null;case U:return B.key===ie?L(M,O,B,K):null;case F:return ie=B._init,B=ie(B._payload),k(M,O,B,K)}if(ye(B)||ne(B))return ie!==null?null:Z(M,O,B,K,null);if(typeof B.then=="function")return k(M,O,Vs(B),K);if(B.$$typeof===T)return k(M,O,Os(M,B),K);Qs(M,B)}return null}function H(M,O,B,K,ie){if(typeof K=="string"&&K!==""||typeof K=="number"||typeof K=="bigint")return M=M.get(B)||null,y(O,M,""+K,ie);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case _:return M=M.get(K.key===null?B:K.key)||null,j(O,M,K,ie);case U:return M=M.get(K.key===null?B:K.key)||null,L(O,M,K,ie);case F:var be=K._init;return K=be(K._payload),H(M,O,B,K,ie)}if(ye(K)||ne(K))return M=M.get(B)||null,Z(O,M,K,ie,null);if(typeof K.then=="function")return H(M,O,B,Vs(K),ie);if(K.$$typeof===T)return H(M,O,B,Os(O,K),ie);Qs(O,K)}return null}function fe(M,O,B,K){for(var ie=null,be=null,oe=O,de=O=0,Je=null;oe!==null&&de<B.length;de++){oe.index>de?(Je=oe,oe=null):Je=oe.sibling;var Ee=k(M,oe,B[de],K);if(Ee===null){oe===null&&(oe=Je);break}e&&oe&&Ee.alternate===null&&t(M,oe),O=o(Ee,O,de),be===null?ie=Ee:be.sibling=Ee,be=Ee,oe=Je}if(de===B.length)return a(M,oe),Te&&Va(M,de),ie;if(oe===null){for(;de<B.length;de++)oe=J(M,B[de],K),oe!==null&&(O=o(oe,O,de),be===null?ie=oe:be.sibling=oe,be=oe);return Te&&Va(M,de),ie}for(oe=l(oe);de<B.length;de++)Je=H(oe,M,de,B[de],K),Je!==null&&(e&&Je.alternate!==null&&oe.delete(Je.key===null?de:Je.key),O=o(Je,O,de),be===null?ie=Je:be.sibling=Je,be=Je);return e&&oe.forEach(function(Ua){return t(M,Ua)}),Te&&Va(M,de),ie}function ue(M,O,B,K){if(B==null)throw Error(c(151));for(var ie=null,be=null,oe=O,de=O=0,Je=null,Ee=B.next();oe!==null&&!Ee.done;de++,Ee=B.next()){oe.index>de?(Je=oe,oe=null):Je=oe.sibling;var Ua=k(M,oe,Ee.value,K);if(Ua===null){oe===null&&(oe=Je);break}e&&oe&&Ua.alternate===null&&t(M,oe),O=o(Ua,O,de),be===null?ie=Ua:be.sibling=Ua,be=Ua,oe=Je}if(Ee.done)return a(M,oe),Te&&Va(M,de),ie;if(oe===null){for(;!Ee.done;de++,Ee=B.next())Ee=J(M,Ee.value,K),Ee!==null&&(O=o(Ee,O,de),be===null?ie=Ee:be.sibling=Ee,be=Ee);return Te&&Va(M,de),ie}for(oe=l(oe);!Ee.done;de++,Ee=B.next())Ee=H(oe,M,de,Ee.value,K),Ee!==null&&(e&&Ee.alternate!==null&&oe.delete(Ee.key===null?de:Ee.key),O=o(Ee,O,de),be===null?ie=Ee:be.sibling=Ee,be=Ee);return e&&oe.forEach(function(O0){return t(M,O0)}),Te&&Va(M,de),ie}function Re(M,O,B,K){if(typeof B=="object"&&B!==null&&B.type===w&&B.key===null&&(B=B.props.children),typeof B=="object"&&B!==null){switch(B.$$typeof){case _:e:{for(var ie=B.key;O!==null;){if(O.key===ie){if(ie=B.type,ie===w){if(O.tag===7){a(M,O.sibling),K=i(O,B.props.children),K.return=M,M=K;break e}}else if(O.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===F&&Xd(ie)===O.type){a(M,O.sibling),K=i(O,B.props),wn(K,B),K.return=M,M=K;break e}a(M,O);break}else t(M,O);O=O.sibling}B.type===w?(K=Ga(B.props.children,M.mode,K,B.key),K.return=M,M=K):(K=ws(B.type,B.key,B.props,null,M.mode,K),wn(K,B),K.return=M,M=K)}return h(M);case U:e:{for(ie=B.key;O!==null;){if(O.key===ie)if(O.tag===4&&O.stateNode.containerInfo===B.containerInfo&&O.stateNode.implementation===B.implementation){a(M,O.sibling),K=i(O,B.children||[]),K.return=M,M=K;break e}else{a(M,O);break}else t(M,O);O=O.sibling}K=wr(B,M.mode,K),K.return=M,M=K}return h(M);case F:return ie=B._init,B=ie(B._payload),Re(M,O,B,K)}if(ye(B))return fe(M,O,B,K);if(ne(B)){if(ie=ne(B),typeof ie!="function")throw Error(c(150));return B=ie.call(B),ue(M,O,B,K)}if(typeof B.then=="function")return Re(M,O,Vs(B),K);if(B.$$typeof===T)return Re(M,O,Os(M,B),K);Qs(M,B)}return typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint"?(B=""+B,O!==null&&O.tag===6?(a(M,O.sibling),K=i(O,B),K.return=M,M=K):(a(M,O),K=_r(B,M.mode,K),K.return=M,M=K),h(M)):a(M,O)}return function(M,O,B,K){try{_n=0;var ie=Re(M,O,B,K);return Rl=null,ie}catch(oe){if(oe===gn||oe===Ms)throw oe;var be=yt(29,oe,null,M.mode);return be.lanes=K,be.return=M,be}finally{}}}var Ol=Vd(!0),Qd=Vd(!1),At=V(null),qt=null;function ba(e){var t=e.alternate;ae(Qe,Qe.current&1),ae(At,e),qt===null&&(t===null||_l.current!==null||t.memoizedState!==null)&&(qt=e)}function Zd(e){if(e.tag===22){if(ae(Qe,Qe.current),ae(At,e),qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(qt=e)}}else ja()}function ja(){ae(Qe,Qe.current),ae(At,At.current)}function ea(e){le(At),qt===e&&(qt=null),le(Qe)}var Qe=V(0);function Zs(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||$o(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function io(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var ro={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=bt(),i=ga(l);i.payload=t,a!=null&&(i.callback=a),t=va(e,i,l),t!==null&&(jt(t,e,l),xn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=bt(),i=ga(l);i.tag=1,i.payload=t,a!=null&&(i.callback=a),t=va(e,i,l),t!==null&&(jt(t,e,l),xn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=bt(),l=ga(a);l.tag=2,t!=null&&(l.callback=t),t=va(e,l,a),t!==null&&(jt(t,e,a),xn(t,e,a))}};function Fd(e,t,a,l,i,o,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,o,h):t.prototype&&t.prototype.isPureReactComponent?!cn(a,l)||!cn(i,o):!0}function Kd(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&ro.enqueueReplaceState(t,t.state,null)}function Wa(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var i in e)a[i]===void 0&&(a[i]=e[i])}return a}var Fs=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Jd(e){Fs(e)}function $d(e){console.error(e)}function Wd(e){Fs(e)}function Ks(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Pd(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function oo(e,t,a){return a=ga(a),a.tag=3,a.payload={element:null},a.callback=function(){Ks(e,t)},a}function Id(e){return e=ga(e),e.tag=3,e}function ef(e,t,a,l){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var o=l.value;e.payload=function(){return i(o)},e.callback=function(){Pd(t,a,l)}}var h=a.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Pd(t,a,l),typeof i!="function"&&(wa===null?wa=new Set([this]):wa.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function Oy(e,t,a,l,i){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&mn(t,a,i,!0),a=At.current,a!==null){switch(a.tag){case 13:return qt===null?Mo():a.alternate===null&&He===0&&(He=3),a.flags&=-257,a.flags|=65536,a.lanes=i,l===Lr?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Uo(e,l,i)),!1;case 22:return a.flags|=65536,l===Lr?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Uo(e,l,i)),!1}throw Error(c(435,a.tag))}return Uo(e,l,i),Mo(),!1}if(Te)return t=At.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==Rr&&(e=Error(c(422),{cause:l}),hn(Tt(e,a)))):(l!==Rr&&(t=Error(c(423),{cause:l}),hn(Tt(t,a))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Tt(l,a),i=oo(e.stateNode,l,i),qr(e,i),He!==4&&(He=2)),!1;var o=Error(c(520),{cause:l});if(o=Tt(o,a),zn===null?zn=[o]:zn.push(o),He!==4&&(He=2),t===null)return!0;l=Tt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=i&-i,a.lanes|=e,e=oo(a.stateNode,l,e),qr(a,e),!1;case 1:if(t=a.type,o=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(wa===null||!wa.has(o))))return a.flags|=65536,i&=-i,a.lanes|=i,i=Id(i),ef(i,e,a,l),qr(a,i),!1}a=a.return}while(a!==null);return!1}var tf=Error(c(461)),Fe=!1;function $e(e,t,a,l){t.child=e===null?Qd(t,null,a,l):Ol(t,e.child,a,l)}function af(e,t,a,l,i){a=a.render;var o=t.ref;if("ref"in l){var h={};for(var y in l)y!=="ref"&&(h[y]=l[y])}else h=l;return Ka(t),l=Qr(e,t,a,h,o,i),y=Zr(),e!==null&&!Fe?(Fr(e,t,i),ta(e,t,i)):(Te&&y&&Cr(t),t.flags|=1,$e(e,t,l,i),t.child)}function lf(e,t,a,l,i){if(e===null){var o=a.type;return typeof o=="function"&&!Tr(o)&&o.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=o,nf(e,t,o,l,i)):(e=ws(a.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!go(e,i)){var h=o.memoizedProps;if(a=a.compare,a=a!==null?a:cn,a(h,l)&&e.ref===t.ref)return ta(e,t,i)}return t.flags|=1,e=Kt(o,l),e.ref=t.ref,e.return=t,t.child=e}function nf(e,t,a,l,i){if(e!==null){var o=e.memoizedProps;if(cn(o,l)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=l=o,go(e,i))(e.flags&131072)!==0&&(Fe=!0);else return t.lanes=e.lanes,ta(e,t,i)}return co(e,t,a,l,i)}function sf(e,t,a){var l=t.pendingProps,i=l.children,o=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=o!==null?o.baseLanes|a:a,e!==null){for(i=t.child=e.child,o=0;i!==null;)o=o|i.lanes|i.childLanes,i=i.sibling;t.childLanes=o&~l}else t.childLanes=0,t.child=null;return rf(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ds(t,o!==null?o.cachePool:null),o!==null?nd(t,o):Gr(),Zd(t);else return t.lanes=t.childLanes=536870912,rf(e,t,o!==null?o.baseLanes|a:a,a)}else o!==null?(Ds(t,o.cachePool),nd(t,o),ja(),t.memoizedState=null):(e!==null&&Ds(t,null),Gr(),ja());return $e(e,t,i,a),t.child}function rf(e,t,a,l){var i=Br();return i=i===null?null:{parent:Ve._currentValue,pool:i},t.memoizedState={baseLanes:a,cachePool:i},e!==null&&Ds(t,null),Gr(),Zd(t),e!==null&&mn(e,t,l,!0),null}function Js(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function co(e,t,a,l,i){return Ka(t),a=Qr(e,t,a,l,void 0,i),l=Zr(),e!==null&&!Fe?(Fr(e,t,i),ta(e,t,i)):(Te&&l&&Cr(t),t.flags|=1,$e(e,t,a,i),t.child)}function of(e,t,a,l,i,o){return Ka(t),t.updateQueue=null,a=id(t,l,a,i),sd(e),l=Zr(),e!==null&&!Fe?(Fr(e,t,o),ta(e,t,o)):(Te&&l&&Cr(t),t.flags|=1,$e(e,t,a,o),t.child)}function cf(e,t,a,l,i){if(Ka(t),t.stateNode===null){var o=jl,h=a.contextType;typeof h=="object"&&h!==null&&(o=et(h)),o=new a(l,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=ro,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=l,o.state=t.memoizedState,o.refs={},kr(t),h=a.contextType,o.context=typeof h=="object"&&h!==null?et(h):jl,o.state=t.memoizedState,h=a.getDerivedStateFromProps,typeof h=="function"&&(io(t,a,h,l),o.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&ro.enqueueReplaceState(o,o.state,null),jn(t,l,o,i),bn(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){o=t.stateNode;var y=t.memoizedProps,j=Wa(a,y);o.props=j;var L=o.context,Z=a.contextType;h=jl,typeof Z=="object"&&Z!==null&&(h=et(Z));var J=a.getDerivedStateFromProps;Z=typeof J=="function"||typeof o.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,Z||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(y||L!==h)&&Kd(t,o,l,h),ya=!1;var k=t.memoizedState;o.state=k,jn(t,l,o,i),bn(),L=t.memoizedState,y||k!==L||ya?(typeof J=="function"&&(io(t,a,J,l),L=t.memoizedState),(j=ya||Fd(t,a,j,l,k,L,h))?(Z||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=L),o.props=l,o.state=L,o.context=h,l=j):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{o=t.stateNode,Hr(e,t),h=t.memoizedProps,Z=Wa(a,h),o.props=Z,J=t.pendingProps,k=o.context,L=a.contextType,j=jl,typeof L=="object"&&L!==null&&(j=et(L)),y=a.getDerivedStateFromProps,(L=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==J||k!==j)&&Kd(t,o,l,j),ya=!1,k=t.memoizedState,o.state=k,jn(t,l,o,i),bn();var H=t.memoizedState;h!==J||k!==H||ya||e!==null&&e.dependencies!==null&&Rs(e.dependencies)?(typeof y=="function"&&(io(t,a,y,l),H=t.memoizedState),(Z=ya||Fd(t,a,Z,l,k,H,j)||e!==null&&e.dependencies!==null&&Rs(e.dependencies))?(L||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(l,H,j),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(l,H,j)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=H),o.props=l,o.state=H,o.context=j,l=Z):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),l=!1)}return o=l,Js(e,t),l=(t.flags&128)!==0,o||l?(o=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&l?(t.child=Ol(t,e.child,null,i),t.child=Ol(t,null,a,i)):$e(e,t,a,i),t.memoizedState=o.state,e=t.child):e=ta(e,t,i),e}function uf(e,t,a,l){return fn(),t.flags|=256,$e(e,t,a,l),t.child}var uo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function fo(e){return{baseLanes:e,cachePool:$u()}}function ho(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Rt),e}function df(e,t,a){var l=t.pendingProps,i=!1,o=(t.flags&128)!==0,h;if((h=o)||(h=e!==null&&e.memoizedState===null?!1:(Qe.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Te){if(i?ba(t):ja(),Te){var y=ke,j;if(j=y){e:{for(j=y,y=Ht;j.nodeType!==8;){if(!y){y=null;break e}if(j=zt(j.nextSibling),j===null){y=null;break e}}y=j}y!==null?(t.memoizedState={dehydrated:y,treeContext:Xa!==null?{id:Jt,overflow:$t}:null,retryLane:536870912,hydrationErrors:null},j=yt(18,null,null,0),j.stateNode=y,j.return=t,t.child=j,lt=t,ke=null,j=!0):j=!1}j||Za(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return $o(y)?t.lanes=32:t.lanes=536870912,null;ea(t)}return y=l.children,l=l.fallback,i?(ja(),i=t.mode,y=$s({mode:"hidden",children:y},i),l=Ga(l,i,a,null),y.return=t,l.return=t,y.sibling=l,t.child=y,i=t.child,i.memoizedState=fo(a),i.childLanes=ho(e,h,a),t.memoizedState=uo,l):(ba(t),mo(t,y))}if(j=e.memoizedState,j!==null&&(y=j.dehydrated,y!==null)){if(o)t.flags&256?(ba(t),t.flags&=-257,t=po(e,t,a)):t.memoizedState!==null?(ja(),t.child=e.child,t.flags|=128,t=null):(ja(),i=l.fallback,y=t.mode,l=$s({mode:"visible",children:l.children},y),i=Ga(i,y,a,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,Ol(t,e.child,null,a),l=t.child,l.memoizedState=fo(a),l.childLanes=ho(e,h,a),t.memoizedState=uo,t=i);else if(ba(t),$o(y)){if(h=y.nextSibling&&y.nextSibling.dataset,h)var L=h.dgst;h=L,l=Error(c(419)),l.stack="",l.digest=h,hn({value:l,source:null,stack:null}),t=po(e,t,a)}else if(Fe||mn(e,t,a,!1),h=(a&e.childLanes)!==0,Fe||h){if(h=De,h!==null&&(l=a&-a,l=(l&42)!==0?1:$i(l),l=(l&(h.suspendedLanes|a))!==0?0:l,l!==0&&l!==j.retryLane))throw j.retryLane=l,bl(e,l),jt(h,e,l),tf;y.data==="$?"||Mo(),t=po(e,t,a)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,ke=zt(y.nextSibling),lt=t,Te=!0,Qa=null,Ht=!1,e!==null&&(wt[Ct++]=Jt,wt[Ct++]=$t,wt[Ct++]=Xa,Jt=e.id,$t=e.overflow,Xa=t),t=mo(t,l.children),t.flags|=4096);return t}return i?(ja(),i=l.fallback,y=t.mode,j=e.child,L=j.sibling,l=Kt(j,{mode:"hidden",children:l.children}),l.subtreeFlags=j.subtreeFlags&65011712,L!==null?i=Kt(L,i):(i=Ga(i,y,a,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,y=e.child.memoizedState,y===null?y=fo(a):(j=y.cachePool,j!==null?(L=Ve._currentValue,j=j.parent!==L?{parent:L,pool:L}:j):j=$u(),y={baseLanes:y.baseLanes|a,cachePool:j}),i.memoizedState=y,i.childLanes=ho(e,h,a),t.memoizedState=uo,l):(ba(t),a=e.child,e=a.sibling,a=Kt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=a,t.memoizedState=null,a)}function mo(e,t){return t=$s({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function $s(e,t){return e=yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function po(e,t,a){return Ol(t,e.child,null,a),e=mo(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ff(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Dr(e.return,t,a)}function yo(e,t,a,l,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=l,o.tail=a,o.tailMode=i)}function hf(e,t,a){var l=t.pendingProps,i=l.revealOrder,o=l.tail;if($e(e,t,l.children,a),l=Qe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ff(e,a,t);else if(e.tag===19)ff(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(ae(Qe,l),i){case"forwards":for(a=t.child,i=null;a!==null;)e=a.alternate,e!==null&&Zs(e)===null&&(i=a),a=a.sibling;a=i,a===null?(i=t.child,t.child=null):(i=a.sibling,a.sibling=null),yo(t,!1,i,a,o);break;case"backwards":for(a=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Zs(e)===null){t.child=i;break}e=i.sibling,i.sibling=a,a=i,i=e}yo(t,!0,a,null,o);break;case"together":yo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ta(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),_a|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(mn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=Kt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Kt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function go(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Rs(e)))}function Dy(e,t,a){switch(t.tag){case 3:ze(t,t.stateNode.containerInfo),pa(t,Ve,e.memoizedState.cache),fn();break;case 27:case 5:Qi(t);break;case 4:ze(t,t.stateNode.containerInfo);break;case 10:pa(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(ba(t),t.flags|=128,null):(a&t.child.childLanes)!==0?df(e,t,a):(ba(t),e=ta(e,t,a),e!==null?e.sibling:null);ba(t);break;case 19:var i=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(mn(e,t,a,!1),l=(a&t.childLanes)!==0),i){if(l)return hf(e,t,a);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),ae(Qe,Qe.current),l)break;return null;case 22:case 23:return t.lanes=0,sf(e,t,a);case 24:pa(t,Ve,e.memoizedState.cache)}return ta(e,t,a)}function mf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Fe=!0;else{if(!go(e,a)&&(t.flags&128)===0)return Fe=!1,Dy(e,t,a);Fe=(e.flags&131072)!==0}else Fe=!1,Te&&(t.flags&1048576)!==0&&Xu(t,As,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")Tr(l)?(e=Wa(l,e),t.tag=1,t=cf(null,t,l,e,a)):(t.tag=0,t=co(null,t,l,e,a));else{if(l!=null){if(i=l.$$typeof,i===N){t.tag=11,t=af(null,t,l,e,a);break e}else if(i===P){t.tag=14,t=lf(null,t,l,e,a);break e}}throw t=re(l)||l,Error(c(306,t,""))}}return t;case 0:return co(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,i=Wa(l,t.pendingProps),cf(e,t,l,i,a);case 3:e:{if(ze(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var o=t.memoizedState;i=o.element,Hr(e,t),jn(t,l,null,a);var h=t.memoizedState;if(l=h.cache,pa(t,Ve,l),l!==o.cache&&Mr(t,[Ve],a,!0),bn(),l=h.element,o.isDehydrated)if(o={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=uf(e,t,l,a);break e}else if(l!==i){i=Tt(Error(c(424)),t),hn(i),t=uf(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ke=zt(e.firstChild),lt=t,Te=!0,Qa=null,Ht=!0,a=Qd(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(fn(),l===i){t=ta(e,t,a);break e}$e(e,t,l,a)}t=t.child}return t;case 26:return Js(e,t),e===null?(a=vh(t.type,null,t.pendingProps,null))?t.memoizedState=a:Te||(a=t.type,e=t.pendingProps,l=ui(he.current).createElement(a),l[Ie]=t,l[st]=e,Pe(l,a,e),Ze(l),t.stateNode=l):t.memoizedState=vh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Qi(t),e===null&&Te&&(l=t.stateNode=ph(t.type,t.pendingProps,he.current),lt=t,Ht=!0,i=ke,Ra(t.type)?(Wo=i,ke=zt(l.firstChild)):ke=i),$e(e,t,t.pendingProps.children,a),Js(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Te&&((i=l=ke)&&(l=i0(l,t.type,t.pendingProps,Ht),l!==null?(t.stateNode=l,lt=t,ke=zt(l.firstChild),Ht=!1,i=!0):i=!1),i||Za(t)),Qi(t),i=t.type,o=t.pendingProps,h=e!==null?e.memoizedProps:null,l=o.children,Fo(i,o)?l=null:h!==null&&Fo(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=Qr(e,t,Ey,null,null,a),Xn._currentValue=i),Js(e,t),$e(e,t,l,a),t.child;case 6:return e===null&&Te&&((e=a=ke)&&(a=r0(a,t.pendingProps,Ht),a!==null?(t.stateNode=a,lt=t,ke=null,e=!0):e=!1),e||Za(t)),null;case 13:return df(e,t,a);case 4:return ze(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Ol(t,null,l,a):$e(e,t,l,a),t.child;case 11:return af(e,t,t.type,t.pendingProps,a);case 7:return $e(e,t,t.pendingProps,a),t.child;case 8:return $e(e,t,t.pendingProps.children,a),t.child;case 12:return $e(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,pa(t,t.type,l.value),$e(e,t,l.children,a),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,Ka(t),i=et(i),l=l(i),t.flags|=1,$e(e,t,l,a),t.child;case 14:return lf(e,t,t.type,t.pendingProps,a);case 15:return nf(e,t,t.type,t.pendingProps,a);case 19:return hf(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=$s(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Kt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return sf(e,t,a);case 24:return Ka(t),l=et(Ve),e===null?(i=Br(),i===null&&(i=De,o=zr(),i.pooledCache=o,o.refCount++,o!==null&&(i.pooledCacheLanes|=a),i=o),t.memoizedState={parent:l,cache:i},kr(t),pa(t,Ve,i)):((e.lanes&a)!==0&&(Hr(e,t),jn(t,null,null,a),bn()),i=e.memoizedState,o=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),pa(t,Ve,l)):(l=o.cache,pa(t,Ve,l),l!==i.cache&&Mr(t,[Ve],a,!0))),$e(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function aa(e){e.flags|=4}function pf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Nh(t)){if(t=At.current,t!==null&&((Ne&4194048)===Ne?qt!==null:(Ne&62914560)!==Ne&&(Ne&536870912)===0||t!==qt))throw vn=Lr,Wu;e.flags|=8192}}function Ws(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Zc():536870912,e.lanes|=t,Ul|=t)}function Cn(e,t){if(!Te)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function My(e,t,a){var l=t.pendingProps;switch(Ar(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Le(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Pt(Ve),da(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(dn(t)?aa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Zu())),Le(t),null;case 26:return a=t.memoizedState,e===null?(aa(t),a!==null?(Le(t),pf(t,a)):(Le(t),t.flags&=-16777217)):a?a!==e.memoizedState?(aa(t),Le(t),pf(t,a)):(Le(t),t.flags&=-16777217):(e.memoizedProps!==l&&aa(t),Le(t),t.flags&=-16777217),null;case 27:os(t),a=he.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Le(t),null}e=I.current,dn(t)?Vu(t):(e=ph(i,l,a),t.stateNode=e,aa(t))}return Le(t),null;case 5:if(os(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Le(t),null}if(e=I.current,dn(t))Vu(t);else{switch(i=ui(he.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(a,{is:l.is}):i.createElement(a)}}e[Ie]=t,e[st]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(Pe(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&aa(t)}}return Le(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=he.current,dn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,i=lt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[Ie]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||oh(e.nodeValue,a)),e||Za(t)}else e=ui(e).createTextNode(l),e[Ie]=t,t.stateNode=e}return Le(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=dn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[Ie]=t}else fn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Le(t),i=!1}else i=Zu(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(ea(t),t):(ea(t),null)}if(ea(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var o=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(o=l.memoizedState.cachePool.pool),o!==i&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Ws(t,t.updateQueue),Le(t),null;case 4:return da(),e===null&&Go(t.stateNode.containerInfo),Le(t),null;case 10:return Pt(t.type),Le(t),null;case 19:if(le(Qe),i=t.memoizedState,i===null)return Le(t),null;if(l=(t.flags&128)!==0,o=i.rendering,o===null)if(l)Cn(i,!1);else{if(He!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=Zs(e),o!==null){for(t.flags|=128,Cn(i,!1),e=o.updateQueue,t.updateQueue=e,Ws(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Gu(a,e),a=a.sibling;return ae(Qe,Qe.current&1|2),t.child}e=e.sibling}i.tail!==null&&kt()>ei&&(t.flags|=128,l=!0,Cn(i,!1),t.lanes=4194304)}else{if(!l)if(e=Zs(o),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Ws(t,e),Cn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!Te)return Le(t),null}else 2*kt()-i.renderingStartTime>ei&&a!==536870912&&(t.flags|=128,l=!0,Cn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(e=i.last,e!==null?e.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=kt(),t.sibling=null,e=Qe.current,ae(Qe,l?e&1|2:e&1),t):(Le(t),null);case 22:case 23:return ea(t),Xr(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),a=t.updateQueue,a!==null&&Ws(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&le(Ja),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Pt(Ve),Le(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function zy(e,t){switch(Ar(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Pt(Ve),da(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return os(t),null;case 13:if(ea(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));fn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(Qe),null;case 4:return da(),null;case 10:return Pt(t.type),null;case 22:case 23:return ea(t),Xr(),e!==null&&le(Ja),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Pt(Ve),null;case 25:return null;default:return null}}function yf(e,t){switch(Ar(t),t.tag){case 3:Pt(Ve),da();break;case 26:case 27:case 5:os(t);break;case 4:da();break;case 13:ea(t);break;case 19:le(Qe);break;case 10:Pt(t.type);break;case 22:case 23:ea(t),Xr(),e!==null&&le(Ja);break;case 24:Pt(Ve)}}function An(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var i=l.next;a=i;do{if((a.tag&e)===e){l=void 0;var o=a.create,h=a.inst;l=o(),h.destroy=l}a=a.next}while(a!==i)}}catch(y){Oe(t,t.return,y)}}function Sa(e,t,a){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var o=i.next;l=o;do{if((l.tag&e)===e){var h=l.inst,y=h.destroy;if(y!==void 0){h.destroy=void 0,i=t;var j=a,L=y;try{L()}catch(Z){Oe(i,j,Z)}}}l=l.next}while(l!==o)}}catch(Z){Oe(t,t.return,Z)}}function gf(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{ld(t,a)}catch(l){Oe(e,e.return,l)}}}function vf(e,t,a){a.props=Wa(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Oe(e,t,l)}}function Rn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(i){Oe(e,t,i)}}function Yt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(i){Oe(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){Oe(e,t,i)}else a.current=null}function xf(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(i){Oe(e,e.return,i)}}function vo(e,t,a){try{var l=e.stateNode;t0(l,e.type,a,t),l[st]=t}catch(i){Oe(e,e.return,i)}}function bf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ra(e.type)||e.tag===4}function xo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ra(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function bo(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=ci));else if(l!==4&&(l===27&&Ra(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(bo(e,t,a),e=e.sibling;e!==null;)bo(e,t,a),e=e.sibling}function Ps(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Ra(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ps(e,t,a),e=e.sibling;e!==null;)Ps(e,t,a),e=e.sibling}function jf(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Pe(t,l,a),t[Ie]=e,t[st]=a}catch(o){Oe(e,e.return,o)}}var la=!1,Ye=!1,jo=!1,Sf=typeof WeakSet=="function"?WeakSet:Set,Ke=null;function Uy(e,t){if(e=e.containerInfo,Qo=yi,e=Du(e),vr(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var i=l.anchorOffset,o=l.focusNode;l=l.focusOffset;try{a.nodeType,o.nodeType}catch{a=null;break e}var h=0,y=-1,j=-1,L=0,Z=0,J=e,k=null;t:for(;;){for(var H;J!==a||i!==0&&J.nodeType!==3||(y=h+i),J!==o||l!==0&&J.nodeType!==3||(j=h+l),J.nodeType===3&&(h+=J.nodeValue.length),(H=J.firstChild)!==null;)k=J,J=H;for(;;){if(J===e)break t;if(k===a&&++L===i&&(y=h),k===o&&++Z===l&&(j=h),(H=J.nextSibling)!==null)break;J=k,k=J.parentNode}J=H}a=y===-1||j===-1?null:{start:y,end:j}}else a=null}a=a||{start:0,end:0}}else a=null;for(Zo={focusedElem:e,selectionRange:a},yi=!1,Ke=t;Ke!==null;)if(t=Ke,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ke=e;else for(;Ke!==null;){switch(t=Ke,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,a=t,i=o.memoizedProps,o=o.memoizedState,l=a.stateNode;try{var fe=Wa(a.type,i,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(fe,o),l.__reactInternalSnapshotBeforeUpdate=e}catch(ue){Oe(a,a.return,ue)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Jo(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Jo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Ke=e;break}Ke=t.return}}function Nf(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Na(e,a),l&4&&An(5,a);break;case 1:if(Na(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(h){Oe(a,a.return,h)}else{var i=Wa(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Oe(a,a.return,h)}}l&64&&gf(a),l&512&&Rn(a,a.return);break;case 3:if(Na(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{ld(e,t)}catch(h){Oe(a,a.return,h)}}break;case 27:t===null&&l&4&&jf(a);case 26:case 5:Na(e,a),t===null&&l&4&&xf(a),l&512&&Rn(a,a.return);break;case 12:Na(e,a);break;case 13:Na(e,a),l&4&&_f(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Vy.bind(null,a),o0(e,a))));break;case 22:if(l=a.memoizedState!==null||la,!l){t=t!==null&&t.memoizedState!==null||Ye,i=la;var o=Ye;la=l,(Ye=t)&&!o?Ea(e,a,(a.subtreeFlags&8772)!==0):Na(e,a),la=i,Ye=o}break;case 30:break;default:Na(e,a)}}function Ef(e){var t=e.alternate;t!==null&&(e.alternate=null,Ef(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ii(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ue=null,ot=!1;function na(e,t,a){for(a=a.child;a!==null;)Tf(e,t,a),a=a.sibling}function Tf(e,t,a){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount($l,a)}catch{}switch(a.tag){case 26:Ye||Yt(a,t),na(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ye||Yt(a,t);var l=Ue,i=ot;Ra(a.type)&&(Ue=a.stateNode,ot=!1),na(e,t,a),Hn(a.stateNode),Ue=l,ot=i;break;case 5:Ye||Yt(a,t);case 6:if(l=Ue,i=ot,Ue=null,na(e,t,a),Ue=l,ot=i,Ue!==null)if(ot)try{(Ue.nodeType===9?Ue.body:Ue.nodeName==="HTML"?Ue.ownerDocument.body:Ue).removeChild(a.stateNode)}catch(o){Oe(a,t,o)}else try{Ue.removeChild(a.stateNode)}catch(o){Oe(a,t,o)}break;case 18:Ue!==null&&(ot?(e=Ue,hh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Fn(e)):hh(Ue,a.stateNode));break;case 4:l=Ue,i=ot,Ue=a.stateNode.containerInfo,ot=!0,na(e,t,a),Ue=l,ot=i;break;case 0:case 11:case 14:case 15:Ye||Sa(2,a,t),Ye||Sa(4,a,t),na(e,t,a);break;case 1:Ye||(Yt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&vf(a,t,l)),na(e,t,a);break;case 21:na(e,t,a);break;case 22:Ye=(l=Ye)||a.memoizedState!==null,na(e,t,a),Ye=l;break;default:na(e,t,a)}}function _f(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Fn(e)}catch(a){Oe(t,t.return,a)}}function By(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Sf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Sf),t;default:throw Error(c(435,e.tag))}}function So(e,t){var a=By(e);t.forEach(function(l){var i=Qy.bind(null,e,l);a.has(l)||(a.add(l),l.then(i,i))})}function gt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var i=a[l],o=e,h=t,y=h;e:for(;y!==null;){switch(y.tag){case 27:if(Ra(y.type)){Ue=y.stateNode,ot=!1;break e}break;case 5:Ue=y.stateNode,ot=!1;break e;case 3:case 4:Ue=y.stateNode.containerInfo,ot=!0;break e}y=y.return}if(Ue===null)throw Error(c(160));Tf(o,h,i),Ue=null,ot=!1,o=i.alternate,o!==null&&(o.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)wf(t,e),t=t.sibling}var Mt=null;function wf(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:gt(t,e),vt(e),l&4&&(Sa(3,e,e.return),An(3,e),Sa(5,e,e.return));break;case 1:gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),l&64&&la&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var i=Mt;if(gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),l&4){var o=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":o=i.getElementsByTagName("title")[0],(!o||o[Il]||o[Ie]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=i.createElement(l),i.head.insertBefore(o,i.querySelector("head > title"))),Pe(o,l,a),o[Ie]=e,Ze(o),l=o;break e;case"link":var h=jh("link","href",i).get(l+(a.href||""));if(h){for(var y=0;y<h.length;y++)if(o=h[y],o.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&o.getAttribute("rel")===(a.rel==null?null:a.rel)&&o.getAttribute("title")===(a.title==null?null:a.title)&&o.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){h.splice(y,1);break t}}o=i.createElement(l),Pe(o,l,a),i.head.appendChild(o);break;case"meta":if(h=jh("meta","content",i).get(l+(a.content||""))){for(y=0;y<h.length;y++)if(o=h[y],o.getAttribute("content")===(a.content==null?null:""+a.content)&&o.getAttribute("name")===(a.name==null?null:a.name)&&o.getAttribute("property")===(a.property==null?null:a.property)&&o.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&o.getAttribute("charset")===(a.charSet==null?null:a.charSet)){h.splice(y,1);break t}}o=i.createElement(l),Pe(o,l,a),i.head.appendChild(o);break;default:throw Error(c(468,l))}o[Ie]=e,Ze(o),l=o}e.stateNode=l}else Sh(i,e.type,e.stateNode);else e.stateNode=bh(i,l,e.memoizedProps);else o!==l?(o===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):o.count--,l===null?Sh(i,e.type,e.stateNode):bh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&vo(e,e.memoizedProps,a.memoizedProps)}break;case 27:gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),a!==null&&l&4&&vo(e,e.memoizedProps,a.memoizedProps);break;case 5:if(gt(t,e),vt(e),l&512&&(Ye||a===null||Yt(a,a.return)),e.flags&32){i=e.stateNode;try{hl(i,"")}catch(H){Oe(e,e.return,H)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,vo(e,i,a!==null?a.memoizedProps:i)),l&1024&&(jo=!0);break;case 6:if(gt(t,e),vt(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(H){Oe(e,e.return,H)}}break;case 3:if(hi=null,i=Mt,Mt=di(t.containerInfo),gt(t,e),Mt=i,vt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Fn(t.containerInfo)}catch(H){Oe(e,e.return,H)}jo&&(jo=!1,Cf(e));break;case 4:l=Mt,Mt=di(e.stateNode.containerInfo),gt(t,e),vt(e),Mt=l;break;case 12:gt(t,e),vt(e);break;case 13:gt(t,e),vt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Co=kt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,So(e,l)));break;case 22:i=e.memoizedState!==null;var j=a!==null&&a.memoizedState!==null,L=la,Z=Ye;if(la=L||i,Ye=Z||j,gt(t,e),Ye=Z,la=L,vt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(a===null||j||la||Ye||Pa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){j=a=t;try{if(o=j.stateNode,i)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{y=j.stateNode;var J=j.memoizedProps.style,k=J!=null&&J.hasOwnProperty("display")?J.display:null;y.style.display=k==null||typeof k=="boolean"?"":(""+k).trim()}}catch(H){Oe(j,j.return,H)}}}else if(t.tag===6){if(a===null){j=t;try{j.stateNode.nodeValue=i?"":j.memoizedProps}catch(H){Oe(j,j.return,H)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,So(e,a))));break;case 19:gt(t,e),vt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,So(e,l)));break;case 30:break;case 21:break;default:gt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(bf(l)){a=l;break}l=l.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var i=a.stateNode,o=xo(e);Ps(e,o,i);break;case 5:var h=a.stateNode;a.flags&32&&(hl(h,""),a.flags&=-33);var y=xo(e);Ps(e,y,h);break;case 3:case 4:var j=a.stateNode.containerInfo,L=xo(e);bo(e,L,j);break;default:throw Error(c(161))}}catch(Z){Oe(e,e.return,Z)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Cf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Na(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Nf(e,t.alternate,t),t=t.sibling}function Pa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Sa(4,t,t.return),Pa(t);break;case 1:Yt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&vf(t,t.return,a),Pa(t);break;case 27:Hn(t.stateNode);case 26:case 5:Yt(t,t.return),Pa(t);break;case 22:t.memoizedState===null&&Pa(t);break;case 30:Pa(t);break;default:Pa(t)}e=e.sibling}}function Ea(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,o=t,h=o.flags;switch(o.tag){case 0:case 11:case 15:Ea(i,o,a),An(4,o);break;case 1:if(Ea(i,o,a),l=o,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(L){Oe(l,l.return,L)}if(l=o,i=l.updateQueue,i!==null){var y=l.stateNode;try{var j=i.shared.hiddenCallbacks;if(j!==null)for(i.shared.hiddenCallbacks=null,i=0;i<j.length;i++)ad(j[i],y)}catch(L){Oe(l,l.return,L)}}a&&h&64&&gf(o),Rn(o,o.return);break;case 27:jf(o);case 26:case 5:Ea(i,o,a),a&&l===null&&h&4&&xf(o),Rn(o,o.return);break;case 12:Ea(i,o,a);break;case 13:Ea(i,o,a),a&&h&4&&_f(i,o);break;case 22:o.memoizedState===null&&Ea(i,o,a),Rn(o,o.return);break;case 30:break;default:Ea(i,o,a)}t=t.sibling}}function No(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&pn(a))}function Eo(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pn(e))}function Gt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Af(e,t,a,l),t=t.sibling}function Af(e,t,a,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Gt(e,t,a,l),i&2048&&An(9,t);break;case 1:Gt(e,t,a,l);break;case 3:Gt(e,t,a,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pn(e)));break;case 12:if(i&2048){Gt(e,t,a,l),e=t.stateNode;try{var o=t.memoizedProps,h=o.id,y=o.onPostCommit;typeof y=="function"&&y(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Oe(t,t.return,j)}}else Gt(e,t,a,l);break;case 13:Gt(e,t,a,l);break;case 23:break;case 22:o=t.stateNode,h=t.alternate,t.memoizedState!==null?o._visibility&2?Gt(e,t,a,l):On(e,t):o._visibility&2?Gt(e,t,a,l):(o._visibility|=2,Dl(e,t,a,l,(t.subtreeFlags&10256)!==0)),i&2048&&No(h,t);break;case 24:Gt(e,t,a,l),i&2048&&Eo(t.alternate,t);break;default:Gt(e,t,a,l)}}function Dl(e,t,a,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,h=t,y=a,j=l,L=h.flags;switch(h.tag){case 0:case 11:case 15:Dl(o,h,y,j,i),An(8,h);break;case 23:break;case 22:var Z=h.stateNode;h.memoizedState!==null?Z._visibility&2?Dl(o,h,y,j,i):On(o,h):(Z._visibility|=2,Dl(o,h,y,j,i)),i&&L&2048&&No(h.alternate,h);break;case 24:Dl(o,h,y,j,i),i&&L&2048&&Eo(h.alternate,h);break;default:Dl(o,h,y,j,i)}t=t.sibling}}function On(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,i=l.flags;switch(l.tag){case 22:On(a,l),i&2048&&No(l.alternate,l);break;case 24:On(a,l),i&2048&&Eo(l.alternate,l);break;default:On(a,l)}t=t.sibling}}var Dn=8192;function Ml(e){if(e.subtreeFlags&Dn)for(e=e.child;e!==null;)Rf(e),e=e.sibling}function Rf(e){switch(e.tag){case 26:Ml(e),e.flags&Dn&&e.memoizedState!==null&&j0(Mt,e.memoizedState,e.memoizedProps);break;case 5:Ml(e);break;case 3:case 4:var t=Mt;Mt=di(e.stateNode.containerInfo),Ml(e),Mt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Dn,Dn=16777216,Ml(e),Dn=t):Ml(e));break;default:Ml(e)}}function Of(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Mn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Mf(l,e)}Of(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Df(e),e=e.sibling}function Df(e){switch(e.tag){case 0:case 11:case 15:Mn(e),e.flags&2048&&Sa(9,e,e.return);break;case 3:Mn(e);break;case 12:Mn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Is(e)):Mn(e);break;default:Mn(e)}}function Is(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Mf(l,e)}Of(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Sa(8,t,t.return),Is(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Is(t));break;default:Is(t)}e=e.sibling}}function Mf(e,t){for(;Ke!==null;){var a=Ke;switch(a.tag){case 0:case 11:case 15:Sa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:pn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ke=l;else e:for(a=e;Ke!==null;){l=Ke;var i=l.sibling,o=l.return;if(Ef(l),l===a){Ke=null;break e}if(i!==null){i.return=o,Ke=i;break e}Ke=o}}}var Ly={getCacheForType:function(e){var t=et(Ve),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},ky=typeof WeakMap=="function"?WeakMap:Map,_e=0,De=null,je=null,Ne=0,we=0,xt=null,Ta=!1,zl=!1,To=!1,sa=0,He=0,_a=0,Ia=0,_o=0,Rt=0,Ul=0,zn=null,ct=null,wo=!1,Co=0,ei=1/0,ti=null,wa=null,We=0,Ca=null,Bl=null,Ll=0,Ao=0,Ro=null,zf=null,Un=0,Oo=null;function bt(){if((_e&2)!==0&&Ne!==0)return Ne&-Ne;if(X.T!==null){var e=El;return e!==0?e:ko()}return Jc()}function Uf(){Rt===0&&(Rt=(Ne&536870912)===0||Te?Qc():536870912);var e=At.current;return e!==null&&(e.flags|=32),Rt}function jt(e,t,a){(e===De&&(we===2||we===9)||e.cancelPendingCommit!==null)&&(kl(e,0),Aa(e,Ne,Rt,!1)),Pl(e,a),((_e&2)===0||e!==De)&&(e===De&&((_e&2)===0&&(Ia|=a),He===4&&Aa(e,Ne,Rt,!1)),Xt(e))}function Bf(e,t,a){if((_e&6)!==0)throw Error(c(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Wl(e,t),i=l?Yy(e,t):zo(e,t,!0),o=l;do{if(i===0){zl&&!l&&Aa(e,t,0,!1);break}else{if(a=e.current.alternate,o&&!Hy(a)){i=zo(e,t,!1),o=!1;continue}if(i===2){if(o=t,e.errorRecoveryDisabledLanes&o)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var y=e;i=zn;var j=y.current.memoizedState.isDehydrated;if(j&&(kl(y,h).flags|=256),h=zo(y,h,!1),h!==2){if(To&&!j){y.errorRecoveryDisabledLanes|=o,Ia|=o,i=4;break e}o=ct,ct=i,o!==null&&(ct===null?ct=o:ct.push.apply(ct,o))}i=h}if(o=!1,i!==2)continue}}if(i===1){kl(e,0),Aa(e,t,0,!0);break}e:{switch(l=e,o=i,o){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Aa(l,t,Rt,!Ta);break e;case 2:ct=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=Co+300-kt(),10<i)){if(Aa(l,t,Rt,!Ta),fs(l,0,!0)!==0)break e;l.timeoutHandle=dh(Lf.bind(null,l,a,ct,ti,wo,t,Rt,Ia,Ul,Ta,o,2,-0,0),i);break e}Lf(l,a,ct,ti,wo,t,Rt,Ia,Ul,Ta,o,0,-0,0)}}break}while(!0);Xt(e)}function Lf(e,t,a,l,i,o,h,y,j,L,Z,J,k,H){if(e.timeoutHandle=-1,J=t.subtreeFlags,(J&8192||(J&16785408)===16785408)&&(Gn={stylesheets:null,count:0,unsuspend:b0},Rf(t),J=S0(),J!==null)){e.cancelPendingCommit=J(Vf.bind(null,e,t,o,a,l,i,h,y,j,Z,1,k,H)),Aa(e,o,h,!L);return}Vf(e,t,o,a,l,i,h,y,j)}function Hy(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var i=a[l],o=i.getSnapshot;i=i.value;try{if(!pt(o(),i))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Aa(e,t,a,l){t&=~_o,t&=~Ia,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var o=31-mt(i),h=1<<o;l[o]=-1,i&=~h}a!==0&&Fc(e,a,t)}function ai(){return(_e&6)===0?(Bn(0),!1):!0}function Do(){if(je!==null){if(we===0)var e=je.return;else e=je,Wt=Fa=null,Kr(e),Rl=null,_n=0,e=je;for(;e!==null;)yf(e.alternate,e),e=e.return;je=null}}function kl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,l0(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Do(),De=e,je=a=Kt(e.current,null),Ne=t,we=0,xt=null,Ta=!1,zl=Wl(e,t),To=!1,Ul=Rt=_o=Ia=_a=He=0,ct=zn=null,wo=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-mt(l),o=1<<i;t|=e[i],l&=~o}return sa=t,Es(),a}function kf(e,t){ve=null,X.H=Xs,t===gn||t===Ms?(t=ed(),we=3):t===Wu?(t=ed(),we=4):we=t===tf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,xt=t,je===null&&(He=1,Ks(e,Tt(t,e.current)))}function Hf(){var e=X.H;return X.H=Xs,e===null?Xs:e}function qf(){var e=X.A;return X.A=Ly,e}function Mo(){He=4,Ta||(Ne&4194048)!==Ne&&At.current!==null||(zl=!0),(_a&134217727)===0&&(Ia&134217727)===0||De===null||Aa(De,Ne,Rt,!1)}function zo(e,t,a){var l=_e;_e|=2;var i=Hf(),o=qf();(De!==e||Ne!==t)&&(ti=null,kl(e,t)),t=!1;var h=He;e:do try{if(we!==0&&je!==null){var y=je,j=xt;switch(we){case 8:Do(),h=6;break e;case 3:case 2:case 9:case 6:At.current===null&&(t=!0);var L=we;if(we=0,xt=null,Hl(e,y,j,L),a&&zl){h=0;break e}break;default:L=we,we=0,xt=null,Hl(e,y,j,L)}}qy(),h=He;break}catch(Z){kf(e,Z)}while(!0);return t&&e.shellSuspendCounter++,Wt=Fa=null,_e=l,X.H=i,X.A=o,je===null&&(De=null,Ne=0,Es()),h}function qy(){for(;je!==null;)Yf(je)}function Yy(e,t){var a=_e;_e|=2;var l=Hf(),i=qf();De!==e||Ne!==t?(ti=null,ei=kt()+500,kl(e,t)):zl=Wl(e,t);e:do try{if(we!==0&&je!==null){t=je;var o=xt;t:switch(we){case 1:we=0,xt=null,Hl(e,t,o,1);break;case 2:case 9:if(Pu(o)){we=0,xt=null,Gf(t);break}t=function(){we!==2&&we!==9||De!==e||(we=7),Xt(e)},o.then(t,t);break e;case 3:we=7;break e;case 4:we=5;break e;case 7:Pu(o)?(we=0,xt=null,Gf(t)):(we=0,xt=null,Hl(e,t,o,7));break;case 5:var h=null;switch(je.tag){case 26:h=je.memoizedState;case 5:case 27:var y=je;if(!h||Nh(h)){we=0,xt=null;var j=y.sibling;if(j!==null)je=j;else{var L=y.return;L!==null?(je=L,li(L)):je=null}break t}}we=0,xt=null,Hl(e,t,o,5);break;case 6:we=0,xt=null,Hl(e,t,o,6);break;case 8:Do(),He=6;break e;default:throw Error(c(462))}}Gy();break}catch(Z){kf(e,Z)}while(!0);return Wt=Fa=null,X.H=l,X.A=i,_e=a,je!==null?0:(De=null,Ne=0,Es(),He)}function Gy(){for(;je!==null&&!up();)Yf(je)}function Yf(e){var t=mf(e.alternate,e,sa);e.memoizedProps=e.pendingProps,t===null?li(e):je=t}function Gf(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=of(a,t,t.pendingProps,t.type,void 0,Ne);break;case 11:t=of(a,t,t.pendingProps,t.type.render,t.ref,Ne);break;case 5:Kr(t);default:yf(a,t),t=je=Gu(t,sa),t=mf(a,t,sa)}e.memoizedProps=e.pendingProps,t===null?li(e):je=t}function Hl(e,t,a,l){Wt=Fa=null,Kr(t),Rl=null,_n=0;var i=t.return;try{if(Oy(e,i,t,a,Ne)){He=1,Ks(e,Tt(a,e.current)),je=null;return}}catch(o){if(i!==null)throw je=i,o;He=1,Ks(e,Tt(a,e.current)),je=null;return}t.flags&32768?(Te||l===1?e=!0:zl||(Ne&536870912)!==0?e=!1:(Ta=e=!0,(l===2||l===9||l===3||l===6)&&(l=At.current,l!==null&&l.tag===13&&(l.flags|=16384))),Xf(t,e)):li(t)}function li(e){var t=e;do{if((t.flags&32768)!==0){Xf(t,Ta);return}e=t.return;var a=My(t.alternate,t,sa);if(a!==null){je=a;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);He===0&&(He=5)}function Xf(e,t){do{var a=zy(e.alternate,e);if(a!==null){a.flags&=32767,je=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){je=e;return}je=e=a}while(e!==null);He=6,je=null}function Vf(e,t,a,l,i,o,h,y,j){e.cancelPendingCommit=null;do ni();while(We!==0);if((_e&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(o=t.lanes|t.childLanes,o|=Nr,bp(e,a,o,h,y,j),e===De&&(je=De=null,Ne=0),Bl=t,Ca=e,Ll=a,Ao=o,Ro=i,zf=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Zy(cs,function(){return Jf(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=X.T,X.T=null,i=te.p,te.p=2,h=_e,_e|=4;try{Uy(e,t,a)}finally{_e=h,te.p=i,X.T=l}}We=1,Qf(),Zf(),Ff()}}function Qf(){if(We===1){We=0;var e=Ca,t=Bl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=X.T,X.T=null;var l=te.p;te.p=2;var i=_e;_e|=4;try{wf(t,e);var o=Zo,h=Du(e.containerInfo),y=o.focusedElem,j=o.selectionRange;if(h!==y&&y&&y.ownerDocument&&Ou(y.ownerDocument.documentElement,y)){if(j!==null&&vr(y)){var L=j.start,Z=j.end;if(Z===void 0&&(Z=L),"selectionStart"in y)y.selectionStart=L,y.selectionEnd=Math.min(Z,y.value.length);else{var J=y.ownerDocument||document,k=J&&J.defaultView||window;if(k.getSelection){var H=k.getSelection(),fe=y.textContent.length,ue=Math.min(j.start,fe),Re=j.end===void 0?ue:Math.min(j.end,fe);!H.extend&&ue>Re&&(h=Re,Re=ue,ue=h);var M=Ru(y,ue),O=Ru(y,Re);if(M&&O&&(H.rangeCount!==1||H.anchorNode!==M.node||H.anchorOffset!==M.offset||H.focusNode!==O.node||H.focusOffset!==O.offset)){var B=J.createRange();B.setStart(M.node,M.offset),H.removeAllRanges(),ue>Re?(H.addRange(B),H.extend(O.node,O.offset)):(B.setEnd(O.node,O.offset),H.addRange(B))}}}}for(J=[],H=y;H=H.parentNode;)H.nodeType===1&&J.push({element:H,left:H.scrollLeft,top:H.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<J.length;y++){var K=J[y];K.element.scrollLeft=K.left,K.element.scrollTop=K.top}}yi=!!Qo,Zo=Qo=null}finally{_e=i,te.p=l,X.T=a}}e.current=t,We=2}}function Zf(){if(We===2){We=0;var e=Ca,t=Bl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=X.T,X.T=null;var l=te.p;te.p=2;var i=_e;_e|=4;try{Nf(e,t.alternate,t)}finally{_e=i,te.p=l,X.T=a}}We=3}}function Ff(){if(We===4||We===3){We=0,dp();var e=Ca,t=Bl,a=Ll,l=zf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?We=5:(We=0,Bl=Ca=null,Kf(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(wa=null),Wi(a),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot($l,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=X.T,i=te.p,te.p=2,X.T=null;try{for(var o=e.onRecoverableError,h=0;h<l.length;h++){var y=l[h];o(y.value,{componentStack:y.stack})}}finally{X.T=t,te.p=i}}(Ll&3)!==0&&ni(),Xt(e),i=e.pendingLanes,(a&4194090)!==0&&(i&42)!==0?e===Oo?Un++:(Un=0,Oo=e):Un=0,Bn(0)}}function Kf(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,pn(t)))}function ni(e){return Qf(),Zf(),Ff(),Jf()}function Jf(){if(We!==5)return!1;var e=Ca,t=Ao;Ao=0;var a=Wi(Ll),l=X.T,i=te.p;try{te.p=32>a?32:a,X.T=null,a=Ro,Ro=null;var o=Ca,h=Ll;if(We=0,Bl=Ca=null,Ll=0,(_e&6)!==0)throw Error(c(331));var y=_e;if(_e|=4,Df(o.current),Af(o,o.current,h,a),_e=y,Bn(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot($l,o)}catch{}return!0}finally{te.p=i,X.T=l,Kf(e,t)}}function $f(e,t,a){t=Tt(a,t),t=oo(e.stateNode,t,2),e=va(e,t,2),e!==null&&(Pl(e,2),Xt(e))}function Oe(e,t,a){if(e.tag===3)$f(e,e,a);else for(;t!==null;){if(t.tag===3){$f(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(wa===null||!wa.has(l))){e=Tt(a,e),a=Id(2),l=va(t,a,2),l!==null&&(ef(a,l,t,e),Pl(l,2),Xt(l));break}}t=t.return}}function Uo(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new ky;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(a)||(To=!0,i.add(a),e=Xy.bind(null,e,t,a),t.then(e,e))}function Xy(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,De===e&&(Ne&a)===a&&(He===4||He===3&&(Ne&62914560)===Ne&&300>kt()-Co?(_e&2)===0&&kl(e,0):_o|=a,Ul===Ne&&(Ul=0)),Xt(e)}function Wf(e,t){t===0&&(t=Zc()),e=bl(e,t),e!==null&&(Pl(e,t),Xt(e))}function Vy(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Wf(e,a)}function Qy(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(a=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),Wf(e,a)}function Zy(e,t){return Fi(e,t)}var si=null,ql=null,Bo=!1,ii=!1,Lo=!1,el=0;function Xt(e){e!==ql&&e.next===null&&(ql===null?si=ql=e:ql=ql.next=e),ii=!0,Bo||(Bo=!0,Ky())}function Bn(e,t){if(!Lo&&ii){Lo=!0;do for(var a=!1,l=si;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var o=0;else{var h=l.suspendedLanes,y=l.pingedLanes;o=(1<<31-mt(42|e)+1)-1,o&=i&~(h&~y),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(a=!0,th(l,o))}else o=Ne,o=fs(l,l===De?o:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(o&3)===0||Wl(l,o)||(a=!0,th(l,o));l=l.next}while(a);Lo=!1}}function Fy(){Pf()}function Pf(){ii=Bo=!1;var e=0;el!==0&&(a0()&&(e=el),el=0);for(var t=kt(),a=null,l=si;l!==null;){var i=l.next,o=If(l,t);o===0?(l.next=null,a===null?si=i:a.next=i,i===null&&(ql=a)):(a=l,(e!==0||(o&3)!==0)&&(ii=!0)),l=i}Bn(e)}function If(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var h=31-mt(o),y=1<<h,j=i[h];j===-1?((y&a)===0||(y&l)!==0)&&(i[h]=xp(y,t)):j<=t&&(e.expiredLanes|=y),o&=~y}if(t=De,a=Ne,a=fs(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(we===2||we===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Ki(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Wl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Ki(l),Wi(a)){case 2:case 8:a=Xc;break;case 32:a=cs;break;case 268435456:a=Vc;break;default:a=cs}return l=eh.bind(null,e),a=Fi(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Ki(l),e.callbackPriority=2,e.callbackNode=null,2}function eh(e,t){if(We!==0&&We!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ni()&&e.callbackNode!==a)return null;var l=Ne;return l=fs(e,e===De?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Bf(e,l,t),If(e,kt()),e.callbackNode!=null&&e.callbackNode===a?eh.bind(null,e):null)}function th(e,t){if(ni())return null;Bf(e,t,!0)}function Ky(){n0(function(){(_e&6)!==0?Fi(Gc,Fy):Pf()})}function ko(){return el===0&&(el=Qc()),el}function ah(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:gs(""+e)}function lh(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Jy(e,t,a,l,i){if(t==="submit"&&a&&a.stateNode===i){var o=ah((i[st]||null).action),h=l.submitter;h&&(t=(t=h[st]||null)?ah(t.formAction):h.getAttribute("formAction"),t!==null&&(o=t,h=null));var y=new js("action","action",null,l,i);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(el!==0){var j=h?lh(i,h):new FormData(i);lo(a,{pending:!0,data:j,method:i.method,action:o},null,j)}}else typeof o=="function"&&(y.preventDefault(),j=h?lh(i,h):new FormData(i),lo(a,{pending:!0,data:j,method:i.method,action:o},o,j))},currentTarget:i}]})}}for(var Ho=0;Ho<Sr.length;Ho++){var qo=Sr[Ho],$y=qo.toLowerCase(),Wy=qo[0].toUpperCase()+qo.slice(1);Dt($y,"on"+Wy)}Dt(Uu,"onAnimationEnd"),Dt(Bu,"onAnimationIteration"),Dt(Lu,"onAnimationStart"),Dt("dblclick","onDoubleClick"),Dt("focusin","onFocus"),Dt("focusout","onBlur"),Dt(my,"onTransitionRun"),Dt(py,"onTransitionStart"),Dt(yy,"onTransitionCancel"),Dt(ku,"onTransitionEnd"),ul("onMouseEnter",["mouseout","mouseover"]),ul("onMouseLeave",["mouseout","mouseover"]),ul("onPointerEnter",["pointerout","pointerover"]),ul("onPointerLeave",["pointerout","pointerover"]),ka("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ka("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ka("onBeforeInput",["compositionend","keypress","textInput","paste"]),ka("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ka("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ka("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ln="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Py=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ln));function nh(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],i=l.event;l=l.listeners;e:{var o=void 0;if(t)for(var h=l.length-1;0<=h;h--){var y=l[h],j=y.instance,L=y.currentTarget;if(y=y.listener,j!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=L;try{o(i)}catch(Z){Fs(Z)}i.currentTarget=null,o=j}else for(h=0;h<l.length;h++){if(y=l[h],j=y.instance,L=y.currentTarget,y=y.listener,j!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=L;try{o(i)}catch(Z){Fs(Z)}i.currentTarget=null,o=j}}}}function Se(e,t){var a=t[Pi];a===void 0&&(a=t[Pi]=new Set);var l=e+"__bubble";a.has(l)||(sh(t,e,2,!1),a.add(l))}function Yo(e,t,a){var l=0;t&&(l|=4),sh(a,e,l,t)}var ri="_reactListening"+Math.random().toString(36).slice(2);function Go(e){if(!e[ri]){e[ri]=!0,Wc.forEach(function(a){a!=="selectionchange"&&(Py.has(a)||Yo(a,!1,e),Yo(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ri]||(t[ri]=!0,Yo("selectionchange",!1,t))}}function sh(e,t,a,l){switch(Ah(t)){case 2:var i=T0;break;case 8:i=_0;break;default:i=ac}a=i.bind(null,t,a,e),i=void 0,!cr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,a,{capture:!0,passive:i}):e.addEventListener(t,a,!0):i!==void 0?e.addEventListener(t,a,{passive:i}):e.addEventListener(t,a,!1)}function Xo(e,t,a,l,i){var o=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var y=l.stateNode.containerInfo;if(y===i)break;if(h===4)for(h=l.return;h!==null;){var j=h.tag;if((j===3||j===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;y!==null;){if(h=rl(y),h===null)return;if(j=h.tag,j===5||j===6||j===26||j===27){l=o=h;continue e}y=y.parentNode}}l=l.return}du(function(){var L=o,Z=rr(a),J=[];e:{var k=Hu.get(e);if(k!==void 0){var H=js,fe=e;switch(e){case"keypress":if(xs(a)===0)break e;case"keydown":case"keyup":H=Zp;break;case"focusin":fe="focus",H=hr;break;case"focusout":fe="blur",H=hr;break;case"beforeblur":case"afterblur":H=hr;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":H=mu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":H=zp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":H=Jp;break;case Uu:case Bu:case Lu:H=Lp;break;case ku:H=Wp;break;case"scroll":case"scrollend":H=Dp;break;case"wheel":H=Ip;break;case"copy":case"cut":case"paste":H=Hp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":H=yu;break;case"toggle":case"beforetoggle":H=ty}var ue=(t&4)!==0,Re=!ue&&(e==="scroll"||e==="scrollend"),M=ue?k!==null?k+"Capture":null:k;ue=[];for(var O=L,B;O!==null;){var K=O;if(B=K.stateNode,K=K.tag,K!==5&&K!==26&&K!==27||B===null||M===null||(K=tn(O,M),K!=null&&ue.push(kn(O,K,B))),Re)break;O=O.return}0<ue.length&&(k=new H(k,fe,null,a,Z),J.push({event:k,listeners:ue}))}}if((t&7)===0){e:{if(k=e==="mouseover"||e==="pointerover",H=e==="mouseout"||e==="pointerout",k&&a!==ir&&(fe=a.relatedTarget||a.fromElement)&&(rl(fe)||fe[il]))break e;if((H||k)&&(k=Z.window===Z?Z:(k=Z.ownerDocument)?k.defaultView||k.parentWindow:window,H?(fe=a.relatedTarget||a.toElement,H=L,fe=fe?rl(fe):null,fe!==null&&(Re=f(fe),ue=fe.tag,fe!==Re||ue!==5&&ue!==27&&ue!==6)&&(fe=null)):(H=null,fe=L),H!==fe)){if(ue=mu,K="onMouseLeave",M="onMouseEnter",O="mouse",(e==="pointerout"||e==="pointerover")&&(ue=yu,K="onPointerLeave",M="onPointerEnter",O="pointer"),Re=H==null?k:en(H),B=fe==null?k:en(fe),k=new ue(K,O+"leave",H,a,Z),k.target=Re,k.relatedTarget=B,K=null,rl(Z)===L&&(ue=new ue(M,O+"enter",fe,a,Z),ue.target=B,ue.relatedTarget=Re,K=ue),Re=K,H&&fe)t:{for(ue=H,M=fe,O=0,B=ue;B;B=Yl(B))O++;for(B=0,K=M;K;K=Yl(K))B++;for(;0<O-B;)ue=Yl(ue),O--;for(;0<B-O;)M=Yl(M),B--;for(;O--;){if(ue===M||M!==null&&ue===M.alternate)break t;ue=Yl(ue),M=Yl(M)}ue=null}else ue=null;H!==null&&ih(J,k,H,ue,!1),fe!==null&&Re!==null&&ih(J,Re,fe,ue,!0)}}e:{if(k=L?en(L):window,H=k.nodeName&&k.nodeName.toLowerCase(),H==="select"||H==="input"&&k.type==="file")var ie=Eu;else if(Su(k))if(Tu)ie=dy;else{ie=cy;var be=oy}else H=k.nodeName,!H||H.toLowerCase()!=="input"||k.type!=="checkbox"&&k.type!=="radio"?L&&sr(L.elementType)&&(ie=Eu):ie=uy;if(ie&&(ie=ie(e,L))){Nu(J,ie,a,Z);break e}be&&be(e,k,L),e==="focusout"&&L&&k.type==="number"&&L.memoizedProps.value!=null&&nr(k,"number",k.value)}switch(be=L?en(L):window,e){case"focusin":(Su(be)||be.contentEditable==="true")&&(gl=be,xr=L,un=null);break;case"focusout":un=xr=gl=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,Mu(J,a,Z);break;case"selectionchange":if(hy)break;case"keydown":case"keyup":Mu(J,a,Z)}var oe;if(pr)e:{switch(e){case"compositionstart":var de="onCompositionStart";break e;case"compositionend":de="onCompositionEnd";break e;case"compositionupdate":de="onCompositionUpdate";break e}de=void 0}else yl?bu(e,a)&&(de="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(de="onCompositionStart");de&&(gu&&a.locale!=="ko"&&(yl||de!=="onCompositionStart"?de==="onCompositionEnd"&&yl&&(oe=fu()):(ma=Z,ur="value"in ma?ma.value:ma.textContent,yl=!0)),be=oi(L,de),0<be.length&&(de=new pu(de,e,null,a,Z),J.push({event:de,listeners:be}),oe?de.data=oe:(oe=ju(a),oe!==null&&(de.data=oe)))),(oe=ly?ny(e,a):sy(e,a))&&(de=oi(L,"onBeforeInput"),0<de.length&&(be=new pu("onBeforeInput","beforeinput",null,a,Z),J.push({event:be,listeners:de}),be.data=oe)),Jy(J,e,L,a,Z)}nh(J,t)})}function kn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function oi(e,t){for(var a=t+"Capture",l=[];e!==null;){var i=e,o=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||o===null||(i=tn(e,a),i!=null&&l.unshift(kn(e,i,o)),i=tn(e,t),i!=null&&l.push(kn(e,i,o))),e.tag===3)return l;e=e.return}return[]}function Yl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function ih(e,t,a,l,i){for(var o=t._reactName,h=[];a!==null&&a!==l;){var y=a,j=y.alternate,L=y.stateNode;if(y=y.tag,j!==null&&j===l)break;y!==5&&y!==26&&y!==27||L===null||(j=L,i?(L=tn(a,o),L!=null&&h.unshift(kn(a,L,j))):i||(L=tn(a,o),L!=null&&h.push(kn(a,L,j)))),a=a.return}h.length!==0&&e.push({event:t,listeners:h})}var Iy=/\r\n?/g,e0=/\u0000|\uFFFD/g;function rh(e){return(typeof e=="string"?e:""+e).replace(Iy,`
`).replace(e0,"")}function oh(e,t){return t=rh(t),rh(e)===t}function ci(){}function Ae(e,t,a,l,i,o){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||hl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&hl(e,""+l);break;case"className":ms(e,"class",l);break;case"tabIndex":ms(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ms(e,a,l);break;case"style":cu(e,l,o);break;case"data":if(t!=="object"){ms(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gs(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(a==="formAction"?(t!=="input"&&Ae(e,t,"name",i.name,i,null),Ae(e,t,"formEncType",i.formEncType,i,null),Ae(e,t,"formMethod",i.formMethod,i,null),Ae(e,t,"formTarget",i.formTarget,i,null)):(Ae(e,t,"encType",i.encType,i,null),Ae(e,t,"method",i.method,i,null),Ae(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gs(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=ci);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=gs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":Se("beforetoggle",e),Se("toggle",e),hs(e,"popover",l);break;case"xlinkActuate":Zt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Zt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Zt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Zt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Zt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Zt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Zt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":hs(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Rp.get(a)||a,hs(e,a,l))}}function Vo(e,t,a,l,i,o){switch(a){case"style":cu(e,l,o);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof l=="string"?hl(e,l):(typeof l=="number"||typeof l=="bigint")&&hl(e,""+l);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"onClick":l!=null&&(e.onclick=ci);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Pc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),t=a.slice(2,i?a.length-7:void 0),o=e[st]||null,o=o!=null?o[a]:null,typeof o=="function"&&e.removeEventListener(t,o,i),typeof l=="function")){typeof o!="function"&&o!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,i);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):hs(e,a,l)}}}function Pe(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var l=!1,i=!1,o;for(o in a)if(a.hasOwnProperty(o)){var h=a[o];if(h!=null)switch(o){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ae(e,t,o,h,a,null)}}i&&Ae(e,t,"srcSet",a.srcSet,a,null),l&&Ae(e,t,"src",a.src,a,null);return;case"input":Se("invalid",e);var y=o=h=i=null,j=null,L=null;for(l in a)if(a.hasOwnProperty(l)){var Z=a[l];if(Z!=null)switch(l){case"name":i=Z;break;case"type":h=Z;break;case"checked":j=Z;break;case"defaultChecked":L=Z;break;case"value":o=Z;break;case"defaultValue":y=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(c(137,t));break;default:Ae(e,t,l,Z,a,null)}}su(e,o,y,j,L,h,i,!1),ps(e);return;case"select":Se("invalid",e),l=h=o=null;for(i in a)if(a.hasOwnProperty(i)&&(y=a[i],y!=null))switch(i){case"value":o=y;break;case"defaultValue":h=y;break;case"multiple":l=y;default:Ae(e,t,i,y,a,null)}t=o,a=h,e.multiple=!!l,t!=null?fl(e,!!l,t,!1):a!=null&&fl(e,!!l,a,!0);return;case"textarea":Se("invalid",e),o=i=l=null;for(h in a)if(a.hasOwnProperty(h)&&(y=a[h],y!=null))switch(h){case"value":l=y;break;case"defaultValue":i=y;break;case"children":o=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(c(91));break;default:Ae(e,t,h,y,a,null)}ru(e,l,i,o),ps(e);return;case"option":for(j in a)if(a.hasOwnProperty(j)&&(l=a[j],l!=null))switch(j){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ae(e,t,j,l,a,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(l=0;l<Ln.length;l++)Se(Ln[l],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(L in a)if(a.hasOwnProperty(L)&&(l=a[L],l!=null))switch(L){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ae(e,t,L,l,a,null)}return;default:if(sr(t)){for(Z in a)a.hasOwnProperty(Z)&&(l=a[Z],l!==void 0&&Vo(e,t,Z,l,a,void 0));return}}for(y in a)a.hasOwnProperty(y)&&(l=a[y],l!=null&&Ae(e,t,y,l,a,null))}function t0(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,o=null,h=null,y=null,j=null,L=null,Z=null;for(H in a){var J=a[H];if(a.hasOwnProperty(H)&&J!=null)switch(H){case"checked":break;case"value":break;case"defaultValue":j=J;default:l.hasOwnProperty(H)||Ae(e,t,H,null,l,J)}}for(var k in l){var H=l[k];if(J=a[k],l.hasOwnProperty(k)&&(H!=null||J!=null))switch(k){case"type":o=H;break;case"name":i=H;break;case"checked":L=H;break;case"defaultChecked":Z=H;break;case"value":h=H;break;case"defaultValue":y=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(c(137,t));break;default:H!==J&&Ae(e,t,k,H,l,J)}}lr(e,h,y,j,L,Z,o,i);return;case"select":H=h=y=k=null;for(o in a)if(j=a[o],a.hasOwnProperty(o)&&j!=null)switch(o){case"value":break;case"multiple":H=j;default:l.hasOwnProperty(o)||Ae(e,t,o,null,l,j)}for(i in l)if(o=l[i],j=a[i],l.hasOwnProperty(i)&&(o!=null||j!=null))switch(i){case"value":k=o;break;case"defaultValue":y=o;break;case"multiple":h=o;default:o!==j&&Ae(e,t,i,o,l,j)}t=y,a=h,l=H,k!=null?fl(e,!!a,k,!1):!!l!=!!a&&(t!=null?fl(e,!!a,t,!0):fl(e,!!a,a?[]:"",!1));return;case"textarea":H=k=null;for(y in a)if(i=a[y],a.hasOwnProperty(y)&&i!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:Ae(e,t,y,null,l,i)}for(h in l)if(i=l[h],o=a[h],l.hasOwnProperty(h)&&(i!=null||o!=null))switch(h){case"value":k=i;break;case"defaultValue":H=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==o&&Ae(e,t,h,i,l,o)}iu(e,k,H);return;case"option":for(var fe in a)if(k=a[fe],a.hasOwnProperty(fe)&&k!=null&&!l.hasOwnProperty(fe))switch(fe){case"selected":e.selected=!1;break;default:Ae(e,t,fe,null,l,k)}for(j in l)if(k=l[j],H=a[j],l.hasOwnProperty(j)&&k!==H&&(k!=null||H!=null))switch(j){case"selected":e.selected=k&&typeof k!="function"&&typeof k!="symbol";break;default:Ae(e,t,j,k,l,H)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ue in a)k=a[ue],a.hasOwnProperty(ue)&&k!=null&&!l.hasOwnProperty(ue)&&Ae(e,t,ue,null,l,k);for(L in l)if(k=l[L],H=a[L],l.hasOwnProperty(L)&&k!==H&&(k!=null||H!=null))switch(L){case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(c(137,t));break;default:Ae(e,t,L,k,l,H)}return;default:if(sr(t)){for(var Re in a)k=a[Re],a.hasOwnProperty(Re)&&k!==void 0&&!l.hasOwnProperty(Re)&&Vo(e,t,Re,void 0,l,k);for(Z in l)k=l[Z],H=a[Z],!l.hasOwnProperty(Z)||k===H||k===void 0&&H===void 0||Vo(e,t,Z,k,l,H);return}}for(var M in a)k=a[M],a.hasOwnProperty(M)&&k!=null&&!l.hasOwnProperty(M)&&Ae(e,t,M,null,l,k);for(J in l)k=l[J],H=a[J],!l.hasOwnProperty(J)||k===H||k==null&&H==null||Ae(e,t,J,k,l,H)}var Qo=null,Zo=null;function ui(e){return e.nodeType===9?e:e.ownerDocument}function ch(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function uh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Fo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ko=null;function a0(){var e=window.event;return e&&e.type==="popstate"?e===Ko?!1:(Ko=e,!0):(Ko=null,!1)}var dh=typeof setTimeout=="function"?setTimeout:void 0,l0=typeof clearTimeout=="function"?clearTimeout:void 0,fh=typeof Promise=="function"?Promise:void 0,n0=typeof queueMicrotask=="function"?queueMicrotask:typeof fh<"u"?function(e){return fh.resolve(null).then(e).catch(s0)}:dh;function s0(e){setTimeout(function(){throw e})}function Ra(e){return e==="head"}function hh(e,t){var a=t,l=0,i=0;do{var o=a.nextSibling;if(e.removeChild(a),o&&o.nodeType===8)if(a=o.data,a==="/$"){if(0<l&&8>l){a=l;var h=e.ownerDocument;if(a&1&&Hn(h.documentElement),a&2&&Hn(h.body),a&4)for(a=h.head,Hn(a),h=a.firstChild;h;){var y=h.nextSibling,j=h.nodeName;h[Il]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&h.rel.toLowerCase()==="stylesheet"||a.removeChild(h),h=y}}if(i===0){e.removeChild(o),Fn(t);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:l=a.charCodeAt(0)-48;else l=0;a=o}while(a);Fn(t)}function Jo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Jo(a),Ii(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function i0(e,t,a,l){for(;e.nodeType===1;){var i=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Il])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=zt(e.nextSibling),e===null)break}return null}function r0(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=zt(e.nextSibling),e===null))return null;return e}function $o(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function o0(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Wo=null;function mh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function ph(e,t,a){switch(t=ui(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Hn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ii(e)}var Ot=new Map,yh=new Set;function di(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ia=te.d;te.d={f:c0,r:u0,D:d0,C:f0,L:h0,m:m0,X:y0,S:p0,M:g0};function c0(){var e=ia.f(),t=ai();return e||t}function u0(e){var t=ol(e);t!==null&&t.tag===5&&t.type==="form"?Ud(t):ia.r(e)}var Gl=typeof document>"u"?null:document;function gh(e,t,a){var l=Gl;if(l&&typeof t=="string"&&t){var i=Et(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),yh.has(i)||(yh.add(i),e={rel:e,crossOrigin:a,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function d0(e){ia.D(e),gh("dns-prefetch",e,null)}function f0(e,t){ia.C(e,t),gh("preconnect",e,t)}function h0(e,t,a){ia.L(e,t,a);var l=Gl;if(l&&e&&t){var i='link[rel="preload"][as="'+Et(t)+'"]';t==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+Et(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+Et(a.imageSizes)+'"]')):i+='[href="'+Et(e)+'"]';var o=i;switch(t){case"style":o=Xl(e);break;case"script":o=Vl(e)}Ot.has(o)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Ot.set(o,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(qn(o))||t==="script"&&l.querySelector(Yn(o))||(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function m0(e,t){ia.m(e,t);var a=Gl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Et(l)+'"][href="'+Et(e)+'"]',o=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Vl(e)}if(!Ot.has(o)&&(e=x({rel:"modulepreload",href:e},t),Ot.set(o,e),a.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Yn(o)))return}l=a.createElement("link"),Pe(l,"link",e),Ze(l),a.head.appendChild(l)}}}function p0(e,t,a){ia.S(e,t,a);var l=Gl;if(l&&e){var i=cl(l).hoistableStyles,o=Xl(e);t=t||"default";var h=i.get(o);if(!h){var y={loading:0,preload:null};if(h=l.querySelector(qn(o)))y.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Ot.get(o))&&Po(e,a);var j=h=l.createElement("link");Ze(j),Pe(j,"link",e),j._p=new Promise(function(L,Z){j.onload=L,j.onerror=Z}),j.addEventListener("load",function(){y.loading|=1}),j.addEventListener("error",function(){y.loading|=2}),y.loading|=4,fi(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:y},i.set(o,h)}}}function y0(e,t){ia.X(e,t);var a=Gl;if(a&&e){var l=cl(a).hoistableScripts,i=Vl(e),o=l.get(i);o||(o=a.querySelector(Yn(i)),o||(e=x({src:e,async:!0},t),(t=Ot.get(i))&&Io(e,t),o=a.createElement("script"),Ze(o),Pe(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function g0(e,t){ia.M(e,t);var a=Gl;if(a&&e){var l=cl(a).hoistableScripts,i=Vl(e),o=l.get(i);o||(o=a.querySelector(Yn(i)),o||(e=x({src:e,async:!0,type:"module"},t),(t=Ot.get(i))&&Io(e,t),o=a.createElement("script"),Ze(o),Pe(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function vh(e,t,a,l){var i=(i=he.current)?di(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Xl(a.href),a=cl(i).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Xl(a.href);var o=cl(i).hoistableStyles,h=o.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,h),(o=i.querySelector(qn(e)))&&!o._p&&(h.instance=o,h.state.loading=5),Ot.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Ot.set(e,a),o||v0(i,e,a,h.state))),t&&l===null)throw Error(c(528,""));return h}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Vl(a),a=cl(i).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Xl(e){return'href="'+Et(e)+'"'}function qn(e){return'link[rel="stylesheet"]['+e+"]"}function xh(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function v0(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Pe(t,"link",a),Ze(t),e.head.appendChild(t))}function Vl(e){return'[src="'+Et(e)+'"]'}function Yn(e){return"script[async]"+e}function bh(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Et(a.href)+'"]');if(l)return t.instance=l,Ze(l),l;var i=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ze(l),Pe(l,"style",i),fi(l,a.precedence,e),t.instance=l;case"stylesheet":i=Xl(a.href);var o=e.querySelector(qn(i));if(o)return t.state.loading|=4,t.instance=o,Ze(o),o;l=xh(a),(i=Ot.get(i))&&Po(l,i),o=(e.ownerDocument||e).createElement("link"),Ze(o);var h=o;return h._p=new Promise(function(y,j){h.onload=y,h.onerror=j}),Pe(o,"link",l),t.state.loading|=4,fi(o,a.precedence,e),t.instance=o;case"script":return o=Vl(a.src),(i=e.querySelector(Yn(o)))?(t.instance=i,Ze(i),i):(l=a,(i=Ot.get(o))&&(l=x({},a),Io(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),Ze(i),Pe(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,fi(l,a.precedence,e));return t.instance}function fi(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,o=i,h=0;h<l.length;h++){var y=l[h];if(y.dataset.precedence===t)o=y;else if(o!==i)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Po(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Io(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var hi=null;function jh(e,t,a){if(hi===null){var l=new Map,i=hi=new Map;i.set(a,l)}else i=hi,l=i.get(a),l||(l=new Map,i.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),i=0;i<a.length;i++){var o=a[i];if(!(o[Il]||o[Ie]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(t)||"";h=e+h;var y=l.get(h);y?y.push(o):l.set(h,[o])}}return l}function Sh(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function x0(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Nh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Gn=null;function b0(){}function j0(e,t,a){if(Gn===null)throw Error(c(475));var l=Gn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=Xl(a.href),o=e.querySelector(qn(i));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=mi.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=o,Ze(o);return}o=e.ownerDocument||e,a=xh(a),(i=Ot.get(i))&&Po(a,i),o=o.createElement("link"),Ze(o);var h=o;h._p=new Promise(function(y,j){h.onload=y,h.onerror=j}),Pe(o,"link",a),t.instance=o}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=mi.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function S0(){if(Gn===null)throw Error(c(475));var e=Gn;return e.stylesheets&&e.count===0&&ec(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&ec(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function mi(){if(this.count--,this.count===0){if(this.stylesheets)ec(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var pi=null;function ec(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,pi=new Map,t.forEach(N0,e),pi=null,mi.call(e))}function N0(e,t){if(!(t.state.loading&4)){var a=pi.get(e);if(a)var l=a.get(null);else{a=new Map,pi.set(e,a);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<i.length;o++){var h=i[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(a.set(h.dataset.precedence,h),l=h)}l&&a.set(null,l)}i=t.instance,h=i.getAttribute("data-precedence"),o=a.get(h)||l,o===l&&a.set(null,i),a.set(h,i),this.count++,l=mi.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),o?o.parentNode.insertBefore(i,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Xn={$$typeof:T,Provider:null,Consumer:null,_currentValue:q,_currentValue2:q,_threadCount:0};function E0(e,t,a,l,i,o,h,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ji(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ji(0),this.hiddenUpdates=Ji(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Eh(e,t,a,l,i,o,h,y,j,L,Z,J){return e=new E0(e,t,a,h,y,j,L,J),t=1,o===!0&&(t|=24),o=yt(3,null,null,t),e.current=o,o.stateNode=e,t=zr(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:l,isDehydrated:a,cache:t},kr(o),e}function Th(e){return e?(e=jl,e):jl}function _h(e,t,a,l,i,o){i=Th(i),l.context===null?l.context=i:l.pendingContext=i,l=ga(t),l.payload={element:a},o=o===void 0?null:o,o!==null&&(l.callback=o),a=va(e,l,t),a!==null&&(jt(a,e,t),xn(a,e,t))}function wh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function tc(e,t){wh(e,t),(e=e.alternate)&&wh(e,t)}function Ch(e){if(e.tag===13){var t=bl(e,67108864);t!==null&&jt(t,e,67108864),tc(e,67108864)}}var yi=!0;function T0(e,t,a,l){var i=X.T;X.T=null;var o=te.p;try{te.p=2,ac(e,t,a,l)}finally{te.p=o,X.T=i}}function _0(e,t,a,l){var i=X.T;X.T=null;var o=te.p;try{te.p=8,ac(e,t,a,l)}finally{te.p=o,X.T=i}}function ac(e,t,a,l){if(yi){var i=lc(l);if(i===null)Xo(e,t,l,gi,a),Rh(e,l);else if(C0(i,e,t,a,l))l.stopPropagation();else if(Rh(e,l),t&4&&-1<w0.indexOf(e)){for(;i!==null;){var o=ol(i);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=La(o.pendingLanes);if(h!==0){var y=o;for(y.pendingLanes|=2,y.entangledLanes|=2;h;){var j=1<<31-mt(h);y.entanglements[1]|=j,h&=~j}Xt(o),(_e&6)===0&&(ei=kt()+500,Bn(0))}}break;case 13:y=bl(o,2),y!==null&&jt(y,o,2),ai(),tc(o,2)}if(o=lc(l),o===null&&Xo(e,t,l,gi,a),o===i)break;i=o}i!==null&&l.stopPropagation()}else Xo(e,t,l,null,a)}}function lc(e){return e=rr(e),nc(e)}var gi=null;function nc(e){if(gi=null,e=rl(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=p(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return gi=e,null}function Ah(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(fp()){case Gc:return 2;case Xc:return 8;case cs:case hp:return 32;case Vc:return 268435456;default:return 32}default:return 32}}var sc=!1,Oa=null,Da=null,Ma=null,Vn=new Map,Qn=new Map,za=[],w0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Rh(e,t){switch(e){case"focusin":case"focusout":Oa=null;break;case"dragenter":case"dragleave":Da=null;break;case"mouseover":case"mouseout":Ma=null;break;case"pointerover":case"pointerout":Vn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qn.delete(t.pointerId)}}function Zn(e,t,a,l,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:o,targetContainers:[i]},t!==null&&(t=ol(t),t!==null&&Ch(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function C0(e,t,a,l,i){switch(t){case"focusin":return Oa=Zn(Oa,e,t,a,l,i),!0;case"dragenter":return Da=Zn(Da,e,t,a,l,i),!0;case"mouseover":return Ma=Zn(Ma,e,t,a,l,i),!0;case"pointerover":var o=i.pointerId;return Vn.set(o,Zn(Vn.get(o)||null,e,t,a,l,i)),!0;case"gotpointercapture":return o=i.pointerId,Qn.set(o,Zn(Qn.get(o)||null,e,t,a,l,i)),!0}return!1}function Oh(e){var t=rl(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=p(a),t!==null){e.blockedOn=t,jp(e.priority,function(){if(a.tag===13){var l=bt();l=$i(l);var i=bl(a,l);i!==null&&jt(i,a,l),tc(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=lc(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);ir=l,a.target.dispatchEvent(l),ir=null}else return t=ol(a),t!==null&&Ch(t),e.blockedOn=a,!1;t.shift()}return!0}function Dh(e,t,a){vi(e)&&a.delete(t)}function A0(){sc=!1,Oa!==null&&vi(Oa)&&(Oa=null),Da!==null&&vi(Da)&&(Da=null),Ma!==null&&vi(Ma)&&(Ma=null),Vn.forEach(Dh),Qn.forEach(Dh)}function xi(e,t){e.blockedOn===t&&(e.blockedOn=null,sc||(sc=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,A0)))}var bi=null;function Mh(e){bi!==e&&(bi=e,s.unstable_scheduleCallback(s.unstable_NormalPriority,function(){bi===e&&(bi=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(nc(l||a)===null)continue;break}var o=ol(a);o!==null&&(e.splice(t,3),t-=3,lo(o,{pending:!0,data:i,method:a.method,action:l},l,i))}}))}function Fn(e){function t(j){return xi(j,e)}Oa!==null&&xi(Oa,e),Da!==null&&xi(Da,e),Ma!==null&&xi(Ma,e),Vn.forEach(t),Qn.forEach(t);for(var a=0;a<za.length;a++){var l=za[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<za.length&&(a=za[0],a.blockedOn===null);)Oh(a),a.blockedOn===null&&za.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var i=a[l],o=a[l+1],h=i[st]||null;if(typeof o=="function")h||Mh(a);else if(h){var y=null;if(o&&o.hasAttribute("formAction")){if(i=o,h=o[st]||null)y=h.formAction;else if(nc(i)!==null)continue}else y=h.action;typeof y=="function"?a[l+1]=y:(a.splice(l,3),l-=3),Mh(a)}}}function ic(e){this._internalRoot=e}ji.prototype.render=ic.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,l=bt();_h(a,l,e,t,null,null)},ji.prototype.unmount=ic.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;_h(e.current,2,null,e,null,null),ai(),t[il]=null}};function ji(e){this._internalRoot=e}ji.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jc();e={blockedOn:null,target:e,priority:t};for(var a=0;a<za.length&&t!==0&&t<za[a].priority;a++);za.splice(a,0,e),a===0&&Oh(e)}};var zh=r.version;if(zh!=="19.1.0")throw Error(c(527,zh,"19.1.0"));te.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=v(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var R0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:X,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Si.isDisabled&&Si.supportsFiber)try{$l=Si.inject(R0),ht=Si}catch{}}return Jn.createRoot=function(e,t){if(!d(e))throw Error(c(299));var a=!1,l="",i=Jd,o=$d,h=Wd,y=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Eh(e,1,!1,null,null,a,l,i,o,h,y,null),e[il]=t.current,Go(e),new ic(t)},Jn.hydrateRoot=function(e,t,a){if(!d(e))throw Error(c(299));var l=!1,i="",o=Jd,h=$d,y=Wd,j=null,L=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(o=a.onUncaughtError),a.onCaughtError!==void 0&&(h=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(j=a.unstable_transitionCallbacks),a.formState!==void 0&&(L=a.formState)),t=Eh(e,1,!0,t,a??null,l,i,o,h,y,j,L),t.context=Th(null),a=t.current,l=bt(),l=$i(l),i=ga(l),i.callback=null,va(a,i,l),a=l,t.current.lanes=a,Pl(t,a),Xt(t),e[il]=t.current,Go(e),new ji(t)},Jn.version="19.1.0",Jn}var Vh;function Y0(){if(Vh)return cc.exports;Vh=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(r){console.error(r)}}return s(),cc.exports=q0(),cc.exports}var G0=Y0(),$n={},Qh;function X0(){if(Qh)return $n;Qh=1,Object.defineProperty($n,"__esModule",{value:!0}),$n.parse=p,$n.serialize=m;const s=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,f=(()=>{const _=function(){};return _.prototype=Object.create(null),_})();function p(_,U){const w=new f,z=_.length;if(z<2)return w;const D=(U==null?void 0:U.decode)||x;let C=0;do{const A=_.indexOf("=",C);if(A===-1)break;const T=_.indexOf(";",C),N=T===-1?z:T;if(A>N){C=_.lastIndexOf(";",A-1)+1;continue}const R=g(_,C,A),W=v(_,A,R),P=_.slice(R,W);if(w[P]===void 0){let F=g(_,A+1,N),ee=v(_,N,F);const Q=D(_.slice(F,ee));w[P]=Q}C=N+1}while(C<z);return w}function g(_,U,w){do{const z=_.charCodeAt(U);if(z!==32&&z!==9)return U}while(++U<w);return w}function v(_,U,w){for(;U>w;){const z=_.charCodeAt(--U);if(z!==32&&z!==9)return U+1}return w}function m(_,U,w){const z=(w==null?void 0:w.encode)||encodeURIComponent;if(!s.test(_))throw new TypeError(`argument name is invalid: ${_}`);const D=z(U);if(!r.test(D))throw new TypeError(`argument val is invalid: ${U}`);let C=_+"="+D;if(!w)return C;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);C+="; Max-Age="+w.maxAge}if(w.domain){if(!u.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);C+="; Domain="+w.domain}if(w.path){if(!c.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);C+="; Path="+w.path}if(w.expires){if(!b(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);C+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(C+="; HttpOnly"),w.secure&&(C+="; Secure"),w.partitioned&&(C+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":C+="; Priority=Low";break;case"medium":C+="; Priority=Medium";break;case"high":C+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":C+="; SameSite=Strict";break;case"lax":C+="; SameSite=Lax";break;case"none":C+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return C}function x(_){if(_.indexOf("%")===-1)return _;try{return decodeURIComponent(_)}catch{return _}}function b(_){return d.call(_)==="[object Date]"}return $n}X0();var Zh="popstate";function V0(s={}){function r(c,d){let{pathname:f,search:p,hash:g}=c.location;return vc("",{pathname:f,search:p,hash:g},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function u(c,d){return typeof d=="string"?d:In(d)}return Z0(r,u,null,s)}function Be(s,r){if(s===!1||s===null||typeof s>"u")throw new Error(r)}function Ut(s,r){if(!s){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Q0(){return Math.random().toString(36).substring(2,10)}function Fh(s,r){return{usr:s.state,key:s.key,idx:r}}function vc(s,r,u=null,c){return{pathname:typeof s=="string"?s:s.pathname,search:"",hash:"",...typeof r=="string"?Ql(r):r,state:u,key:r&&r.key||c||Q0()}}function In({pathname:s="/",search:r="",hash:u=""}){return r&&r!=="?"&&(s+=r.charAt(0)==="?"?r:"?"+r),u&&u!=="#"&&(s+=u.charAt(0)==="#"?u:"#"+u),s}function Ql(s){let r={};if(s){let u=s.indexOf("#");u>=0&&(r.hash=s.substring(u),s=s.substring(0,u));let c=s.indexOf("?");c>=0&&(r.search=s.substring(c),s=s.substring(0,c)),s&&(r.pathname=s)}return r}function Z0(s,r,u,c={}){let{window:d=document.defaultView,v5Compat:f=!1}=c,p=d.history,g="POP",v=null,m=x();m==null&&(m=0,p.replaceState({...p.state,idx:m},""));function x(){return(p.state||{idx:null}).idx}function b(){g="POP";let D=x(),C=D==null?null:D-m;m=D,v&&v({action:g,location:z.location,delta:C})}function _(D,C){g="PUSH";let A=vc(z.location,D,C);m=x()+1;let T=Fh(A,m),N=z.createHref(A);try{p.pushState(T,"",N)}catch(R){if(R instanceof DOMException&&R.name==="DataCloneError")throw R;d.location.assign(N)}f&&v&&v({action:g,location:z.location,delta:1})}function U(D,C){g="REPLACE";let A=vc(z.location,D,C);m=x();let T=Fh(A,m),N=z.createHref(A);p.replaceState(T,"",N),f&&v&&v({action:g,location:z.location,delta:0})}function w(D){return F0(D)}let z={get action(){return g},get location(){return s(d,p)},listen(D){if(v)throw new Error("A history only accepts one active listener");return d.addEventListener(Zh,b),v=D,()=>{d.removeEventListener(Zh,b),v=null}},createHref(D){return r(d,D)},createURL:w,encodeLocation(D){let C=w(D);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:_,replace:U,go(D){return p.go(D)}};return z}function F0(s,r=!1){let u="http://localhost";typeof window<"u"&&(u=window.location.origin!=="null"?window.location.origin:window.location.href),Be(u,"No window.location.(origin|href) available to create URL");let c=typeof s=="string"?s:In(s);return c=c.replace(/ $/,"%20"),!r&&c.startsWith("//")&&(c=u+c),new URL(c,u)}function hm(s,r,u="/"){return K0(s,r,u,!1)}function K0(s,r,u,c){let d=typeof r=="string"?Ql(r):r,f=ca(d.pathname||"/",u);if(f==null)return null;let p=mm(s);J0(p);let g=null;for(let v=0;g==null&&v<p.length;++v){let m=ig(f);g=ng(p[v],m,c)}return g}function mm(s,r=[],u=[],c=""){let d=(f,p,g)=>{let v={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:p,route:f};v.relativePath.startsWith("/")&&(Be(v.relativePath.startsWith(c),`Absolute route path "${v.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(c.length));let m=ra([c,v.relativePath]),x=u.concat(v);f.children&&f.children.length>0&&(Be(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),mm(f.children,r,x,m)),!(f.path==null&&!f.index)&&r.push({path:m,score:ag(m,f.index),routesMeta:x})};return s.forEach((f,p)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))d(f,p);else for(let v of pm(f.path))d(f,p,v)}),r}function pm(s){let r=s.split("/");if(r.length===0)return[];let[u,...c]=r,d=u.endsWith("?"),f=u.replace(/\?$/,"");if(c.length===0)return d?[f,""]:[f];let p=pm(c.join("/")),g=[];return g.push(...p.map(v=>v===""?f:[f,v].join("/"))),d&&g.push(...p),g.map(v=>s.startsWith("/")&&v===""?"/":v)}function J0(s){s.sort((r,u)=>r.score!==u.score?u.score-r.score:lg(r.routesMeta.map(c=>c.childrenIndex),u.routesMeta.map(c=>c.childrenIndex)))}var $0=/^:[\w-]+$/,W0=3,P0=2,I0=1,eg=10,tg=-2,Kh=s=>s==="*";function ag(s,r){let u=s.split("/"),c=u.length;return u.some(Kh)&&(c+=tg),r&&(c+=P0),u.filter(d=>!Kh(d)).reduce((d,f)=>d+($0.test(f)?W0:f===""?I0:eg),c)}function lg(s,r){return s.length===r.length&&s.slice(0,-1).every((c,d)=>c===r[d])?s[s.length-1]-r[r.length-1]:0}function ng(s,r,u=!1){let{routesMeta:c}=s,d={},f="/",p=[];for(let g=0;g<c.length;++g){let v=c[g],m=g===c.length-1,x=f==="/"?r:r.slice(f.length)||"/",b=Ai({path:v.relativePath,caseSensitive:v.caseSensitive,end:m},x),_=v.route;if(!b&&m&&u&&!c[c.length-1].route.index&&(b=Ai({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},x)),!b)return null;Object.assign(d,b.params),p.push({params:d,pathname:ra([f,b.pathname]),pathnameBase:ug(ra([f,b.pathnameBase])),route:_}),b.pathnameBase!=="/"&&(f=ra([f,b.pathnameBase]))}return p}function Ai(s,r){typeof s=="string"&&(s={path:s,caseSensitive:!1,end:!0});let[u,c]=sg(s.path,s.caseSensitive,s.end),d=r.match(u);if(!d)return null;let f=d[0],p=f.replace(/(.)\/+$/,"$1"),g=d.slice(1);return{params:c.reduce((m,{paramName:x,isOptional:b},_)=>{if(x==="*"){let w=g[_]||"";p=f.slice(0,f.length-w.length).replace(/(.)\/+$/,"$1")}const U=g[_];return b&&!U?m[x]=void 0:m[x]=(U||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:p,pattern:s}}function sg(s,r=!1,u=!0){Ut(s==="*"||!s.endsWith("*")||s.endsWith("/*"),`Route path "${s}" will be treated as if it were "${s.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${s.replace(/\*$/,"/*")}".`);let c=[],d="^"+s.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(p,g,v)=>(c.push({paramName:g,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return s.endsWith("*")?(c.push({paramName:"*"}),d+=s==="*"||s==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?d+="\\/*$":s!==""&&s!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,r?void 0:"i"),c]}function ig(s){try{return s.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Ut(!1,`The URL path "${s}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),s}}function ca(s,r){if(r==="/")return s;if(!s.toLowerCase().startsWith(r.toLowerCase()))return null;let u=r.endsWith("/")?r.length-1:r.length,c=s.charAt(u);return c&&c!=="/"?null:s.slice(u)||"/"}function rg(s,r="/"){let{pathname:u,search:c="",hash:d=""}=typeof s=="string"?Ql(s):s;return{pathname:u?u.startsWith("/")?u:og(u,r):r,search:dg(c),hash:fg(d)}}function og(s,r){let u=r.replace(/\/+$/,"").split("/");return s.split("/").forEach(d=>{d===".."?u.length>1&&u.pop():d!=="."&&u.push(d)}),u.length>1?u.join("/"):"/"}function hc(s,r,u,c){return`Cannot include a '${s}' character in a manually specified \`to.${r}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${u}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function cg(s){return s.filter((r,u)=>u===0||r.route.path&&r.route.path.length>0)}function Oc(s){let r=cg(s);return r.map((u,c)=>c===r.length-1?u.pathname:u.pathnameBase)}function Dc(s,r,u,c=!1){let d;typeof s=="string"?d=Ql(s):(d={...s},Be(!d.pathname||!d.pathname.includes("?"),hc("?","pathname","search",d)),Be(!d.pathname||!d.pathname.includes("#"),hc("#","pathname","hash",d)),Be(!d.search||!d.search.includes("#"),hc("#","search","hash",d)));let f=s===""||d.pathname==="",p=f?"/":d.pathname,g;if(p==null)g=u;else{let b=r.length-1;if(!c&&p.startsWith("..")){let _=p.split("/");for(;_[0]==="..";)_.shift(),b-=1;d.pathname=_.join("/")}g=b>=0?r[b]:"/"}let v=rg(d,g),m=p&&p!=="/"&&p.endsWith("/"),x=(f||p===".")&&u.endsWith("/");return!v.pathname.endsWith("/")&&(m||x)&&(v.pathname+="/"),v}var ra=s=>s.join("/").replace(/\/\/+/g,"/"),ug=s=>s.replace(/\/+$/,"").replace(/^\/*/,"/"),dg=s=>!s||s==="?"?"":s.startsWith("?")?s:"?"+s,fg=s=>!s||s==="#"?"":s.startsWith("#")?s:"#"+s;function hg(s){return s!=null&&typeof s.status=="number"&&typeof s.statusText=="string"&&typeof s.internal=="boolean"&&"data"in s}var ym=["POST","PUT","PATCH","DELETE"];new Set(ym);var mg=["GET",...ym];new Set(mg);var Zl=E.createContext(null);Zl.displayName="DataRouter";var Di=E.createContext(null);Di.displayName="DataRouterState";var gm=E.createContext({isTransitioning:!1});gm.displayName="ViewTransition";var pg=E.createContext(new Map);pg.displayName="Fetchers";var yg=E.createContext(null);yg.displayName="Await";var Bt=E.createContext(null);Bt.displayName="Navigation";var as=E.createContext(null);as.displayName="Location";var Qt=E.createContext({outlet:null,matches:[],isDataRoute:!1});Qt.displayName="Route";var Mc=E.createContext(null);Mc.displayName="RouteError";function gg(s,{relative:r}={}){Be(Fl(),"useHref() may be used only in the context of a <Router> component.");let{basename:u,navigator:c}=E.useContext(Bt),{hash:d,pathname:f,search:p}=ls(s,{relative:r}),g=f;return u!=="/"&&(g=f==="/"?u:ra([u,f])),c.createHref({pathname:g,search:p,hash:d})}function Fl(){return E.useContext(as)!=null}function Ba(){return Be(Fl(),"useLocation() may be used only in the context of a <Router> component."),E.useContext(as).location}var vm="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function xm(s){E.useContext(Bt).static||E.useLayoutEffect(s)}function bm(){let{isDataRoute:s}=E.useContext(Qt);return s?Rg():vg()}function vg(){Be(Fl(),"useNavigate() may be used only in the context of a <Router> component.");let s=E.useContext(Zl),{basename:r,navigator:u}=E.useContext(Bt),{matches:c}=E.useContext(Qt),{pathname:d}=Ba(),f=JSON.stringify(Oc(c)),p=E.useRef(!1);return xm(()=>{p.current=!0}),E.useCallback((v,m={})=>{if(Ut(p.current,vm),!p.current)return;if(typeof v=="number"){u.go(v);return}let x=Dc(v,JSON.parse(f),d,m.relative==="path");s==null&&r!=="/"&&(x.pathname=x.pathname==="/"?r:ra([r,x.pathname])),(m.replace?u.replace:u.push)(x,m.state,m)},[r,u,f,d,s])}E.createContext(null);function ls(s,{relative:r}={}){let{matches:u}=E.useContext(Qt),{pathname:c}=Ba(),d=JSON.stringify(Oc(u));return E.useMemo(()=>Dc(s,JSON.parse(d),c,r==="path"),[s,d,c,r])}function xg(s,r){return jm(s,r)}function jm(s,r,u,c){var A;Be(Fl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d,static:f}=E.useContext(Bt),{matches:p}=E.useContext(Qt),g=p[p.length-1],v=g?g.params:{},m=g?g.pathname:"/",x=g?g.pathnameBase:"/",b=g&&g.route;{let T=b&&b.path||"";Sm(m,!b||T.endsWith("*")||T.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${T}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${T}"> to <Route path="${T==="/"?"*":`${T}/*`}">.`)}let _=Ba(),U;if(r){let T=typeof r=="string"?Ql(r):r;Be(x==="/"||((A=T.pathname)==null?void 0:A.startsWith(x)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${x}" but pathname "${T.pathname}" was given in the \`location\` prop.`),U=T}else U=_;let w=U.pathname||"/",z=w;if(x!=="/"){let T=x.replace(/^\//,"").split("/");z="/"+w.replace(/^\//,"").split("/").slice(T.length).join("/")}let D=!f&&u&&u.matches&&u.matches.length>0?u.matches:hm(s,{pathname:z});Ut(b||D!=null,`No routes matched location "${U.pathname}${U.search}${U.hash}" `),Ut(D==null||D[D.length-1].route.element!==void 0||D[D.length-1].route.Component!==void 0||D[D.length-1].route.lazy!==void 0,`Matched leaf route at location "${U.pathname}${U.search}${U.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let C=Eg(D&&D.map(T=>Object.assign({},T,{params:Object.assign({},v,T.params),pathname:ra([x,d.encodeLocation?d.encodeLocation(T.pathname).pathname:T.pathname]),pathnameBase:T.pathnameBase==="/"?x:ra([x,d.encodeLocation?d.encodeLocation(T.pathnameBase).pathname:T.pathnameBase])})),p,u,c);return r&&C?E.createElement(as.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...U},navigationType:"POP"}},C):C}function bg(){let s=Ag(),r=hg(s)?`${s.status} ${s.statusText}`:s instanceof Error?s.message:JSON.stringify(s),u=s instanceof Error?s.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},f={padding:"2px 4px",backgroundColor:c},p=null;return console.error("Error handled by React Router default ErrorBoundary:",s),p=E.createElement(E.Fragment,null,E.createElement("p",null,"💿 Hey developer 👋"),E.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",E.createElement("code",{style:f},"ErrorBoundary")," or"," ",E.createElement("code",{style:f},"errorElement")," prop on your route.")),E.createElement(E.Fragment,null,E.createElement("h2",null,"Unexpected Application Error!"),E.createElement("h3",{style:{fontStyle:"italic"}},r),u?E.createElement("pre",{style:d},u):null,p)}var jg=E.createElement(bg,null),Sg=class extends E.Component{constructor(s){super(s),this.state={location:s.location,revalidation:s.revalidation,error:s.error}}static getDerivedStateFromError(s){return{error:s}}static getDerivedStateFromProps(s,r){return r.location!==s.location||r.revalidation!=="idle"&&s.revalidation==="idle"?{error:s.error,location:s.location,revalidation:s.revalidation}:{error:s.error!==void 0?s.error:r.error,location:r.location,revalidation:s.revalidation||r.revalidation}}componentDidCatch(s,r){console.error("React Router caught the following error during render",s,r)}render(){return this.state.error!==void 0?E.createElement(Qt.Provider,{value:this.props.routeContext},E.createElement(Mc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Ng({routeContext:s,match:r,children:u}){let c=E.useContext(Zl);return c&&c.static&&c.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=r.route.id),E.createElement(Qt.Provider,{value:s},u)}function Eg(s,r=[],u=null,c=null){if(s==null){if(!u)return null;if(u.errors)s=u.matches;else if(r.length===0&&!u.initialized&&u.matches.length>0)s=u.matches;else return null}let d=s,f=u==null?void 0:u.errors;if(f!=null){let v=d.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);Be(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),d=d.slice(0,Math.min(d.length,v+1))}let p=!1,g=-1;if(u)for(let v=0;v<d.length;v++){let m=d[v];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(g=v),m.route.id){let{loaderData:x,errors:b}=u,_=m.route.loader&&!x.hasOwnProperty(m.route.id)&&(!b||b[m.route.id]===void 0);if(m.route.lazy||_){p=!0,g>=0?d=d.slice(0,g+1):d=[d[0]];break}}}return d.reduceRight((v,m,x)=>{let b,_=!1,U=null,w=null;u&&(b=f&&m.route.id?f[m.route.id]:void 0,U=m.route.errorElement||jg,p&&(g<0&&x===0?(Sm("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),_=!0,w=null):g===x&&(_=!0,w=m.route.hydrateFallbackElement||null)));let z=r.concat(d.slice(0,x+1)),D=()=>{let C;return b?C=U:_?C=w:m.route.Component?C=E.createElement(m.route.Component,null):m.route.element?C=m.route.element:C=v,E.createElement(Ng,{match:m,routeContext:{outlet:v,matches:z,isDataRoute:u!=null},children:C})};return u&&(m.route.ErrorBoundary||m.route.errorElement||x===0)?E.createElement(Sg,{location:u.location,revalidation:u.revalidation,component:U,error:b,children:D(),routeContext:{outlet:null,matches:z,isDataRoute:!0}}):D()},null)}function zc(s){return`${s} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Tg(s){let r=E.useContext(Zl);return Be(r,zc(s)),r}function _g(s){let r=E.useContext(Di);return Be(r,zc(s)),r}function wg(s){let r=E.useContext(Qt);return Be(r,zc(s)),r}function Uc(s){let r=wg(s),u=r.matches[r.matches.length-1];return Be(u.route.id,`${s} can only be used on routes that contain a unique "id"`),u.route.id}function Cg(){return Uc("useRouteId")}function Ag(){var c;let s=E.useContext(Mc),r=_g("useRouteError"),u=Uc("useRouteError");return s!==void 0?s:(c=r.errors)==null?void 0:c[u]}function Rg(){let{router:s}=Tg("useNavigate"),r=Uc("useNavigate"),u=E.useRef(!1);return xm(()=>{u.current=!0}),E.useCallback(async(d,f={})=>{Ut(u.current,vm),u.current&&(typeof d=="number"?s.navigate(d):await s.navigate(d,{fromRouteId:r,...f}))},[s,r])}var Jh={};function Sm(s,r,u){!r&&!Jh[s]&&(Jh[s]=!0,Ut(!1,u))}E.memo(Og);function Og({routes:s,future:r,state:u}){return jm(s,void 0,u,r)}function Dg({to:s,replace:r,state:u,relative:c}){Be(Fl(),"<Navigate> may be used only in the context of a <Router> component.");let{static:d}=E.useContext(Bt);Ut(!d,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=E.useContext(Qt),{pathname:p}=Ba(),g=bm(),v=Dc(s,Oc(f),p,c==="path"),m=JSON.stringify(v);return E.useEffect(()=>{g(JSON.parse(m),{replace:r,state:u,relative:c})},[g,m,c,r,u]),null}function St(s){Be(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Mg({basename:s="/",children:r=null,location:u,navigationType:c="POP",navigator:d,static:f=!1}){Be(!Fl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let p=s.replace(/^\/*/,"/"),g=E.useMemo(()=>({basename:p,navigator:d,static:f,future:{}}),[p,d,f]);typeof u=="string"&&(u=Ql(u));let{pathname:v="/",search:m="",hash:x="",state:b=null,key:_="default"}=u,U=E.useMemo(()=>{let w=ca(v,p);return w==null?null:{location:{pathname:w,search:m,hash:x,state:b,key:_},navigationType:c}},[p,v,m,x,b,_,c]);return Ut(U!=null,`<Router basename="${p}"> is not able to match the URL "${v}${m}${x}" because it does not start with the basename, so the <Router> won't render anything.`),U==null?null:E.createElement(Bt.Provider,{value:g},E.createElement(as.Provider,{children:r,value:U}))}function zg({children:s,location:r}){return xg(xc(s),r)}function xc(s,r=[]){let u=[];return E.Children.forEach(s,(c,d)=>{if(!E.isValidElement(c))return;let f=[...r,d];if(c.type===E.Fragment){u.push.apply(u,xc(c.props.children,f));return}Be(c.type===St,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Be(!c.props.index||!c.props.children,"An index route cannot have child routes.");let p={id:c.props.id||f.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(p.children=xc(c.props.children,f)),u.push(p)}),u}var Ei="get",Ti="application/x-www-form-urlencoded";function Mi(s){return s!=null&&typeof s.tagName=="string"}function Ug(s){return Mi(s)&&s.tagName.toLowerCase()==="button"}function Bg(s){return Mi(s)&&s.tagName.toLowerCase()==="form"}function Lg(s){return Mi(s)&&s.tagName.toLowerCase()==="input"}function kg(s){return!!(s.metaKey||s.altKey||s.ctrlKey||s.shiftKey)}function Hg(s,r){return s.button===0&&(!r||r==="_self")&&!kg(s)}var Ni=null;function qg(){if(Ni===null)try{new FormData(document.createElement("form"),0),Ni=!1}catch{Ni=!0}return Ni}var Yg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function mc(s){return s!=null&&!Yg.has(s)?(Ut(!1,`"${s}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ti}"`),null):s}function Gg(s,r){let u,c,d,f,p;if(Bg(s)){let g=s.getAttribute("action");c=g?ca(g,r):null,u=s.getAttribute("method")||Ei,d=mc(s.getAttribute("enctype"))||Ti,f=new FormData(s)}else if(Ug(s)||Lg(s)&&(s.type==="submit"||s.type==="image")){let g=s.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=s.getAttribute("formaction")||g.getAttribute("action");if(c=v?ca(v,r):null,u=s.getAttribute("formmethod")||g.getAttribute("method")||Ei,d=mc(s.getAttribute("formenctype"))||mc(g.getAttribute("enctype"))||Ti,f=new FormData(g,s),!qg()){let{name:m,type:x,value:b}=s;if(x==="image"){let _=m?`${m}.`:"";f.append(`${_}x`,"0"),f.append(`${_}y`,"0")}else m&&f.append(m,b)}}else{if(Mi(s))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');u=Ei,c=null,d=Ti,p=s}return f&&d==="text/plain"&&(p=f,f=void 0),{action:c,method:u.toLowerCase(),encType:d,formData:f,body:p}}function Bc(s,r){if(s===!1||s===null||typeof s>"u")throw new Error(r)}async function Xg(s,r){if(s.id in r)return r[s.id];try{let u=await import(s.module);return r[s.id]=u,u}catch(u){return console.error(`Error loading route module \`${s.module}\`, reloading page...`),console.error(u),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Vg(s){return s==null?!1:s.href==null?s.rel==="preload"&&typeof s.imageSrcSet=="string"&&typeof s.imageSizes=="string":typeof s.rel=="string"&&typeof s.href=="string"}async function Qg(s,r,u){let c=await Promise.all(s.map(async d=>{let f=r.routes[d.route.id];if(f){let p=await Xg(f,u);return p.links?p.links():[]}return[]}));return Jg(c.flat(1).filter(Vg).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function $h(s,r,u,c,d,f){let p=(v,m)=>u[m]?v.route.id!==u[m].route.id:!0,g=(v,m)=>{var x;return u[m].pathname!==v.pathname||((x=u[m].route.path)==null?void 0:x.endsWith("*"))&&u[m].params["*"]!==v.params["*"]};return f==="assets"?r.filter((v,m)=>p(v,m)||g(v,m)):f==="data"?r.filter((v,m)=>{var b;let x=c.routes[v.route.id];if(!x||!x.hasLoader)return!1;if(p(v,m)||g(v,m))return!0;if(v.route.shouldRevalidate){let _=v.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((b=u[0])==null?void 0:b.params)||{},nextUrl:new URL(s,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof _=="boolean")return _}return!0}):[]}function Zg(s,r,{includeHydrateFallback:u}={}){return Fg(s.map(c=>{let d=r.routes[c.route.id];if(!d)return[];let f=[d.module];return d.clientActionModule&&(f=f.concat(d.clientActionModule)),d.clientLoaderModule&&(f=f.concat(d.clientLoaderModule)),u&&d.hydrateFallbackModule&&(f=f.concat(d.hydrateFallbackModule)),d.imports&&(f=f.concat(d.imports)),f}).flat(1))}function Fg(s){return[...new Set(s)]}function Kg(s){let r={},u=Object.keys(s).sort();for(let c of u)r[c]=s[c];return r}function Jg(s,r){let u=new Set;return new Set(r),s.reduce((c,d)=>{let f=JSON.stringify(Kg(d));return u.has(f)||(u.add(f),c.push({key:f,link:d})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var $g=new Set([100,101,204,205]);function Wg(s,r){let u=typeof s=="string"?new URL(s,typeof window>"u"?"server://singlefetch/":window.location.origin):s;return u.pathname==="/"?u.pathname="_root.data":r&&ca(u.pathname,r)==="/"?u.pathname=`${r.replace(/\/$/,"")}/_root.data`:u.pathname=`${u.pathname.replace(/\/$/,"")}.data`,u}function Nm(){let s=E.useContext(Zl);return Bc(s,"You must render this element inside a <DataRouterContext.Provider> element"),s}function Pg(){let s=E.useContext(Di);return Bc(s,"You must render this element inside a <DataRouterStateContext.Provider> element"),s}var Lc=E.createContext(void 0);Lc.displayName="FrameworkContext";function Em(){let s=E.useContext(Lc);return Bc(s,"You must render this element inside a <HydratedRouter> element"),s}function Ig(s,r){let u=E.useContext(Lc),[c,d]=E.useState(!1),[f,p]=E.useState(!1),{onFocus:g,onBlur:v,onMouseEnter:m,onMouseLeave:x,onTouchStart:b}=r,_=E.useRef(null);E.useEffect(()=>{if(s==="render"&&p(!0),s==="viewport"){let z=C=>{C.forEach(A=>{p(A.isIntersecting)})},D=new IntersectionObserver(z,{threshold:.5});return _.current&&D.observe(_.current),()=>{D.disconnect()}}},[s]),E.useEffect(()=>{if(c){let z=setTimeout(()=>{p(!0)},100);return()=>{clearTimeout(z)}}},[c]);let U=()=>{d(!0)},w=()=>{d(!1),p(!1)};return u?s!=="intent"?[f,_,{}]:[f,_,{onFocus:Wn(g,U),onBlur:Wn(v,w),onMouseEnter:Wn(m,U),onMouseLeave:Wn(x,w),onTouchStart:Wn(b,U)}]:[!1,_,{}]}function Wn(s,r){return u=>{s&&s(u),u.defaultPrevented||r(u)}}function ev({page:s,...r}){let{router:u}=Nm(),c=E.useMemo(()=>hm(u.routes,s,u.basename),[u.routes,s,u.basename]);return c?E.createElement(av,{page:s,matches:c,...r}):null}function tv(s){let{manifest:r,routeModules:u}=Em(),[c,d]=E.useState([]);return E.useEffect(()=>{let f=!1;return Qg(s,r,u).then(p=>{f||d(p)}),()=>{f=!0}},[s,r,u]),c}function av({page:s,matches:r,...u}){let c=Ba(),{manifest:d,routeModules:f}=Em(),{basename:p}=Nm(),{loaderData:g,matches:v}=Pg(),m=E.useMemo(()=>$h(s,r,v,d,c,"data"),[s,r,v,d,c]),x=E.useMemo(()=>$h(s,r,v,d,c,"assets"),[s,r,v,d,c]),b=E.useMemo(()=>{if(s===c.pathname+c.search+c.hash)return[];let w=new Set,z=!1;if(r.forEach(C=>{var T;let A=d.routes[C.route.id];!A||!A.hasLoader||(!m.some(N=>N.route.id===C.route.id)&&C.route.id in g&&((T=f[C.route.id])!=null&&T.shouldRevalidate)||A.hasClientLoader?z=!0:w.add(C.route.id))}),w.size===0)return[];let D=Wg(s,p);return z&&w.size>0&&D.searchParams.set("_routes",r.filter(C=>w.has(C.route.id)).map(C=>C.route.id).join(",")),[D.pathname+D.search]},[p,g,c,d,m,r,s,f]),_=E.useMemo(()=>Zg(x,d),[x,d]),U=tv(x);return E.createElement(E.Fragment,null,b.map(w=>E.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...u})),_.map(w=>E.createElement("link",{key:w,rel:"modulepreload",href:w,...u})),U.map(({key:w,link:z})=>E.createElement("link",{key:w,...z})))}function lv(...s){return r=>{s.forEach(u=>{typeof u=="function"?u(r):u!=null&&(u.current=r)})}}var Tm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Tm&&(window.__reactRouterVersion="7.6.0")}catch{}function nv({basename:s,children:r,window:u}){let c=E.useRef();c.current==null&&(c.current=V0({window:u,v5Compat:!0}));let d=c.current,[f,p]=E.useState({action:d.action,location:d.location}),g=E.useCallback(v=>{E.startTransition(()=>p(v))},[p]);return E.useLayoutEffect(()=>d.listen(g),[d,g]),E.createElement(Mg,{basename:s,children:r,location:f.location,navigationType:f.action,navigator:d})}var _m=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,wm=E.forwardRef(function({onClick:r,discover:u="render",prefetch:c="none",relative:d,reloadDocument:f,replace:p,state:g,target:v,to:m,preventScrollReset:x,viewTransition:b,..._},U){let{basename:w}=E.useContext(Bt),z=typeof m=="string"&&_m.test(m),D,C=!1;if(typeof m=="string"&&z&&(D=m,Tm))try{let ee=new URL(window.location.href),Q=m.startsWith("//")?new URL(ee.protocol+m):new URL(m),$=ca(Q.pathname,w);Q.origin===ee.origin&&$!=null?m=$+Q.search+Q.hash:C=!0}catch{Ut(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let A=gg(m,{relative:d}),[T,N,R]=Ig(c,_),W=rv(m,{replace:p,state:g,target:v,preventScrollReset:x,relative:d,viewTransition:b});function P(ee){r&&r(ee),ee.defaultPrevented||W(ee)}let F=E.createElement("a",{..._,...R,href:D||A,onClick:C||f?r:P,ref:lv(U,N),target:v,"data-discover":!z&&u==="render"?"true":void 0});return T&&!z?E.createElement(E.Fragment,null,F,E.createElement(ev,{page:A})):F});wm.displayName="Link";var Cm=E.forwardRef(function({"aria-current":r="page",caseSensitive:u=!1,className:c="",end:d=!1,style:f,to:p,viewTransition:g,children:v,...m},x){let b=ls(p,{relative:m.relative}),_=Ba(),U=E.useContext(Di),{navigator:w,basename:z}=E.useContext(Bt),D=U!=null&&fv(b)&&g===!0,C=w.encodeLocation?w.encodeLocation(b).pathname:b.pathname,A=_.pathname,T=U&&U.navigation&&U.navigation.location?U.navigation.location.pathname:null;u||(A=A.toLowerCase(),T=T?T.toLowerCase():null,C=C.toLowerCase()),T&&z&&(T=ca(T,z)||T);const N=C!=="/"&&C.endsWith("/")?C.length-1:C.length;let R=A===C||!d&&A.startsWith(C)&&A.charAt(N)==="/",W=T!=null&&(T===C||!d&&T.startsWith(C)&&T.charAt(C.length)==="/"),P={isActive:R,isPending:W,isTransitioning:D},F=R?r:void 0,ee;typeof c=="function"?ee=c(P):ee=[c,R?"active":null,W?"pending":null,D?"transitioning":null].filter(Boolean).join(" ");let Q=typeof f=="function"?f(P):f;return E.createElement(wm,{...m,"aria-current":F,className:ee,ref:x,style:Q,to:p,viewTransition:g},typeof v=="function"?v(P):v)});Cm.displayName="NavLink";var sv=E.forwardRef(({discover:s="render",fetcherKey:r,navigate:u,reloadDocument:c,replace:d,state:f,method:p=Ei,action:g,onSubmit:v,relative:m,preventScrollReset:x,viewTransition:b,..._},U)=>{let w=uv(),z=dv(g,{relative:m}),D=p.toLowerCase()==="get"?"get":"post",C=typeof g=="string"&&_m.test(g),A=T=>{if(v&&v(T),T.defaultPrevented)return;T.preventDefault();let N=T.nativeEvent.submitter,R=(N==null?void 0:N.getAttribute("formmethod"))||p;w(N||T.currentTarget,{fetcherKey:r,method:R,navigate:u,replace:d,state:f,relative:m,preventScrollReset:x,viewTransition:b})};return E.createElement("form",{ref:U,method:D,action:z,onSubmit:c?v:A,..._,"data-discover":!C&&s==="render"?"true":void 0})});sv.displayName="Form";function iv(s){return`${s} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Am(s){let r=E.useContext(Zl);return Be(r,iv(s)),r}function rv(s,{target:r,replace:u,state:c,preventScrollReset:d,relative:f,viewTransition:p}={}){let g=bm(),v=Ba(),m=ls(s,{relative:f});return E.useCallback(x=>{if(Hg(x,r)){x.preventDefault();let b=u!==void 0?u:In(v)===In(m);g(s,{replace:b,state:c,preventScrollReset:d,relative:f,viewTransition:p})}},[v,g,m,u,c,r,s,d,f,p])}var ov=0,cv=()=>`__${String(++ov)}__`;function uv(){let{router:s}=Am("useSubmit"),{basename:r}=E.useContext(Bt),u=Cg();return E.useCallback(async(c,d={})=>{let{action:f,method:p,encType:g,formData:v,body:m}=Gg(c,r);if(d.navigate===!1){let x=d.fetcherKey||cv();await s.fetch(x,u,d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:m,formMethod:d.method||p,formEncType:d.encType||g,flushSync:d.flushSync})}else await s.navigate(d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:m,formMethod:d.method||p,formEncType:d.encType||g,replace:d.replace,state:d.state,fromRouteId:u,flushSync:d.flushSync,viewTransition:d.viewTransition})},[s,r,u])}function dv(s,{relative:r}={}){let{basename:u}=E.useContext(Bt),c=E.useContext(Qt);Be(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),f={...ls(s||".",{relative:r})},p=Ba();if(s==null){f.search=p.search;let g=new URLSearchParams(f.search),v=g.getAll("index");if(v.some(x=>x==="")){g.delete("index"),v.filter(b=>b).forEach(b=>g.append("index",b));let x=g.toString();f.search=x?`?${x}`:""}}return(!s||s===".")&&d.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),u!=="/"&&(f.pathname=f.pathname==="/"?u:ra([u,f.pathname])),In(f)}function fv(s,r={}){let u=E.useContext(gm);Be(u!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=Am("useViewTransitionState"),d=ls(s,{relative:r.relative});if(!u.isTransitioning)return!1;let f=ca(u.currentLocation.pathname,c)||u.currentLocation.pathname,p=ca(u.nextLocation.pathname,c)||u.nextLocation.pathname;return Ai(d.pathname,p)!=null||Ai(d.pathname,f)!=null}[...$g];function Rm(s){var r,u,c="";if(typeof s=="string"||typeof s=="number")c+=s;else if(typeof s=="object")if(Array.isArray(s)){var d=s.length;for(r=0;r<d;r++)s[r]&&(u=Rm(s[r]))&&(c&&(c+=" "),c+=u)}else for(u in s)s[u]&&(c&&(c+=" "),c+=u);return c}function al(){for(var s,r,u=0,c="",d=arguments.length;u<d;u++)(s=arguments[u])&&(r=Rm(s))&&(c&&(c+=" "),c+=r);return c}function hv(s){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],u=document.createElement("style");u.type="text/css",r.firstChild?r.insertBefore(u,r.firstChild):r.appendChild(u),u.styleSheet?u.styleSheet.cssText=s:u.appendChild(document.createTextNode(s))}hv(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var ns=s=>typeof s=="number"&&!isNaN(s),nl=s=>typeof s=="string",ua=s=>typeof s=="function",mv=s=>nl(s)||ns(s),bc=s=>nl(s)||ua(s)?s:null,pv=(s,r)=>s===!1||ns(s)&&s>0?s:r,jc=s=>E.isValidElement(s)||nl(s)||ua(s)||ns(s);function yv(s,r,u=300){let{scrollHeight:c,style:d}=s;requestAnimationFrame(()=>{d.minHeight="initial",d.height=c+"px",d.transition=`all ${u}ms`,requestAnimationFrame(()=>{d.height="0",d.padding="0",d.margin="0",setTimeout(r,u)})})}function gv({enter:s,exit:r,appendPosition:u=!1,collapse:c=!0,collapseDuration:d=300}){return function({children:f,position:p,preventExitTransition:g,done:v,nodeRef:m,isIn:x,playToast:b}){let _=u?`${s}--${p}`:s,U=u?`${r}--${p}`:r,w=E.useRef(0);return E.useLayoutEffect(()=>{let z=m.current,D=_.split(" "),C=A=>{A.target===m.current&&(b(),z.removeEventListener("animationend",C),z.removeEventListener("animationcancel",C),w.current===0&&A.type!=="animationcancel"&&z.classList.remove(...D))};z.classList.add(...D),z.addEventListener("animationend",C),z.addEventListener("animationcancel",C)},[]),E.useEffect(()=>{let z=m.current,D=()=>{z.removeEventListener("animationend",D),c?yv(z,v,d):v()};x||(g?D():(w.current=1,z.className+=` ${U}`,z.addEventListener("animationend",D)))},[x]),Me.createElement(Me.Fragment,null,f)}}function Wh(s,r){return{content:Om(s.content,s.props),containerId:s.props.containerId,id:s.props.toastId,theme:s.props.theme,type:s.props.type,data:s.props.data||{},isLoading:s.props.isLoading,icon:s.props.icon,reason:s.removalReason,status:r}}function Om(s,r,u=!1){return E.isValidElement(s)&&!nl(s.type)?E.cloneElement(s,{closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):ua(s)?s({closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):s}function vv({closeToast:s,theme:r,ariaLabel:u="close"}){return Me.createElement("button",{className:`Toastify__close-button Toastify__close-button--${r}`,type:"button",onClick:c=>{c.stopPropagation(),s(!0)},"aria-label":u},Me.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Me.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function xv({delay:s,isRunning:r,closeToast:u,type:c="default",hide:d,className:f,controlledProgress:p,progress:g,rtl:v,isIn:m,theme:x}){let b=d||p&&g===0,_={animationDuration:`${s}ms`,animationPlayState:r?"running":"paused"};p&&(_.transform=`scaleX(${g})`);let U=al("Toastify__progress-bar",p?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${x}`,`Toastify__progress-bar--${c}`,{"Toastify__progress-bar--rtl":v}),w=ua(f)?f({rtl:v,type:c,defaultClassName:U}):al(U,f),z={[p&&g>=1?"onTransitionEnd":"onAnimationEnd"]:p&&g<1?null:()=>{m&&u()}};return Me.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":b},Me.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${x} Toastify__progress-bar--${c}`}),Me.createElement("div",{role:"progressbar","aria-hidden":b?"true":"false","aria-label":"notification timer",className:w,style:_,...z}))}var bv=1,Dm=()=>`${bv++}`;function jv(s,r,u){let c=1,d=0,f=[],p=[],g=r,v=new Map,m=new Set,x=A=>(m.add(A),()=>m.delete(A)),b=()=>{p=Array.from(v.values()),m.forEach(A=>A())},_=({containerId:A,toastId:T,updateId:N})=>{let R=A?A!==s:s!==1,W=v.has(T)&&N==null;return R||W},U=(A,T)=>{v.forEach(N=>{var R;(T==null||T===N.props.toastId)&&((R=N.toggle)==null||R.call(N,A))})},w=A=>{var T,N;(N=(T=A.props)==null?void 0:T.onClose)==null||N.call(T,A.removalReason),A.isActive=!1},z=A=>{if(A==null)v.forEach(w);else{let T=v.get(A);T&&w(T)}b()},D=()=>{d-=f.length,f=[]},C=A=>{var T,N;let{toastId:R,updateId:W}=A.props,P=W==null;A.staleId&&v.delete(A.staleId),A.isActive=!0,v.set(R,A),b(),u(Wh(A,P?"added":"updated")),P&&((N=(T=A.props).onOpen)==null||N.call(T))};return{id:s,props:g,observe:x,toggle:U,removeToast:z,toasts:v,clearQueue:D,buildToast:(A,T)=>{if(_(T))return;let{toastId:N,updateId:R,data:W,staleId:P,delay:F}=T,ee=R==null;ee&&d++;let Q={...g,style:g.toastStyle,key:c++,...Object.fromEntries(Object.entries(T).filter(([ne,ce])=>ce!=null)),toastId:N,updateId:R,data:W,isIn:!1,className:bc(T.className||g.toastClassName),progressClassName:bc(T.progressClassName||g.progressClassName),autoClose:T.isLoading?!1:pv(T.autoClose,g.autoClose),closeToast(ne){v.get(N).removalReason=ne,z(N)},deleteToast(){let ne=v.get(N);if(ne!=null){if(u(Wh(ne,"removed")),v.delete(N),d--,d<0&&(d=0),f.length>0){C(f.shift());return}b()}}};Q.closeButton=g.closeButton,T.closeButton===!1||jc(T.closeButton)?Q.closeButton=T.closeButton:T.closeButton===!0&&(Q.closeButton=jc(g.closeButton)?g.closeButton:!0);let $={content:A,props:Q,staleId:P};g.limit&&g.limit>0&&d>g.limit&&ee?f.push($):ns(F)?setTimeout(()=>{C($)},F):C($)},setProps(A){g=A},setToggle:(A,T)=>{let N=v.get(A);N&&(N.toggle=T)},isToastActive:A=>{var T;return(T=v.get(A))==null?void 0:T.isActive},getSnapshot:()=>p}}var nt=new Map,es=[],Sc=new Set,Sv=s=>Sc.forEach(r=>r(s)),Mm=()=>nt.size>0;function Nv(){es.forEach(s=>Um(s.content,s.options)),es=[]}var Ev=(s,{containerId:r})=>{var u;return(u=nt.get(r||1))==null?void 0:u.toasts.get(s)};function zm(s,r){var u;if(r)return!!((u=nt.get(r))!=null&&u.isToastActive(s));let c=!1;return nt.forEach(d=>{d.isToastActive(s)&&(c=!0)}),c}function Tv(s){if(!Mm()){es=es.filter(r=>s!=null&&r.options.toastId!==s);return}if(s==null||mv(s))nt.forEach(r=>{r.removeToast(s)});else if(s&&("containerId"in s||"id"in s)){let r=nt.get(s.containerId);r?r.removeToast(s.id):nt.forEach(u=>{u.removeToast(s.id)})}}var _v=(s={})=>{nt.forEach(r=>{r.props.limit&&(!s.containerId||r.id===s.containerId)&&r.clearQueue()})};function Um(s,r){jc(s)&&(Mm()||es.push({content:s,options:r}),nt.forEach(u=>{u.buildToast(s,r)}))}function wv(s){var r;(r=nt.get(s.containerId||1))==null||r.setToggle(s.id,s.fn)}function Bm(s,r){nt.forEach(u=>{(r==null||!(r!=null&&r.containerId)||(r==null?void 0:r.containerId)===u.id)&&u.toggle(s,r==null?void 0:r.id)})}function Cv(s){let r=s.containerId||1;return{subscribe(u){let c=jv(r,s,Sv);nt.set(r,c);let d=c.observe(u);return Nv(),()=>{d(),nt.delete(r)}},setProps(u){var c;(c=nt.get(r))==null||c.setProps(u)},getSnapshot(){var u;return(u=nt.get(r))==null?void 0:u.getSnapshot()}}}function Av(s){return Sc.add(s),()=>{Sc.delete(s)}}function Rv(s){return s&&(nl(s.toastId)||ns(s.toastId))?s.toastId:Dm()}function ss(s,r){return Um(s,r),r.toastId}function zi(s,r){return{...r,type:r&&r.type||s,toastId:Rv(r)}}function Ui(s){return(r,u)=>ss(r,zi(s,u))}function Y(s,r){return ss(s,zi("default",r))}Y.loading=(s,r)=>ss(s,zi("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...r}));function Ov(s,{pending:r,error:u,success:c},d){let f;r&&(f=nl(r)?Y.loading(r,d):Y.loading(r.render,{...d,...r}));let p={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},g=(m,x,b)=>{if(x==null){Y.dismiss(f);return}let _={type:m,...p,...d,data:b},U=nl(x)?{render:x}:x;return f?Y.update(f,{..._,...U}):Y(U.render,{..._,...U}),b},v=ua(s)?s():s;return v.then(m=>g("success",c,m)).catch(m=>g("error",u,m)),v}Y.promise=Ov;Y.success=Ui("success");Y.info=Ui("info");Y.error=Ui("error");Y.warning=Ui("warning");Y.warn=Y.warning;Y.dark=(s,r)=>ss(s,zi("default",{theme:"dark",...r}));function Dv(s){Tv(s)}Y.dismiss=Dv;Y.clearWaitingQueue=_v;Y.isActive=zm;Y.update=(s,r={})=>{let u=Ev(s,r);if(u){let{props:c,content:d}=u,f={delay:100,...c,...r,toastId:r.toastId||s,updateId:Dm()};f.toastId!==s&&(f.staleId=s);let p=f.render||d;delete f.render,ss(p,f)}};Y.done=s=>{Y.update(s,{progress:1})};Y.onChange=Av;Y.play=s=>Bm(!0,s);Y.pause=s=>Bm(!1,s);function Mv(s){var r;let{subscribe:u,getSnapshot:c,setProps:d}=E.useRef(Cv(s)).current;d(s);let f=(r=E.useSyncExternalStore(u,c,c))==null?void 0:r.slice();function p(g){if(!f)return[];let v=new Map;return s.newestOnTop&&f.reverse(),f.forEach(m=>{let{position:x}=m.props;v.has(x)||v.set(x,[]),v.get(x).push(m)}),Array.from(v,m=>g(m[0],m[1]))}return{getToastToRender:p,isToastActive:zm,count:f==null?void 0:f.length}}function zv(s){let[r,u]=E.useState(!1),[c,d]=E.useState(!1),f=E.useRef(null),p=E.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:g,pauseOnHover:v,closeToast:m,onClick:x,closeOnClick:b}=s;wv({id:s.toastId,containerId:s.containerId,fn:u}),E.useEffect(()=>{if(s.pauseOnFocusLoss)return _(),()=>{U()}},[s.pauseOnFocusLoss]);function _(){document.hasFocus()||C(),window.addEventListener("focus",D),window.addEventListener("blur",C)}function U(){window.removeEventListener("focus",D),window.removeEventListener("blur",C)}function w(P){if(s.draggable===!0||s.draggable===P.pointerType){A();let F=f.current;p.canCloseOnClick=!0,p.canDrag=!0,F.style.transition="none",s.draggableDirection==="x"?(p.start=P.clientX,p.removalDistance=F.offsetWidth*(s.draggablePercent/100)):(p.start=P.clientY,p.removalDistance=F.offsetHeight*(s.draggablePercent===80?s.draggablePercent*1.5:s.draggablePercent)/100)}}function z(P){let{top:F,bottom:ee,left:Q,right:$}=f.current.getBoundingClientRect();P.nativeEvent.type!=="touchend"&&s.pauseOnHover&&P.clientX>=Q&&P.clientX<=$&&P.clientY>=F&&P.clientY<=ee?C():D()}function D(){u(!0)}function C(){u(!1)}function A(){p.didMove=!1,document.addEventListener("pointermove",N),document.addEventListener("pointerup",R)}function T(){document.removeEventListener("pointermove",N),document.removeEventListener("pointerup",R)}function N(P){let F=f.current;if(p.canDrag&&F){p.didMove=!0,r&&C(),s.draggableDirection==="x"?p.delta=P.clientX-p.start:p.delta=P.clientY-p.start,p.start!==P.clientX&&(p.canCloseOnClick=!1);let ee=s.draggableDirection==="x"?`${p.delta}px, var(--y)`:`0, calc(${p.delta}px + var(--y))`;F.style.transform=`translate3d(${ee},0)`,F.style.opacity=`${1-Math.abs(p.delta/p.removalDistance)}`}}function R(){T();let P=f.current;if(p.canDrag&&p.didMove&&P){if(p.canDrag=!1,Math.abs(p.delta)>p.removalDistance){d(!0),s.closeToast(!0),s.collapseAll();return}P.style.transition="transform 0.2s, opacity 0.2s",P.style.removeProperty("transform"),P.style.removeProperty("opacity")}}let W={onPointerDown:w,onPointerUp:z};return g&&v&&(W.onMouseEnter=C,s.stacked||(W.onMouseLeave=D)),b&&(W.onClick=P=>{x&&x(P),p.canCloseOnClick&&m(!0)}),{playToast:D,pauseToast:C,isRunning:r,preventExitTransition:c,toastRef:f,eventHandlers:W}}var Uv=typeof window<"u"?E.useLayoutEffect:E.useEffect,Bi=({theme:s,type:r,isLoading:u,...c})=>Me.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:s==="colored"?"currentColor":`var(--toastify-icon-color-${r})`,...c});function Bv(s){return Me.createElement(Bi,{...s},Me.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function Lv(s){return Me.createElement(Bi,{...s},Me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function kv(s){return Me.createElement(Bi,{...s},Me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function Hv(s){return Me.createElement(Bi,{...s},Me.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function qv(){return Me.createElement("div",{className:"Toastify__spinner"})}var Nc={info:Lv,warning:Bv,success:kv,error:Hv,spinner:qv},Yv=s=>s in Nc;function Gv({theme:s,type:r,isLoading:u,icon:c}){let d=null,f={theme:s,type:r};return c===!1||(ua(c)?d=c({...f,isLoading:u}):E.isValidElement(c)?d=E.cloneElement(c,f):u?d=Nc.spinner():Yv(r)&&(d=Nc[r](f))),d}var Xv=s=>{let{isRunning:r,preventExitTransition:u,toastRef:c,eventHandlers:d,playToast:f}=zv(s),{closeButton:p,children:g,autoClose:v,onClick:m,type:x,hideProgressBar:b,closeToast:_,transition:U,position:w,className:z,style:D,progressClassName:C,updateId:A,role:T,progress:N,rtl:R,toastId:W,deleteToast:P,isIn:F,isLoading:ee,closeOnClick:Q,theme:$,ariaLabel:ne}=s,ce=al("Toastify__toast",`Toastify__toast-theme--${$}`,`Toastify__toast--${x}`,{"Toastify__toast--rtl":R},{"Toastify__toast--close-on-click":Q}),re=ua(z)?z({rtl:R,position:w,type:x,defaultClassName:ce}):al(ce,z),ye=Gv(s),X=!!N||!v,te={closeToast:_,type:x,theme:$},q=null;return p===!1||(ua(p)?q=p(te):E.isValidElement(p)?q=E.cloneElement(p,te):q=vv(te)),Me.createElement(U,{isIn:F,done:P,position:w,preventExitTransition:u,nodeRef:c,playToast:f},Me.createElement("div",{id:W,tabIndex:0,onClick:m,"data-in":F,className:re,...d,style:D,ref:c,...F&&{role:T,"aria-label":ne}},ye!=null&&Me.createElement("div",{className:al("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!ee})},ye),Om(g,s,!r),q,!s.customProgressBar&&Me.createElement(xv,{...A&&!X?{key:`p-${A}`}:{},rtl:R,theme:$,delay:v,isRunning:r,isIn:F,closeToast:_,hide:b,type:x,className:C,controlledProgress:X,progress:N||0})))},Vv=(s,r=!1)=>({enter:`Toastify--animate Toastify__${s}-enter`,exit:`Toastify--animate Toastify__${s}-exit`,appendPosition:r}),Qv=gv(Vv("bounce",!0)),Zv={position:"top-right",transition:Qv,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:s=>s.altKey&&s.code==="KeyT"};function Fv(s){let r={...Zv,...s},u=s.stacked,[c,d]=E.useState(!0),f=E.useRef(null),{getToastToRender:p,isToastActive:g,count:v}=Mv(r),{className:m,style:x,rtl:b,containerId:_,hotKeys:U}=r;function w(D){let C=al("Toastify__toast-container",`Toastify__toast-container--${D}`,{"Toastify__toast-container--rtl":b});return ua(m)?m({position:D,rtl:b,defaultClassName:C}):al(C,bc(m))}function z(){u&&(d(!0),Y.play())}return Uv(()=>{var D;if(u){let C=f.current.querySelectorAll('[data-in="true"]'),A=12,T=(D=r.position)==null?void 0:D.includes("top"),N=0,R=0;Array.from(C).reverse().forEach((W,P)=>{let F=W;F.classList.add("Toastify__toast--stacked"),P>0&&(F.dataset.collapsed=`${c}`),F.dataset.pos||(F.dataset.pos=T?"top":"bot");let ee=N*(c?.2:1)+(c?0:A*P);F.style.setProperty("--y",`${T?ee:ee*-1}px`),F.style.setProperty("--g",`${A}`),F.style.setProperty("--s",`${1-(c?R:0)}`),N+=F.offsetHeight,R+=.025})}},[c,v,u]),E.useEffect(()=>{function D(C){var A;let T=f.current;U(C)&&((A=T.querySelector('[tabIndex="0"]'))==null||A.focus(),d(!1),Y.pause()),C.key==="Escape"&&(document.activeElement===T||T!=null&&T.contains(document.activeElement))&&(d(!0),Y.play())}return document.addEventListener("keydown",D),()=>{document.removeEventListener("keydown",D)}},[U]),Me.createElement("section",{ref:f,className:"Toastify",id:_,onMouseEnter:()=>{u&&(d(!1),Y.pause())},onMouseLeave:z,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":r["aria-label"]},p((D,C)=>{let A=C.length?{...x}:{...x,pointerEvents:"none"};return Me.createElement("div",{tabIndex:-1,className:w(D),"data-stacked":u,style:A,key:`c-${D}`},C.map(({content:T,props:N})=>Me.createElement(Xv,{...N,stacked:u,collapseAll:z,isIn:g(N.toastId,N.containerId),key:`t-${N.key}`},T)))}))}function Lm(s,r){return function(){return s.apply(r,arguments)}}const{toString:Kv}=Object.prototype,{getPrototypeOf:kc}=Object,{iterator:Li,toStringTag:km}=Symbol,ki=(s=>r=>{const u=Kv.call(r);return s[u]||(s[u]=u.slice(8,-1).toLowerCase())})(Object.create(null)),Lt=s=>(s=s.toLowerCase(),r=>ki(r)===s),Hi=s=>r=>typeof r===s,{isArray:Kl}=Array,ts=Hi("undefined");function Jv(s){return s!==null&&!ts(s)&&s.constructor!==null&&!ts(s.constructor)&&ut(s.constructor.isBuffer)&&s.constructor.isBuffer(s)}const Hm=Lt("ArrayBuffer");function $v(s){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(s):r=s&&s.buffer&&Hm(s.buffer),r}const Wv=Hi("string"),ut=Hi("function"),qm=Hi("number"),qi=s=>s!==null&&typeof s=="object",Pv=s=>s===!0||s===!1,_i=s=>{if(ki(s)!=="object")return!1;const r=kc(s);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(km in s)&&!(Li in s)},Iv=Lt("Date"),e1=Lt("File"),t1=Lt("Blob"),a1=Lt("FileList"),l1=s=>qi(s)&&ut(s.pipe),n1=s=>{let r;return s&&(typeof FormData=="function"&&s instanceof FormData||ut(s.append)&&((r=ki(s))==="formdata"||r==="object"&&ut(s.toString)&&s.toString()==="[object FormData]"))},s1=Lt("URLSearchParams"),[i1,r1,o1,c1]=["ReadableStream","Request","Response","Headers"].map(Lt),u1=s=>s.trim?s.trim():s.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function is(s,r,{allOwnKeys:u=!1}={}){if(s===null||typeof s>"u")return;let c,d;if(typeof s!="object"&&(s=[s]),Kl(s))for(c=0,d=s.length;c<d;c++)r.call(null,s[c],c,s);else{const f=u?Object.getOwnPropertyNames(s):Object.keys(s),p=f.length;let g;for(c=0;c<p;c++)g=f[c],r.call(null,s[g],g,s)}}function Ym(s,r){r=r.toLowerCase();const u=Object.keys(s);let c=u.length,d;for(;c-- >0;)if(d=u[c],r===d.toLowerCase())return d;return null}const tl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Gm=s=>!ts(s)&&s!==tl;function Ec(){const{caseless:s}=Gm(this)&&this||{},r={},u=(c,d)=>{const f=s&&Ym(r,d)||d;_i(r[f])&&_i(c)?r[f]=Ec(r[f],c):_i(c)?r[f]=Ec({},c):Kl(c)?r[f]=c.slice():r[f]=c};for(let c=0,d=arguments.length;c<d;c++)arguments[c]&&is(arguments[c],u);return r}const d1=(s,r,u,{allOwnKeys:c}={})=>(is(r,(d,f)=>{u&&ut(d)?s[f]=Lm(d,u):s[f]=d},{allOwnKeys:c}),s),f1=s=>(s.charCodeAt(0)===65279&&(s=s.slice(1)),s),h1=(s,r,u,c)=>{s.prototype=Object.create(r.prototype,c),s.prototype.constructor=s,Object.defineProperty(s,"super",{value:r.prototype}),u&&Object.assign(s.prototype,u)},m1=(s,r,u,c)=>{let d,f,p;const g={};if(r=r||{},s==null)return r;do{for(d=Object.getOwnPropertyNames(s),f=d.length;f-- >0;)p=d[f],(!c||c(p,s,r))&&!g[p]&&(r[p]=s[p],g[p]=!0);s=u!==!1&&kc(s)}while(s&&(!u||u(s,r))&&s!==Object.prototype);return r},p1=(s,r,u)=>{s=String(s),(u===void 0||u>s.length)&&(u=s.length),u-=r.length;const c=s.indexOf(r,u);return c!==-1&&c===u},y1=s=>{if(!s)return null;if(Kl(s))return s;let r=s.length;if(!qm(r))return null;const u=new Array(r);for(;r-- >0;)u[r]=s[r];return u},g1=(s=>r=>s&&r instanceof s)(typeof Uint8Array<"u"&&kc(Uint8Array)),v1=(s,r)=>{const c=(s&&s[Li]).call(s);let d;for(;(d=c.next())&&!d.done;){const f=d.value;r.call(s,f[0],f[1])}},x1=(s,r)=>{let u;const c=[];for(;(u=s.exec(r))!==null;)c.push(u);return c},b1=Lt("HTMLFormElement"),j1=s=>s.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(u,c,d){return c.toUpperCase()+d}),Ph=(({hasOwnProperty:s})=>(r,u)=>s.call(r,u))(Object.prototype),S1=Lt("RegExp"),Xm=(s,r)=>{const u=Object.getOwnPropertyDescriptors(s),c={};is(u,(d,f)=>{let p;(p=r(d,f,s))!==!1&&(c[f]=p||d)}),Object.defineProperties(s,c)},N1=s=>{Xm(s,(r,u)=>{if(ut(s)&&["arguments","caller","callee"].indexOf(u)!==-1)return!1;const c=s[u];if(ut(c)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+u+"'")})}})},E1=(s,r)=>{const u={},c=d=>{d.forEach(f=>{u[f]=!0})};return Kl(s)?c(s):c(String(s).split(r)),u},T1=()=>{},_1=(s,r)=>s!=null&&Number.isFinite(s=+s)?s:r;function w1(s){return!!(s&&ut(s.append)&&s[km]==="FormData"&&s[Li])}const C1=s=>{const r=new Array(10),u=(c,d)=>{if(qi(c)){if(r.indexOf(c)>=0)return;if(!("toJSON"in c)){r[d]=c;const f=Kl(c)?[]:{};return is(c,(p,g)=>{const v=u(p,d+1);!ts(v)&&(f[g]=v)}),r[d]=void 0,f}}return c};return u(s,0)},A1=Lt("AsyncFunction"),R1=s=>s&&(qi(s)||ut(s))&&ut(s.then)&&ut(s.catch),Vm=((s,r)=>s?setImmediate:r?((u,c)=>(tl.addEventListener("message",({source:d,data:f})=>{d===tl&&f===u&&c.length&&c.shift()()},!1),d=>{c.push(d),tl.postMessage(u,"*")}))(`axios@${Math.random()}`,[]):u=>setTimeout(u))(typeof setImmediate=="function",ut(tl.postMessage)),O1=typeof queueMicrotask<"u"?queueMicrotask.bind(tl):typeof process<"u"&&process.nextTick||Vm,D1=s=>s!=null&&ut(s[Li]),G={isArray:Kl,isArrayBuffer:Hm,isBuffer:Jv,isFormData:n1,isArrayBufferView:$v,isString:Wv,isNumber:qm,isBoolean:Pv,isObject:qi,isPlainObject:_i,isReadableStream:i1,isRequest:r1,isResponse:o1,isHeaders:c1,isUndefined:ts,isDate:Iv,isFile:e1,isBlob:t1,isRegExp:S1,isFunction:ut,isStream:l1,isURLSearchParams:s1,isTypedArray:g1,isFileList:a1,forEach:is,merge:Ec,extend:d1,trim:u1,stripBOM:f1,inherits:h1,toFlatObject:m1,kindOf:ki,kindOfTest:Lt,endsWith:p1,toArray:y1,forEachEntry:v1,matchAll:x1,isHTMLForm:b1,hasOwnProperty:Ph,hasOwnProp:Ph,reduceDescriptors:Xm,freezeMethods:N1,toObjectSet:E1,toCamelCase:j1,noop:T1,toFiniteNumber:_1,findKey:Ym,global:tl,isContextDefined:Gm,isSpecCompliantForm:w1,toJSONObject:C1,isAsyncFn:A1,isThenable:R1,setImmediate:Vm,asap:O1,isIterable:D1};function pe(s,r,u,c,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=s,this.name="AxiosError",r&&(this.code=r),u&&(this.config=u),c&&(this.request=c),d&&(this.response=d,this.status=d.status?d.status:null)}G.inherits(pe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:G.toJSONObject(this.config),code:this.code,status:this.status}}});const Qm=pe.prototype,Zm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(s=>{Zm[s]={value:s}});Object.defineProperties(pe,Zm);Object.defineProperty(Qm,"isAxiosError",{value:!0});pe.from=(s,r,u,c,d,f)=>{const p=Object.create(Qm);return G.toFlatObject(s,p,function(v){return v!==Error.prototype},g=>g!=="isAxiosError"),pe.call(p,s.message,r,u,c,d),p.cause=s,p.name=s.name,f&&Object.assign(p,f),p};const M1=null;function Tc(s){return G.isPlainObject(s)||G.isArray(s)}function Fm(s){return G.endsWith(s,"[]")?s.slice(0,-2):s}function Ih(s,r,u){return s?s.concat(r).map(function(d,f){return d=Fm(d),!u&&f?"["+d+"]":d}).join(u?".":""):r}function z1(s){return G.isArray(s)&&!s.some(Tc)}const U1=G.toFlatObject(G,{},null,function(r){return/^is[A-Z]/.test(r)});function Yi(s,r,u){if(!G.isObject(s))throw new TypeError("target must be an object");r=r||new FormData,u=G.toFlatObject(u,{metaTokens:!0,dots:!1,indexes:!1},!1,function(z,D){return!G.isUndefined(D[z])});const c=u.metaTokens,d=u.visitor||x,f=u.dots,p=u.indexes,v=(u.Blob||typeof Blob<"u"&&Blob)&&G.isSpecCompliantForm(r);if(!G.isFunction(d))throw new TypeError("visitor must be a function");function m(w){if(w===null)return"";if(G.isDate(w))return w.toISOString();if(!v&&G.isBlob(w))throw new pe("Blob is not supported. Use a Buffer instead.");return G.isArrayBuffer(w)||G.isTypedArray(w)?v&&typeof Blob=="function"?new Blob([w]):Buffer.from(w):w}function x(w,z,D){let C=w;if(w&&!D&&typeof w=="object"){if(G.endsWith(z,"{}"))z=c?z:z.slice(0,-2),w=JSON.stringify(w);else if(G.isArray(w)&&z1(w)||(G.isFileList(w)||G.endsWith(z,"[]"))&&(C=G.toArray(w)))return z=Fm(z),C.forEach(function(T,N){!(G.isUndefined(T)||T===null)&&r.append(p===!0?Ih([z],N,f):p===null?z:z+"[]",m(T))}),!1}return Tc(w)?!0:(r.append(Ih(D,z,f),m(w)),!1)}const b=[],_=Object.assign(U1,{defaultVisitor:x,convertValue:m,isVisitable:Tc});function U(w,z){if(!G.isUndefined(w)){if(b.indexOf(w)!==-1)throw Error("Circular reference detected in "+z.join("."));b.push(w),G.forEach(w,function(C,A){(!(G.isUndefined(C)||C===null)&&d.call(r,C,G.isString(A)?A.trim():A,z,_))===!0&&U(C,z?z.concat(A):[A])}),b.pop()}}if(!G.isObject(s))throw new TypeError("data must be an object");return U(s),r}function em(s){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(s).replace(/[!'()~]|%20|%00/g,function(c){return r[c]})}function Hc(s,r){this._pairs=[],s&&Yi(s,this,r)}const Km=Hc.prototype;Km.append=function(r,u){this._pairs.push([r,u])};Km.toString=function(r){const u=r?function(c){return r.call(this,c,em)}:em;return this._pairs.map(function(d){return u(d[0])+"="+u(d[1])},"").join("&")};function B1(s){return encodeURIComponent(s).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Jm(s,r,u){if(!r)return s;const c=u&&u.encode||B1;G.isFunction(u)&&(u={serialize:u});const d=u&&u.serialize;let f;if(d?f=d(r,u):f=G.isURLSearchParams(r)?r.toString():new Hc(r,u).toString(c),f){const p=s.indexOf("#");p!==-1&&(s=s.slice(0,p)),s+=(s.indexOf("?")===-1?"?":"&")+f}return s}class tm{constructor(){this.handlers=[]}use(r,u,c){return this.handlers.push({fulfilled:r,rejected:u,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){G.forEach(this.handlers,function(c){c!==null&&r(c)})}}const $m={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},L1=typeof URLSearchParams<"u"?URLSearchParams:Hc,k1=typeof FormData<"u"?FormData:null,H1=typeof Blob<"u"?Blob:null,q1={isBrowser:!0,classes:{URLSearchParams:L1,FormData:k1,Blob:H1},protocols:["http","https","file","blob","url","data"]},qc=typeof window<"u"&&typeof document<"u",_c=typeof navigator=="object"&&navigator||void 0,Y1=qc&&(!_c||["ReactNative","NativeScript","NS"].indexOf(_c.product)<0),G1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",X1=qc&&window.location.href||"http://localhost",V1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:qc,hasStandardBrowserEnv:Y1,hasStandardBrowserWebWorkerEnv:G1,navigator:_c,origin:X1},Symbol.toStringTag,{value:"Module"})),at={...V1,...q1};function Q1(s,r){return Yi(s,new at.classes.URLSearchParams,Object.assign({visitor:function(u,c,d,f){return at.isNode&&G.isBuffer(u)?(this.append(c,u.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},r))}function Z1(s){return G.matchAll(/\w+|\[(\w*)]/g,s).map(r=>r[0]==="[]"?"":r[1]||r[0])}function F1(s){const r={},u=Object.keys(s);let c;const d=u.length;let f;for(c=0;c<d;c++)f=u[c],r[f]=s[f];return r}function Wm(s){function r(u,c,d,f){let p=u[f++];if(p==="__proto__")return!0;const g=Number.isFinite(+p),v=f>=u.length;return p=!p&&G.isArray(d)?d.length:p,v?(G.hasOwnProp(d,p)?d[p]=[d[p],c]:d[p]=c,!g):((!d[p]||!G.isObject(d[p]))&&(d[p]=[]),r(u,c,d[p],f)&&G.isArray(d[p])&&(d[p]=F1(d[p])),!g)}if(G.isFormData(s)&&G.isFunction(s.entries)){const u={};return G.forEachEntry(s,(c,d)=>{r(Z1(c),d,u,0)}),u}return null}function K1(s,r,u){if(G.isString(s))try{return(r||JSON.parse)(s),G.trim(s)}catch(c){if(c.name!=="SyntaxError")throw c}return(u||JSON.stringify)(s)}const rs={transitional:$m,adapter:["xhr","http","fetch"],transformRequest:[function(r,u){const c=u.getContentType()||"",d=c.indexOf("application/json")>-1,f=G.isObject(r);if(f&&G.isHTMLForm(r)&&(r=new FormData(r)),G.isFormData(r))return d?JSON.stringify(Wm(r)):r;if(G.isArrayBuffer(r)||G.isBuffer(r)||G.isStream(r)||G.isFile(r)||G.isBlob(r)||G.isReadableStream(r))return r;if(G.isArrayBufferView(r))return r.buffer;if(G.isURLSearchParams(r))return u.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let g;if(f){if(c.indexOf("application/x-www-form-urlencoded")>-1)return Q1(r,this.formSerializer).toString();if((g=G.isFileList(r))||c.indexOf("multipart/form-data")>-1){const v=this.env&&this.env.FormData;return Yi(g?{"files[]":r}:r,v&&new v,this.formSerializer)}}return f||d?(u.setContentType("application/json",!1),K1(r)):r}],transformResponse:[function(r){const u=this.transitional||rs.transitional,c=u&&u.forcedJSONParsing,d=this.responseType==="json";if(G.isResponse(r)||G.isReadableStream(r))return r;if(r&&G.isString(r)&&(c&&!this.responseType||d)){const p=!(u&&u.silentJSONParsing)&&d;try{return JSON.parse(r)}catch(g){if(p)throw g.name==="SyntaxError"?pe.from(g,pe.ERR_BAD_RESPONSE,this,null,this.response):g}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};G.forEach(["delete","get","head","post","put","patch"],s=>{rs.headers[s]={}});const J1=G.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),$1=s=>{const r={};let u,c,d;return s&&s.split(`
`).forEach(function(p){d=p.indexOf(":"),u=p.substring(0,d).trim().toLowerCase(),c=p.substring(d+1).trim(),!(!u||r[u]&&J1[u])&&(u==="set-cookie"?r[u]?r[u].push(c):r[u]=[c]:r[u]=r[u]?r[u]+", "+c:c)}),r},am=Symbol("internals");function Pn(s){return s&&String(s).trim().toLowerCase()}function wi(s){return s===!1||s==null?s:G.isArray(s)?s.map(wi):String(s)}function W1(s){const r=Object.create(null),u=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=u.exec(s);)r[c[1]]=c[2];return r}const P1=s=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(s.trim());function pc(s,r,u,c,d){if(G.isFunction(c))return c.call(this,r,u);if(d&&(r=u),!!G.isString(r)){if(G.isString(c))return r.indexOf(c)!==-1;if(G.isRegExp(c))return c.test(r)}}function I1(s){return s.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,u,c)=>u.toUpperCase()+c)}function ex(s,r){const u=G.toCamelCase(" "+r);["get","set","has"].forEach(c=>{Object.defineProperty(s,c+u,{value:function(d,f,p){return this[c].call(this,r,d,f,p)},configurable:!0})})}let dt=class{constructor(r){r&&this.set(r)}set(r,u,c){const d=this;function f(g,v,m){const x=Pn(v);if(!x)throw new Error("header name must be a non-empty string");const b=G.findKey(d,x);(!b||d[b]===void 0||m===!0||m===void 0&&d[b]!==!1)&&(d[b||v]=wi(g))}const p=(g,v)=>G.forEach(g,(m,x)=>f(m,x,v));if(G.isPlainObject(r)||r instanceof this.constructor)p(r,u);else if(G.isString(r)&&(r=r.trim())&&!P1(r))p($1(r),u);else if(G.isObject(r)&&G.isIterable(r)){let g={},v,m;for(const x of r){if(!G.isArray(x))throw TypeError("Object iterator must return a key-value pair");g[m=x[0]]=(v=g[m])?G.isArray(v)?[...v,x[1]]:[v,x[1]]:x[1]}p(g,u)}else r!=null&&f(u,r,c);return this}get(r,u){if(r=Pn(r),r){const c=G.findKey(this,r);if(c){const d=this[c];if(!u)return d;if(u===!0)return W1(d);if(G.isFunction(u))return u.call(this,d,c);if(G.isRegExp(u))return u.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,u){if(r=Pn(r),r){const c=G.findKey(this,r);return!!(c&&this[c]!==void 0&&(!u||pc(this,this[c],c,u)))}return!1}delete(r,u){const c=this;let d=!1;function f(p){if(p=Pn(p),p){const g=G.findKey(c,p);g&&(!u||pc(c,c[g],g,u))&&(delete c[g],d=!0)}}return G.isArray(r)?r.forEach(f):f(r),d}clear(r){const u=Object.keys(this);let c=u.length,d=!1;for(;c--;){const f=u[c];(!r||pc(this,this[f],f,r,!0))&&(delete this[f],d=!0)}return d}normalize(r){const u=this,c={};return G.forEach(this,(d,f)=>{const p=G.findKey(c,f);if(p){u[p]=wi(d),delete u[f];return}const g=r?I1(f):String(f).trim();g!==f&&delete u[f],u[g]=wi(d),c[g]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const u=Object.create(null);return G.forEach(this,(c,d)=>{c!=null&&c!==!1&&(u[d]=r&&G.isArray(c)?c.join(", "):c)}),u}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,u])=>r+": "+u).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...u){const c=new this(r);return u.forEach(d=>c.set(d)),c}static accessor(r){const c=(this[am]=this[am]={accessors:{}}).accessors,d=this.prototype;function f(p){const g=Pn(p);c[g]||(ex(d,p),c[g]=!0)}return G.isArray(r)?r.forEach(f):f(r),this}};dt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);G.reduceDescriptors(dt.prototype,({value:s},r)=>{let u=r[0].toUpperCase()+r.slice(1);return{get:()=>s,set(c){this[u]=c}}});G.freezeMethods(dt);function yc(s,r){const u=this||rs,c=r||u,d=dt.from(c.headers);let f=c.data;return G.forEach(s,function(g){f=g.call(u,f,d.normalize(),r?r.status:void 0)}),d.normalize(),f}function Pm(s){return!!(s&&s.__CANCEL__)}function Jl(s,r,u){pe.call(this,s??"canceled",pe.ERR_CANCELED,r,u),this.name="CanceledError"}G.inherits(Jl,pe,{__CANCEL__:!0});function Im(s,r,u){const c=u.config.validateStatus;!u.status||!c||c(u.status)?s(u):r(new pe("Request failed with status code "+u.status,[pe.ERR_BAD_REQUEST,pe.ERR_BAD_RESPONSE][Math.floor(u.status/100)-4],u.config,u.request,u))}function tx(s){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(s);return r&&r[1]||""}function ax(s,r){s=s||10;const u=new Array(s),c=new Array(s);let d=0,f=0,p;return r=r!==void 0?r:1e3,function(v){const m=Date.now(),x=c[f];p||(p=m),u[d]=v,c[d]=m;let b=f,_=0;for(;b!==d;)_+=u[b++],b=b%s;if(d=(d+1)%s,d===f&&(f=(f+1)%s),m-p<r)return;const U=x&&m-x;return U?Math.round(_*1e3/U):void 0}}function lx(s,r){let u=0,c=1e3/r,d,f;const p=(m,x=Date.now())=>{u=x,d=null,f&&(clearTimeout(f),f=null),s.apply(null,m)};return[(...m)=>{const x=Date.now(),b=x-u;b>=c?p(m,x):(d=m,f||(f=setTimeout(()=>{f=null,p(d)},c-b)))},()=>d&&p(d)]}const Ri=(s,r,u=3)=>{let c=0;const d=ax(50,250);return lx(f=>{const p=f.loaded,g=f.lengthComputable?f.total:void 0,v=p-c,m=d(v),x=p<=g;c=p;const b={loaded:p,total:g,progress:g?p/g:void 0,bytes:v,rate:m||void 0,estimated:m&&g&&x?(g-p)/m:void 0,event:f,lengthComputable:g!=null,[r?"download":"upload"]:!0};s(b)},u)},lm=(s,r)=>{const u=s!=null;return[c=>r[0]({lengthComputable:u,total:s,loaded:c}),r[1]]},nm=s=>(...r)=>G.asap(()=>s(...r)),nx=at.hasStandardBrowserEnv?((s,r)=>u=>(u=new URL(u,at.origin),s.protocol===u.protocol&&s.host===u.host&&(r||s.port===u.port)))(new URL(at.origin),at.navigator&&/(msie|trident)/i.test(at.navigator.userAgent)):()=>!0,sx=at.hasStandardBrowserEnv?{write(s,r,u,c,d,f){const p=[s+"="+encodeURIComponent(r)];G.isNumber(u)&&p.push("expires="+new Date(u).toGMTString()),G.isString(c)&&p.push("path="+c),G.isString(d)&&p.push("domain="+d),f===!0&&p.push("secure"),document.cookie=p.join("; ")},read(s){const r=document.cookie.match(new RegExp("(^|;\\s*)("+s+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(s){this.write(s,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ix(s){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(s)}function rx(s,r){return r?s.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):s}function ep(s,r,u){let c=!ix(r);return s&&(c||u==!1)?rx(s,r):r}const sm=s=>s instanceof dt?{...s}:s;function sl(s,r){r=r||{};const u={};function c(m,x,b,_){return G.isPlainObject(m)&&G.isPlainObject(x)?G.merge.call({caseless:_},m,x):G.isPlainObject(x)?G.merge({},x):G.isArray(x)?x.slice():x}function d(m,x,b,_){if(G.isUndefined(x)){if(!G.isUndefined(m))return c(void 0,m,b,_)}else return c(m,x,b,_)}function f(m,x){if(!G.isUndefined(x))return c(void 0,x)}function p(m,x){if(G.isUndefined(x)){if(!G.isUndefined(m))return c(void 0,m)}else return c(void 0,x)}function g(m,x,b){if(b in r)return c(m,x);if(b in s)return c(void 0,m)}const v={url:f,method:f,data:f,baseURL:p,transformRequest:p,transformResponse:p,paramsSerializer:p,timeout:p,timeoutMessage:p,withCredentials:p,withXSRFToken:p,adapter:p,responseType:p,xsrfCookieName:p,xsrfHeaderName:p,onUploadProgress:p,onDownloadProgress:p,decompress:p,maxContentLength:p,maxBodyLength:p,beforeRedirect:p,transport:p,httpAgent:p,httpsAgent:p,cancelToken:p,socketPath:p,responseEncoding:p,validateStatus:g,headers:(m,x,b)=>d(sm(m),sm(x),b,!0)};return G.forEach(Object.keys(Object.assign({},s,r)),function(x){const b=v[x]||d,_=b(s[x],r[x],x);G.isUndefined(_)&&b!==g||(u[x]=_)}),u}const tp=s=>{const r=sl({},s);let{data:u,withXSRFToken:c,xsrfHeaderName:d,xsrfCookieName:f,headers:p,auth:g}=r;r.headers=p=dt.from(p),r.url=Jm(ep(r.baseURL,r.url,r.allowAbsoluteUrls),s.params,s.paramsSerializer),g&&p.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let v;if(G.isFormData(u)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)p.setContentType(void 0);else if((v=p.getContentType())!==!1){const[m,...x]=v?v.split(";").map(b=>b.trim()).filter(Boolean):[];p.setContentType([m||"multipart/form-data",...x].join("; "))}}if(at.hasStandardBrowserEnv&&(c&&G.isFunction(c)&&(c=c(r)),c||c!==!1&&nx(r.url))){const m=d&&f&&sx.read(f);m&&p.set(d,m)}return r},ox=typeof XMLHttpRequest<"u",cx=ox&&function(s){return new Promise(function(u,c){const d=tp(s);let f=d.data;const p=dt.from(d.headers).normalize();let{responseType:g,onUploadProgress:v,onDownloadProgress:m}=d,x,b,_,U,w;function z(){U&&U(),w&&w(),d.cancelToken&&d.cancelToken.unsubscribe(x),d.signal&&d.signal.removeEventListener("abort",x)}let D=new XMLHttpRequest;D.open(d.method.toUpperCase(),d.url,!0),D.timeout=d.timeout;function C(){if(!D)return;const T=dt.from("getAllResponseHeaders"in D&&D.getAllResponseHeaders()),R={data:!g||g==="text"||g==="json"?D.responseText:D.response,status:D.status,statusText:D.statusText,headers:T,config:s,request:D};Im(function(P){u(P),z()},function(P){c(P),z()},R),D=null}"onloadend"in D?D.onloadend=C:D.onreadystatechange=function(){!D||D.readyState!==4||D.status===0&&!(D.responseURL&&D.responseURL.indexOf("file:")===0)||setTimeout(C)},D.onabort=function(){D&&(c(new pe("Request aborted",pe.ECONNABORTED,s,D)),D=null)},D.onerror=function(){c(new pe("Network Error",pe.ERR_NETWORK,s,D)),D=null},D.ontimeout=function(){let N=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const R=d.transitional||$m;d.timeoutErrorMessage&&(N=d.timeoutErrorMessage),c(new pe(N,R.clarifyTimeoutError?pe.ETIMEDOUT:pe.ECONNABORTED,s,D)),D=null},f===void 0&&p.setContentType(null),"setRequestHeader"in D&&G.forEach(p.toJSON(),function(N,R){D.setRequestHeader(R,N)}),G.isUndefined(d.withCredentials)||(D.withCredentials=!!d.withCredentials),g&&g!=="json"&&(D.responseType=d.responseType),m&&([_,w]=Ri(m,!0),D.addEventListener("progress",_)),v&&D.upload&&([b,U]=Ri(v),D.upload.addEventListener("progress",b),D.upload.addEventListener("loadend",U)),(d.cancelToken||d.signal)&&(x=T=>{D&&(c(!T||T.type?new Jl(null,s,D):T),D.abort(),D=null)},d.cancelToken&&d.cancelToken.subscribe(x),d.signal&&(d.signal.aborted?x():d.signal.addEventListener("abort",x)));const A=tx(d.url);if(A&&at.protocols.indexOf(A)===-1){c(new pe("Unsupported protocol "+A+":",pe.ERR_BAD_REQUEST,s));return}D.send(f||null)})},ux=(s,r)=>{const{length:u}=s=s?s.filter(Boolean):[];if(r||u){let c=new AbortController,d;const f=function(m){if(!d){d=!0,g();const x=m instanceof Error?m:this.reason;c.abort(x instanceof pe?x:new Jl(x instanceof Error?x.message:x))}};let p=r&&setTimeout(()=>{p=null,f(new pe(`timeout ${r} of ms exceeded`,pe.ETIMEDOUT))},r);const g=()=>{s&&(p&&clearTimeout(p),p=null,s.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),s=null)};s.forEach(m=>m.addEventListener("abort",f));const{signal:v}=c;return v.unsubscribe=()=>G.asap(g),v}},dx=function*(s,r){let u=s.byteLength;if(u<r){yield s;return}let c=0,d;for(;c<u;)d=c+r,yield s.slice(c,d),c=d},fx=async function*(s,r){for await(const u of hx(s))yield*dx(u,r)},hx=async function*(s){if(s[Symbol.asyncIterator]){yield*s;return}const r=s.getReader();try{for(;;){const{done:u,value:c}=await r.read();if(u)break;yield c}}finally{await r.cancel()}},im=(s,r,u,c)=>{const d=fx(s,r);let f=0,p,g=v=>{p||(p=!0,c&&c(v))};return new ReadableStream({async pull(v){try{const{done:m,value:x}=await d.next();if(m){g(),v.close();return}let b=x.byteLength;if(u){let _=f+=b;u(_)}v.enqueue(new Uint8Array(x))}catch(m){throw g(m),m}},cancel(v){return g(v),d.return()}},{highWaterMark:2})},Gi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ap=Gi&&typeof ReadableStream=="function",mx=Gi&&(typeof TextEncoder=="function"?(s=>r=>s.encode(r))(new TextEncoder):async s=>new Uint8Array(await new Response(s).arrayBuffer())),lp=(s,...r)=>{try{return!!s(...r)}catch{return!1}},px=ap&&lp(()=>{let s=!1;const r=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return s=!0,"half"}}).headers.has("Content-Type");return s&&!r}),rm=64*1024,wc=ap&&lp(()=>G.isReadableStream(new Response("").body)),Oi={stream:wc&&(s=>s.body)};Gi&&(s=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Oi[r]&&(Oi[r]=G.isFunction(s[r])?u=>u[r]():(u,c)=>{throw new pe(`Response type '${r}' is not supported`,pe.ERR_NOT_SUPPORT,c)})})})(new Response);const yx=async s=>{if(s==null)return 0;if(G.isBlob(s))return s.size;if(G.isSpecCompliantForm(s))return(await new Request(at.origin,{method:"POST",body:s}).arrayBuffer()).byteLength;if(G.isArrayBufferView(s)||G.isArrayBuffer(s))return s.byteLength;if(G.isURLSearchParams(s)&&(s=s+""),G.isString(s))return(await mx(s)).byteLength},gx=async(s,r)=>{const u=G.toFiniteNumber(s.getContentLength());return u??yx(r)},vx=Gi&&(async s=>{let{url:r,method:u,data:c,signal:d,cancelToken:f,timeout:p,onDownloadProgress:g,onUploadProgress:v,responseType:m,headers:x,withCredentials:b="same-origin",fetchOptions:_}=tp(s);m=m?(m+"").toLowerCase():"text";let U=ux([d,f&&f.toAbortSignal()],p),w;const z=U&&U.unsubscribe&&(()=>{U.unsubscribe()});let D;try{if(v&&px&&u!=="get"&&u!=="head"&&(D=await gx(x,c))!==0){let R=new Request(r,{method:"POST",body:c,duplex:"half"}),W;if(G.isFormData(c)&&(W=R.headers.get("content-type"))&&x.setContentType(W),R.body){const[P,F]=lm(D,Ri(nm(v)));c=im(R.body,rm,P,F)}}G.isString(b)||(b=b?"include":"omit");const C="credentials"in Request.prototype;w=new Request(r,{..._,signal:U,method:u.toUpperCase(),headers:x.normalize().toJSON(),body:c,duplex:"half",credentials:C?b:void 0});let A=await fetch(w);const T=wc&&(m==="stream"||m==="response");if(wc&&(g||T&&z)){const R={};["status","statusText","headers"].forEach(ee=>{R[ee]=A[ee]});const W=G.toFiniteNumber(A.headers.get("content-length")),[P,F]=g&&lm(W,Ri(nm(g),!0))||[];A=new Response(im(A.body,rm,P,()=>{F&&F(),z&&z()}),R)}m=m||"text";let N=await Oi[G.findKey(Oi,m)||"text"](A,s);return!T&&z&&z(),await new Promise((R,W)=>{Im(R,W,{data:N,headers:dt.from(A.headers),status:A.status,statusText:A.statusText,config:s,request:w})})}catch(C){throw z&&z(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new pe("Network Error",pe.ERR_NETWORK,s,w),{cause:C.cause||C}):pe.from(C,C&&C.code,s,w)}}),Cc={http:M1,xhr:cx,fetch:vx};G.forEach(Cc,(s,r)=>{if(s){try{Object.defineProperty(s,"name",{value:r})}catch{}Object.defineProperty(s,"adapterName",{value:r})}});const om=s=>`- ${s}`,xx=s=>G.isFunction(s)||s===null||s===!1,np={getAdapter:s=>{s=G.isArray(s)?s:[s];const{length:r}=s;let u,c;const d={};for(let f=0;f<r;f++){u=s[f];let p;if(c=u,!xx(u)&&(c=Cc[(p=String(u)).toLowerCase()],c===void 0))throw new pe(`Unknown adapter '${p}'`);if(c)break;d[p||"#"+f]=c}if(!c){const f=Object.entries(d).map(([g,v])=>`adapter ${g} `+(v===!1?"is not supported by the environment":"is not available in the build"));let p=r?f.length>1?`since :
`+f.map(om).join(`
`):" "+om(f[0]):"as no adapter specified";throw new pe("There is no suitable adapter to dispatch the request "+p,"ERR_NOT_SUPPORT")}return c},adapters:Cc};function gc(s){if(s.cancelToken&&s.cancelToken.throwIfRequested(),s.signal&&s.signal.aborted)throw new Jl(null,s)}function cm(s){return gc(s),s.headers=dt.from(s.headers),s.data=yc.call(s,s.transformRequest),["post","put","patch"].indexOf(s.method)!==-1&&s.headers.setContentType("application/x-www-form-urlencoded",!1),np.getAdapter(s.adapter||rs.adapter)(s).then(function(c){return gc(s),c.data=yc.call(s,s.transformResponse,c),c.headers=dt.from(c.headers),c},function(c){return Pm(c)||(gc(s),c&&c.response&&(c.response.data=yc.call(s,s.transformResponse,c.response),c.response.headers=dt.from(c.response.headers))),Promise.reject(c)})}const sp="1.9.0",Xi={};["object","boolean","number","function","string","symbol"].forEach((s,r)=>{Xi[s]=function(c){return typeof c===s||"a"+(r<1?"n ":" ")+s}});const um={};Xi.transitional=function(r,u,c){function d(f,p){return"[Axios v"+sp+"] Transitional option '"+f+"'"+p+(c?". "+c:"")}return(f,p,g)=>{if(r===!1)throw new pe(d(p," has been removed"+(u?" in "+u:"")),pe.ERR_DEPRECATED);return u&&!um[p]&&(um[p]=!0,console.warn(d(p," has been deprecated since v"+u+" and will be removed in the near future"))),r?r(f,p,g):!0}};Xi.spelling=function(r){return(u,c)=>(console.warn(`${c} is likely a misspelling of ${r}`),!0)};function bx(s,r,u){if(typeof s!="object")throw new pe("options must be an object",pe.ERR_BAD_OPTION_VALUE);const c=Object.keys(s);let d=c.length;for(;d-- >0;){const f=c[d],p=r[f];if(p){const g=s[f],v=g===void 0||p(g,f,s);if(v!==!0)throw new pe("option "+f+" must be "+v,pe.ERR_BAD_OPTION_VALUE);continue}if(u!==!0)throw new pe("Unknown option "+f,pe.ERR_BAD_OPTION)}}const Ci={assertOptions:bx,validators:Xi},Vt=Ci.validators;let ll=class{constructor(r){this.defaults=r||{},this.interceptors={request:new tm,response:new tm}}async request(r,u){try{return await this._request(r,u)}catch(c){if(c instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const f=d.stack?d.stack.replace(/^.+\n/,""):"";try{c.stack?f&&!String(c.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+f):c.stack=f}catch{}}throw c}}_request(r,u){typeof r=="string"?(u=u||{},u.url=r):u=r||{},u=sl(this.defaults,u);const{transitional:c,paramsSerializer:d,headers:f}=u;c!==void 0&&Ci.assertOptions(c,{silentJSONParsing:Vt.transitional(Vt.boolean),forcedJSONParsing:Vt.transitional(Vt.boolean),clarifyTimeoutError:Vt.transitional(Vt.boolean)},!1),d!=null&&(G.isFunction(d)?u.paramsSerializer={serialize:d}:Ci.assertOptions(d,{encode:Vt.function,serialize:Vt.function},!0)),u.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?u.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:u.allowAbsoluteUrls=!0),Ci.assertOptions(u,{baseUrl:Vt.spelling("baseURL"),withXsrfToken:Vt.spelling("withXSRFToken")},!0),u.method=(u.method||this.defaults.method||"get").toLowerCase();let p=f&&G.merge(f.common,f[u.method]);f&&G.forEach(["delete","get","head","post","put","patch","common"],w=>{delete f[w]}),u.headers=dt.concat(p,f);const g=[];let v=!0;this.interceptors.request.forEach(function(z){typeof z.runWhen=="function"&&z.runWhen(u)===!1||(v=v&&z.synchronous,g.unshift(z.fulfilled,z.rejected))});const m=[];this.interceptors.response.forEach(function(z){m.push(z.fulfilled,z.rejected)});let x,b=0,_;if(!v){const w=[cm.bind(this),void 0];for(w.unshift.apply(w,g),w.push.apply(w,m),_=w.length,x=Promise.resolve(u);b<_;)x=x.then(w[b++],w[b++]);return x}_=g.length;let U=u;for(b=0;b<_;){const w=g[b++],z=g[b++];try{U=w(U)}catch(D){z.call(this,D);break}}try{x=cm.call(this,U)}catch(w){return Promise.reject(w)}for(b=0,_=m.length;b<_;)x=x.then(m[b++],m[b++]);return x}getUri(r){r=sl(this.defaults,r);const u=ep(r.baseURL,r.url,r.allowAbsoluteUrls);return Jm(u,r.params,r.paramsSerializer)}};G.forEach(["delete","get","head","options"],function(r){ll.prototype[r]=function(u,c){return this.request(sl(c||{},{method:r,url:u,data:(c||{}).data}))}});G.forEach(["post","put","patch"],function(r){function u(c){return function(f,p,g){return this.request(sl(g||{},{method:r,headers:c?{"Content-Type":"multipart/form-data"}:{},url:f,data:p}))}}ll.prototype[r]=u(),ll.prototype[r+"Form"]=u(!0)});let jx=class ip{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let u;this.promise=new Promise(function(f){u=f});const c=this;this.promise.then(d=>{if(!c._listeners)return;let f=c._listeners.length;for(;f-- >0;)c._listeners[f](d);c._listeners=null}),this.promise.then=d=>{let f;const p=new Promise(g=>{c.subscribe(g),f=g}).then(d);return p.cancel=function(){c.unsubscribe(f)},p},r(function(f,p,g){c.reason||(c.reason=new Jl(f,p,g),u(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const u=this._listeners.indexOf(r);u!==-1&&this._listeners.splice(u,1)}toAbortSignal(){const r=new AbortController,u=c=>{r.abort(c)};return this.subscribe(u),r.signal.unsubscribe=()=>this.unsubscribe(u),r.signal}static source(){let r;return{token:new ip(function(d){r=d}),cancel:r}}};function Sx(s){return function(u){return s.apply(null,u)}}function Nx(s){return G.isObject(s)&&s.isAxiosError===!0}const Ac={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ac).forEach(([s,r])=>{Ac[r]=s});function rp(s){const r=new ll(s),u=Lm(ll.prototype.request,r);return G.extend(u,ll.prototype,r,{allOwnKeys:!0}),G.extend(u,r,null,{allOwnKeys:!0}),u.create=function(d){return rp(sl(s,d))},u}const me=rp(rs);me.Axios=ll;me.CanceledError=Jl;me.CancelToken=jx;me.isCancel=Pm;me.VERSION=sp;me.toFormData=Yi;me.AxiosError=pe;me.Cancel=me.CanceledError;me.all=function(r){return Promise.all(r)};me.spread=Sx;me.isAxiosError=Nx;me.mergeConfig=sl;me.AxiosHeaders=dt;me.formToJSON=s=>Wm(G.isHTMLForm(s)?new FormData(s):s);me.getAdapter=np.getAdapter;me.HttpStatusCode=Ac;me.default=me;const{Axios:Jx,AxiosError:$x,CanceledError:Wx,isCancel:Px,CancelToken:Ix,VERSION:e2,all:t2,Cancel:a2,isAxiosError:l2,spread:n2,toFormData:s2,AxiosHeaders:i2,HttpStatusCode:r2,formToJSON:o2,getAdapter:c2,mergeConfig:u2}=me,op=E.createContext({admin:null,token:null,loading:!1,login:()=>{},logout:()=>{},updateAdmin:()=>{},isAuthenticated:!1,url:"https://eatzone.onrender.com"}),Vi=()=>{const s=E.useContext(op);if(!s)throw new Error("useAdmin must be used within an AdminProvider");return s},Ex=({children:s})=>{const[r,u]=E.useState(null),[c,d]=E.useState(localStorage.getItem("adminToken")||null),[f,p]=E.useState(!1),g="https://eatzone.onrender.com",v=!!(r&&c),m=async(U,w)=>{var z,D;try{p(!0);const C=await me.post(`${g}/api/admin/login`,{email:U,password:w});if(C.data.success){const{token:A,admin:T}=C.data;return d(A),u(T),localStorage.setItem("adminToken",A),{success:!0}}else return{success:!1,message:C.data.message}}catch(C){return console.error("Login error:",C),{success:!1,message:((D=(z=C.response)==null?void 0:z.data)==null?void 0:D.message)||"Login failed"}}finally{p(!1)}},x=()=>{u(null),d(null),localStorage.removeItem("adminToken")},b=U=>{u(U)};E.useEffect(()=>{(async()=>{if(c)try{const w=await me.get(`${g}/api/admin/verify`,{headers:{Authorization:`Bearer ${c}`}});w.data.success?u(w.data.admin):x()}catch(w){console.error("Token verification failed:",w),x()}p(!1)})()},[c,g]),E.useEffect(()=>{const U=localStorage.getItem("adminToken"),w=localStorage.getItem("adminData");if(U&&w)try{d(U),u(JSON.parse(w))}catch(z){console.error("Error loading saved admin data:",z),x()}},[]);const _={admin:r,token:c,loading:f,login:m,logout:x,updateAdmin:b,isAuthenticated:v,url:g};return n.jsx(op.Provider,{value:_,children:s})},Tx=({setSidebarOpen:s})=>{var v;const[r,u]=E.useState(!1),{admin:c,logout:d}=Vi(),f=E.useRef(null),p=()=>{s(m=>!m)},g=()=>{d(),u(!1)};return E.useEffect(()=>{const m=x=>{f.current&&!f.current.contains(x.target)&&u(!1)};return document.addEventListener("mousedown",m),()=>{document.removeEventListener("mousedown",m)}},[]),n.jsx("nav",{className:"navbar",children:n.jsxs("div",{className:"navbar-content",children:[n.jsxs("div",{className:"navbar-left",children:[n.jsx("button",{className:"sidebar-toggle",onClick:p,"aria-label":"Toggle sidebar",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),n.jsx("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),n.jsx("line",{x1:"3",y1:"18",x2:"21",y2:"18"})]})}),n.jsx("div",{className:"navbar-brand",children:n.jsx("h1",{children:"EatZone Admin"})})]}),n.jsx("div",{className:"navbar-right",children:n.jsxs("div",{className:"admin-profile",ref:f,onClick:()=>u(!r),children:[n.jsx("div",{className:"admin-avatar",children:n.jsx("span",{children:((v=c==null?void 0:c.name)==null?void 0:v.charAt(0))||"A"})}),n.jsx("span",{className:"admin-name",children:(c==null?void 0:c.name)||"Admin"}),n.jsx("svg",{className:"dropdown-icon",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("polyline",{points:"6,9 12,15 18,9"})}),r&&n.jsxs("div",{className:"admin-dropdown",children:[n.jsxs("div",{className:"dropdown-item",children:[n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"12",cy:"7",r:"4"})]}),"Profile"]}),n.jsxs("div",{className:"dropdown-item",children:[n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"}),n.jsx("circle",{cx:"12",cy:"12",r:"3"})]}),"Settings"]}),n.jsx("div",{className:"dropdown-divider"}),n.jsxs("div",{className:"dropdown-item logout",onClick:g,children:[n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),n.jsx("polyline",{points:"16,17 21,12 16,7"}),n.jsx("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),"Logout"]})]})]})})]})})},_x=({isOpen:s,setSidebarOpen:r})=>{const u=[{path:"/dashboard",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("rect",{x:"3",y:"3",width:"7",height:"7"}),n.jsx("rect",{x:"14",y:"3",width:"7",height:"7"}),n.jsx("rect",{x:"14",y:"14",width:"7",height:"7"}),n.jsx("rect",{x:"3",y:"14",width:"7",height:"7"})]}),label:"Dashboard",description:"Overview & Analytics"},{path:"/add",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),n.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),label:"Add Food Items",description:"Create new menu items"},{path:"/list",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M9 12l2 2 4-4"}),n.jsx("path",{d:"M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"}),n.jsx("path",{d:"M21 19c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"})]}),label:"List Items",description:"Manage menu items"},{path:"/categories",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("rect",{x:"3",y:"3",width:"7",height:"7"}),n.jsx("rect",{x:"14",y:"3",width:"7",height:"7"}),n.jsx("rect",{x:"14",y:"14",width:"7",height:"7"}),n.jsx("rect",{x:"3",y:"14",width:"7",height:"7"})]}),label:"Food Categories",description:"Manage food categories"},{path:"/add-restaurant",icon:n.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("path",{d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),label:"Add Restaurant",description:"Create new restaurant"},{path:"/restaurant-list",icon:n.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("path",{d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),label:"Restaurant List",description:"Manage restaurants"},{path:"/orders",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),n.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),label:"Orders",description:"Manage customer orders"},{path:"/analytics",icon:n.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("polyline",{points:"22,12 18,12 15,21 9,3 6,12 2,12"})}),label:"Analytics",description:"Sales & performance"},{path:"/delivery-partners",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"9",cy:"7",r:"4"}),n.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),n.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]}),label:"Delivery Partners",description:"Manage delivery team"},{path:"/feedback",icon:n.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})}),label:"Feedback & Complaints",description:"Customer feedback"},{path:"/debug",icon:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M12 2L2 7l10 5 10-5-10-5z"}),n.jsx("path",{d:"M2 17l10 5 10-5"}),n.jsx("path",{d:"M2 12l10 5 10-5"})]}),label:"API Debug",description:"Debug API connections"}];return n.jsxs(n.Fragment,{children:[isMobileOpen&&n.jsx("div",{className:"mobile-overlay active",onClick:onMobileClose}),n.jsxs("div",{className:`sidebar ${isMobileOpen?"mobile-open":""}`,children:[n.jsxs("div",{className:"sidebar-header",children:[n.jsx("h3",{children:"Navigation"}),n.jsx("span",{children:"Manage your platform"})]}),n.jsx("div",{className:"sidebar-menu",children:u.map((c,d)=>n.jsxs(Cm,{to:c.path,className:({isActive:f})=>`sidebar-option ${f?"active":""}`,"data-tooltip":c.label,onClick:onMobileClose,children:[n.jsx("div",{className:"sidebar-option-icon",children:c.icon}),n.jsxs("div",{className:"sidebar-option-content",children:[n.jsx("span",{className:"sidebar-option-label",children:c.label}),n.jsx("span",{className:"sidebar-option-description",children:c.description})]})]},d))}),n.jsx("div",{className:"sidebar-footer",children:n.jsxs("div",{className:"sidebar-help",children:[n.jsx("div",{className:"help-icon",children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),n.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]})}),n.jsxs("div",{children:[n.jsx("span",{className:"help-title",children:"Need Help?"}),n.jsx("span",{className:"help-subtitle",children:"Contact support"})]})]})})]})]})},wx=()=>{const[s,r]=E.useState({email:"",password:""}),[u,c]=E.useState(!1),{login:d,loading:f}=Vi(),p=m=>{r({...s,[m.target.name]:m.target.value})},g=async m=>{if(m.preventDefault(),!s.email||!s.password){Y.error("Please fill in all fields");return}const x=await d(s.email,s.password);x.success?Y.success("Login successful!"):Y.error(x.message||"Login failed")},v=()=>{r({email:"<EMAIL>",password:"admin123"}),Y.info("Demo credentials filled. Click Login to continue.")};return n.jsx("div",{className:"admin-login",children:n.jsxs("div",{className:"login-container",children:[n.jsxs("div",{className:"login-header",children:[n.jsxs("div",{className:"logo",children:[n.jsx("h1",{children:"EatZone"}),n.jsx("span",{children:"Admin Panel"})]}),n.jsx("h2",{children:"Welcome Back"}),n.jsx("p",{children:"Sign in to your admin account"})]}),n.jsxs("form",{onSubmit:g,className:"login-form",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"email",children:"Email Address"}),n.jsxs("div",{className:"input-wrapper",children:[n.jsxs("svg",{className:"input-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),n.jsx("polyline",{points:"22,6 12,13 2,6"})]}),n.jsx("input",{type:"email",id:"email",name:"email",value:s.email,onChange:p,placeholder:"Enter your email",required:!0})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"password",children:"Password"}),n.jsxs("div",{className:"input-wrapper",children:[n.jsxs("svg",{className:"input-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),n.jsx("circle",{cx:"12",cy:"16",r:"1"}),n.jsx("path",{d:"M7 11V7a5 5 0 0 1 10 0v4"})]}),n.jsx("input",{type:u?"text":"password",id:"password",name:"password",value:s.password,onChange:p,placeholder:"Enter your password",required:!0}),n.jsx("button",{type:"button",className:"password-toggle",onClick:()=>c(!u),children:u?n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),n.jsx("line",{x1:"1",y1:"1",x2:"23",y2:"23"})]}):n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),n.jsx("circle",{cx:"12",cy:"12",r:"3"})]})})]})]}),n.jsx("button",{type:"submit",className:"login-btn",disabled:f,children:f?n.jsxs(n.Fragment,{children:[n.jsx("div",{className:"spinner"}),"Signing In..."]}):"Sign In"}),n.jsx("button",{type:"button",className:"demo-btn",onClick:v,disabled:f,children:"Use Demo Credentials"})]}),n.jsx("div",{className:"login-footer",children:n.jsx("p",{children:"© 2024 EatZone. All rights reserved."})})]})})},dm=({url:s})=>{const[r,u]=E.useState({totalOrders:0,totalRevenue:0,totalFoodItems:0,totalUsers:0,todayOrders:0,todayRevenue:0,weekRevenue:0,pendingOrders:0,deliveredOrders:0}),[c,d]=E.useState([]),[f,p]=E.useState([]),[g,v]=E.useState(!0);E.useEffect(()=>{m()},[]);const m=async()=>{try{v(!0);const[_,U,w]=await Promise.all([me.get(`${s}/api/order/list`),me.get(`${s}/api/food/list`),me.get(`${s}/api/user/list`).catch(()=>({data:{success:!1,data:[]}}))]),z=_.data.success?_.data.data:[],D=U.data.success?U.data.data:[],C=w.data.success?w.data.data:[],A=new Date().toDateString(),T=z.filter(re=>new Date(re.date).toDateString()===A),N=new Date;N.setDate(N.getDate()-7);const R=z.filter(re=>new Date(re.date)>=N),W=z.reduce((re,ye)=>re+ye.amount,0),P=T.reduce((re,ye)=>re+ye.amount,0),F=R.reduce((re,ye)=>re+ye.amount,0),ee=z.filter(re=>re.status==="Food Processing"||re.status==="Out for delivery").length,Q=z.filter(re=>re.status==="Delivered").length;u({totalOrders:z.length,totalRevenue:W,totalFoodItems:D.length,totalUsers:C.length,todayOrders:T.length,todayRevenue:P,weekRevenue:F,pendingOrders:ee,deliveredOrders:Q});const $=z.sort((re,ye)=>new Date(ye.date)-new Date(re.date));d($.slice(0,10));const ne={};z.forEach(re=>{var ye;(ye=re.items)==null||ye.forEach(X=>{ne[X._id]=(ne[X._id]||0)+X.quantity})});const ce=D.map(re=>({...re,orderCount:ne[re._id]||0})).sort((re,ye)=>ye.orderCount-re.orderCount).slice(0,5);p(ce)}catch(_){console.error("Error fetching dashboard data:",_)}finally{v(!1)}},x=_=>`₹${_}`,b=({title:_,value:U,icon:w,color:z,change:D})=>n.jsx("div",{className:"stat-card",children:n.jsxs("div",{className:"stat-card-header",children:[n.jsx("div",{className:`stat-icon ${z}`,children:w}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:U}),n.jsx("p",{children:_}),D&&n.jsxs("span",{className:`stat-change ${D.type}`,children:[D.type==="increase"?"↗":"↘"," ",D.value,"%"]})]})]})});return g?n.jsx("div",{className:"dashboard",children:n.jsxs("div",{className:"dashboard-loading",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading dashboard..."})]})}):n.jsxs("div",{className:"dashboard",children:[n.jsxs("div",{className:"dashboard-header",children:[n.jsx("h1",{children:"Dashboard Overview"}),n.jsx("p",{children:"Welcome back! Here's what's happening with your restaurant today."})]}),n.jsxs("div",{className:"stats-grid",children:[n.jsx(b,{title:"Total Orders",value:r.totalOrders,color:"primary",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),n.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),change:{type:"increase",value:12}}),n.jsx(b,{title:"Total Revenue",value:x(r.totalRevenue),color:"success",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"12",y1:"1",x2:"12",y2:"23"}),n.jsx("path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})]}),change:{type:"increase",value:8}}),n.jsx(b,{title:"Food Items",value:r.totalFoodItems,color:"warning",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M18 8h1a4 4 0 0 1 0 8h-1"}),n.jsx("path",{d:"M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"}),n.jsx("line",{x1:"6",y1:"1",x2:"6",y2:"4"}),n.jsx("line",{x1:"10",y1:"1",x2:"10",y2:"4"}),n.jsx("line",{x1:"14",y1:"1",x2:"14",y2:"4"})]})}),n.jsx(b,{title:"Today's Orders",value:r.todayOrders,color:"info",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("polyline",{points:"12,6 12,12 16,14"})]}),change:{type:"increase",value:5}}),n.jsx(b,{title:"Total Users",value:r.totalUsers,color:"purple",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"12",cy:"7",r:"4"})]}),change:{type:"increase",value:5}}),n.jsx(b,{title:"Week Revenue",value:x(r.weekRevenue),color:"success",icon:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M3 3v18h18"}),n.jsx("path",{d:"M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"})]}),change:{type:"increase",value:22}})]}),n.jsxs("div",{className:"dashboard-content",children:[n.jsx("div",{className:"dashboard-left",children:n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Recent Orders"}),n.jsx("p",{className:"card-subtitle",children:"Latest customer orders"})]}),n.jsx("div",{className:"recent-orders",children:c.length>0?c.map((_,U)=>n.jsxs("div",{className:"order-item",children:[n.jsxs("div",{className:"order-info",children:[n.jsxs("h4",{children:["Order #",_._id.slice(-6)]}),n.jsxs("p",{children:[_.address.firstName," ",_.address.lastName]}),n.jsx("span",{className:"order-time",children:new Date(_.date).toLocaleDateString()})]}),n.jsxs("div",{className:"order-details",children:[n.jsx("span",{className:"order-amount",children:x(_.amount)}),n.jsx("span",{className:`badge badge-${_.status==="Delivered"?"success":_.status==="Out for delivery"?"warning":"info"}`,children:_.status})]})]},U)):n.jsx("p",{className:"no-data",children:"No recent orders"})})]})}),n.jsxs("div",{className:"dashboard-right",children:[n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Popular Items"}),n.jsx("p",{className:"card-subtitle",children:"Best selling food items"})]}),n.jsx("div",{className:"top-items",children:f.length>0?f.map((_,U)=>n.jsxs("div",{className:"food-item",children:[n.jsx("img",{src:`${s}/images/${_.image}`,alt:_.name,className:"food-image"}),n.jsxs("div",{className:"food-info",children:[n.jsx("h4",{children:_.name}),n.jsx("p",{children:_.category}),n.jsx("span",{className:"food-price",children:x(_.price)})]}),n.jsxs("div",{className:"food-rank",children:["#",U+1]})]},U)):n.jsx("p",{className:"no-data",children:"No food items"})})]}),n.jsxs("div",{className:"card",children:[n.jsx("div",{className:"card-header",children:n.jsx("h3",{className:"card-title",children:"Quick Stats"})}),n.jsxs("div",{className:"quick-stats",children:[n.jsxs("div",{className:"quick-stat",children:[n.jsx("span",{className:"stat-label",children:"Pending Orders"}),n.jsx("span",{className:"stat-value",children:r.pendingOrders})]}),n.jsxs("div",{className:"quick-stat",children:[n.jsx("span",{className:"stat-label",children:"Delivered Today"}),n.jsx("span",{className:"stat-value",children:r.deliveredOrders})]}),n.jsxs("div",{className:"quick-stat",children:[n.jsx("span",{className:"stat-label",children:"Today's Revenue"}),n.jsx("span",{className:"stat-value",children:x(r.todayRevenue)})]})]})]})]})]})]})},Yc=async(s,r="eatzone",u={})=>{var c;try{const d=new FormData;d.append("file",s),d.append("upload_preset","eatzone_admin"),r&&d.append("folder",r),u.tags&&d.append("tags",Array.isArray(u.tags)?u.tags.join(","):u.tags),u.transformation&&d.append("transformation",JSON.stringify(u.transformation));const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const v=await f.json();throw new Error(((c=v.error)==null?void 0:c.message)||"Upload failed")}const p=await f.json(),g=p.secure_url.replace("/upload/","/upload/f_auto,q_auto,w_800,h_600,c_fill/");return{success:!0,url:p.secure_url,optimizedUrl:g,publicId:p.public_id}}catch(d){return console.error("Cloudinary upload error:",d),{success:!1,error:d.message}}},oa={food:{folder:"eatzone/food",tags:["food","menu"],transformation:{width:800,height:600,crop:"fill",quality:"auto",format:"auto"}},restaurant:{folder:"eatzone/restaurants",tags:["restaurant","cover"],transformation:{width:1200,height:800,crop:"fill",quality:"auto",format:"auto"}},category:{folder:"eatzone/categories",tags:["category","icon"],transformation:{width:200,height:200,crop:"fill",quality:"auto",format:"auto"}}},Cx=(s,r={})=>{const{maxSize:u=5*1024*1024,allowedTypes:c=["image/jpeg","image/jpg","image/png","image/webp"],minWidth:d=100,minHeight:f=100}=r;return s.size>u?{valid:!1,error:`File size must be less than ${Math.round(u/(1024*1024))}MB`}:c.includes(s.type)?{valid:!0}:{valid:!1,error:`File type must be one of: ${c.join(", ")}`}},Ax=({url:s})=>{const[r,u]=E.useState(!1),[c,d]=E.useState(!1),[f,p]=E.useState(""),[g,v]=E.useState([]),[m,x]=E.useState([]),[b,_]=E.useState({name:"",description:"",price:"",category:"",restaurantId:"",discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""});E.useEffect(()=>{U(),w()},[]);const U=async()=>{try{const A=await me.get(`${s}/api/restaurant/list`);A.data.success&&v(A.data.data)}catch(A){console.error("Error fetching restaurants:",A),Y.error("Failed to load restaurants")}},w=async()=>{try{const A=await me.get(`${s}/api/category/list`);A.data.success&&(x(A.data.data),A.data.data.length>0&&!b.category&&_(T=>({...T,category:A.data.data[0].name})))}catch(A){console.error("Error fetching categories:",A),Y.error("Failed to load categories")}},z=A=>{const T=A.target.name,N=A.target.type==="checkbox"?A.target.checked:A.target.value;_(R=>({...R,[T]:N}))},D=async A=>{if(A){d(!0),Y.info("Uploading image to Cloudinary...");try{const T=await Yc(A,oa.food.folder,{tags:oa.food.tags,transformation:oa.food.transformation});T.success?(console.log("Cloudinary upload successful:",T.url),p(T.url),u(!1),Y.success("Image uploaded successfully!")):(console.error("Cloudinary upload failed:",T.error),Y.error(T.error||"Failed to upload image"))}catch(T){console.error("Image upload error:",T),Y.error("Failed to upload image")}finally{d(!1)}}},C=async A=>{A.preventDefault();try{if(!f){Y.error("Please upload an image to Cloudinary first");return}if(!b.name||!b.description||!b.price){Y.error("Please fill all required fields");return}if(console.log("Form submission data:",{name:b.name,description:b.description,price:b.price,category:b.category,cloudinaryUrl:f,restaurantId:b.restaurantId}),r&&!f){Y.error("Please upload the image to Cloudinary first by clicking the 'Upload to Cloudinary' button");return}const T=new FormData;T.append("name",b.name),T.append("description",b.description),T.append("price",Number(b.price)),T.append("category",b.category),T.append("restaurantId",b.restaurantId),T.append("image",f),b.discountPercentage&&T.append("discountPercentage",Number(b.discountPercentage)),b.discountLabel&&T.append("discountLabel",b.discountLabel),T.append("isPopular",b.isPopular),T.append("isFeatured",b.isFeatured),b.tags&&T.append("tags",b.tags),Y.info("Adding food item...");const N=await me.post(`${s}/api/food/add`,T);N.data.success?(_({name:"",description:"",price:"",category:m.length>0?m[0].name:"",restaurantId:"",discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),u(!1),p(""),Y.success(N.data.message||"Food item added successfully!")):Y.error(N.data.message||"Failed to add food item")}catch(T){console.error("Error adding food item:",T),Y.error(T.response&&T.response.data&&T.response.data.message||"An error occurred while adding the food item")}};return n.jsx("div",{className:"add",children:n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h2",{className:"card-title",children:"Add New Food Item"}),n.jsx("p",{className:"card-subtitle",children:"Create a new menu item for your restaurant"})]}),n.jsxs("form",{onSubmit:C,children:[n.jsxs("div",{className:"add-img-upload",children:[n.jsx("label",{className:"form-label required",children:"Food Image"}),n.jsxs("label",{htmlFor:"image",children:[f?n.jsx("img",{src:f,alt:"Food preview from Cloudinary",onError:A=>{console.error("Failed to load Cloudinary image:",f),A.target.style.display="none";const T=A.target.nextElementSibling;T&&(T.style.display="block")},onLoad:()=>{console.log("Cloudinary image loaded successfully:",f)}}):r?n.jsx("img",{src:URL.createObjectURL(r),alt:"Food preview",onError:A=>{console.error("Failed to load local image preview"),A.target.style.display="none"}}):n.jsxs("div",{className:"upload-text",children:[n.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),n.jsx("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),n.jsx("polyline",{points:"21,15 16,10 5,21"})]}),n.jsx("h3",{children:"Upload Food Image"}),n.jsx("p",{children:"Click to browse or drag and drop your image here"}),n.jsx("p",{children:"Recommended: 400x400px, JPG or PNG"})]}),f&&n.jsx("div",{className:"error-fallback",style:{display:"none"},children:n.jsxs("div",{className:"upload-text",children:[n.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),n.jsx("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),n.jsx("polyline",{points:"21,15 16,10 5,21"})]}),n.jsx("h3",{children:"Image Upload Complete"}),n.jsx("p",{children:"Image uploaded to Cloudinary but preview failed to load"}),n.jsx("p",{children:"The image will display correctly on the website"})]})})]}),n.jsx("input",{onChange:A=>{const T=A.target.files[0];T&&(console.log("File selected:",T.name,T.size,T.type),u(T),p(""))},type:"file",id:"image",accept:"image/*",hidden:!0,required:!0}),r&&!f&&n.jsx("div",{className:"upload-actions",children:n.jsx("button",{type:"button",onClick:()=>D(r),disabled:c,className:"upload-btn",children:c?"Uploading...":"Upload to Cloudinary"})}),f&&n.jsxs("div",{className:"upload-success",children:[n.jsx("span",{className:"success-text",children:"✅ Image uploaded to Cloudinary successfully!"}),n.jsx("div",{className:"cloudinary-url",children:n.jsxs("small",{children:["URL: ",f]})}),n.jsxs("div",{className:"test-actions",children:[n.jsx("button",{type:"button",onClick:()=>{window.open(f,"_blank")},className:"test-btn",children:"🔗 Test Image URL"}),n.jsx("button",{type:"button",onClick:()=>{const A=document.querySelector(".add-img-upload img");A&&(A.src=f+"?t="+Date.now())},className:"test-btn",children:"🔄 Refresh Preview"})]})]})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"add-product-name",children:[n.jsx("label",{className:"form-label required",children:"Product Name"}),n.jsx("input",{onChange:z,value:b.name,type:"text",name:"name",placeholder:"Enter food item name",required:!0})]}),n.jsxs("div",{className:"add-category",children:[n.jsx("label",{className:"form-label required",children:"Category"}),n.jsxs("select",{onChange:z,name:"category",value:b.category,required:!0,children:[n.jsx("option",{value:"",children:"Select Category"}),m.map(A=>n.jsx("option",{value:A.name,children:A.name},A._id))]}),m.length===0&&n.jsx("small",{className:"form-help",children:"No categories available. Please add categories first in the Categories section."})]})]}),n.jsx("div",{className:"form-row",children:n.jsxs("div",{className:"add-restaurant",children:[n.jsx("label",{className:"form-label",children:"Restaurant (Optional)"}),n.jsxs("select",{onChange:z,name:"restaurantId",value:b.restaurantId,children:[n.jsx("option",{value:"",children:"Select Restaurant (Optional)"}),g.map(A=>n.jsx("option",{value:A._id,children:A.name},A._id))]}),n.jsx("small",{className:"form-help",children:"Leave empty to add as general food item, or select a restaurant to associate this item with a specific restaurant."})]})}),n.jsxs("div",{className:"add-product-description",children:[n.jsx("label",{className:"form-label required",children:"Description"}),n.jsx("textarea",{onChange:z,value:b.description,name:"description",rows:"4",placeholder:"Describe your food item, ingredients, and special features...",required:!0})]}),n.jsxs("div",{className:"add-category-price",children:[n.jsxs("div",{className:"add-price",children:[n.jsx("label",{className:"form-label required",children:"Price (INR)"}),n.jsx("input",{onChange:z,value:b.price,type:"number",name:"price",placeholder:"₹0",min:"1",required:!0})]}),n.jsxs("div",{className:"add-discount",children:[n.jsx("label",{className:"form-label",children:"Discount (%)"}),n.jsx("input",{onChange:z,value:b.discountPercentage,type:"number",name:"discountPercentage",placeholder:"0",min:"0",max:"100"})]})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"add-discount-label",children:[n.jsx("label",{className:"form-label",children:"Discount Label"}),n.jsx("input",{onChange:z,value:b.discountLabel,type:"text",name:"discountLabel",placeholder:"e.g., MEGA SALE, LIMITED TIME"})]}),n.jsxs("div",{className:"add-tags",children:[n.jsx("label",{className:"form-label",children:"Tags"}),n.jsx("input",{onChange:z,value:b.tags,type:"text",name:"tags",placeholder:"e.g., Bestseller, New, Spicy (comma separated)"})]})]}),n.jsx("div",{className:"form-row",children:n.jsxs("div",{className:"add-checkboxes",children:[n.jsxs("label",{className:"checkbox-label",children:[n.jsx("input",{type:"checkbox",name:"isPopular",checked:b.isPopular,onChange:z}),n.jsx("span",{className:"checkmark"}),"Mark as Popular Item"]}),n.jsxs("label",{className:"checkbox-label",children:[n.jsx("input",{type:"checkbox",name:"isFeatured",checked:b.isFeatured,onChange:z}),n.jsx("span",{className:"checkmark"}),"Mark as Featured Item"]})]})}),n.jsx("div",{className:"form-submit",children:n.jsxs("button",{type:"submit",className:"add-btn",children:[n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),n.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),"Add Food Item"]})})]})]})})},Rx=(s,r)=>{if(!s)return"/api/placeholder/50/50";const u=String(s);if(u.includes("cloudinary.com")||u.startsWith("http")||u.startsWith("/")||u.startsWith("data:"))return u;if(u.includes(".png")||u.includes(".jpg")||u.includes(".jpeg")){const c=u.startsWith("/")?u.substring(1):u;return`${r}/images/${c}`}return`${r}/images/${u}`},Ox=({url:s})=>{const[r,u]=E.useState([]),[c,d]=E.useState([]),[f,p]=E.useState(!0),[g,v]=E.useState(""),[m,x]=E.useState("all"),[b,_]=E.useState(null),[U,w]=E.useState([]),[z,D]=E.useState([]),[C,A]=E.useState({name:"",description:"",price:"",category:"",restaurantId:"",image:null,discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),[T,N]=E.useState(!1),[R,W]=E.useState(""),P=async()=>{try{console.log("Testing API connection to:",s);const q=await me.get(`${s}/test`);console.log("API test response:",q.data),Y.success("API connection successful!",{autoClose:2e3})}catch(q){console.error("API connection test failed:",q),Y.error("API connection failed. Check console for details.",{autoClose:5e3})}},F=async()=>{var q,se,S;try{p(!0),console.log("Fetching food list from:",`${s}/api/food/list`);const V=await me.get(`${s}/api/food/list`);console.log("Food list response:",V.data),V.data.success?(u(V.data.data),d(V.data.data),console.log("Food items loaded:",V.data.data.length,"items"),Y.success(`Food items loaded successfully (${V.data.data.length} items)`,{autoClose:2e3})):(console.error("Failed to load food items:",V.data.message),Y.error(V.data.message||"Failed to load food items"))}catch(V){console.error("Error fetching food list:",V),console.error("Error details:",(q=V.response)==null?void 0:q.data),Y.error(((S=(se=V.response)==null?void 0:se.data)==null?void 0:S.message)||"An error occurred while fetching food items")}finally{p(!1)}},ee=async()=>{var q;try{console.log("Fetching restaurants from:",`${s}/api/restaurant/list`);const se=await me.get(`${s}/api/restaurant/list`);console.log("Restaurants response:",se.data),se.data.success?(w(se.data.data),console.log("Restaurants loaded:",se.data.data.length,"restaurants")):console.error("Failed to load restaurants:",se.data.message)}catch(se){console.error("Error fetching restaurants:",se),console.error("Error details:",(q=se.response)==null?void 0:q.data)}},Q=async q=>{var se,S;if(window.confirm("Are you sure you want to delete this food item?"))try{Y.info("Removing food item...",{autoClose:1e3});const V=await me.post(`${s}/api/food/remove`,{id:q});V.data.success?(Y.success(V.data.message||"Food item removed successfully"),await F()):Y.error(V.data.message||"Failed to remove food item")}catch(V){console.error("Error removing food item:",V),Y.error(((S=(se=V.response)==null?void 0:se.data)==null?void 0:S.message)||"An error occurred while removing food item")}},$=async()=>{var q,se;if(window.confirm(`⚠️ WARNING: Are you sure you want to delete ALL food items?

This action cannot be undone and will remove all items from your database.`))try{Y.info("Clearing all food items...",{autoClose:2e3});const S=await me.post(`${s}/api/food/clear-all`);S.data.success?(Y.success(S.data.message||"All food items cleared successfully"),await F()):Y.error(S.data.message||"Failed to clear food items")}catch(S){console.error("Error clearing food items:",S),Y.error(((se=(q=S.response)==null?void 0:q.data)==null?void 0:se.message)||"An error occurred while clearing food items")}},ne=q=>{var se,S;_(q),A({name:q.name,description:q.description||"",price:(q.originalPrice||q.price).toString(),category:q.category,restaurantId:((se=q.restaurantId)==null?void 0:se._id)||q.restaurantId||"",image:null,discountPercentage:((S=q.discountPercentage)==null?void 0:S.toString())||"",discountLabel:q.discountLabel||"",isPopular:q.isPopular||!1,isFeatured:q.isFeatured||!1,tags:q.tags?q.tags.join(", "):""}),W(""),N(!1)},ce=()=>{_(null),A({name:"",description:"",price:"",category:"",restaurantId:"",image:null,discountPercentage:"",discountLabel:"",isPopular:!1,isFeatured:!1,tags:""}),W(""),N(!1)},re=q=>{const{name:se,value:S,files:V,type:le,checked:ae}=q.target;A(se==="image"?I=>({...I,image:V[0]}):le==="checkbox"?I=>({...I,[se]:ae}):I=>({...I,[se]:S}))},ye=async q=>{if(!q)return;const se=Cx(q);if(!se.valid){Y.error(se.error);return}N(!0),Y.info("Uploading food image to Cloudinary...");try{const S=await Yc(q,oa.food.folder,{tags:oa.food.tags,transformation:oa.food.transformation});S.success?(W(S.url),Y.success("Food image uploaded successfully!")):Y.error(S.error||"Failed to upload food image")}catch(S){console.error("Food image upload error:",S),Y.error("Failed to upload food image")}finally{N(!1)}},X=async q=>{var se,S,V,le,ae;if(q.preventDefault(),!C.name||!C.price||!C.category){Y.error("Please fill in all required fields");return}if(isNaN(C.price)||Number(C.price)<=0){Y.error("Please enter a valid price");return}try{console.log("Updating food item:",b._id),console.log("Form data:",C),Y.info("Updating food item...",{autoClose:1e3});const I=new FormData;I.append("id",b._id),I.append("name",C.name.trim()),I.append("description",C.description.trim()),I.append("price",C.price),I.append("category",C.category),C.restaurantId&&C.restaurantId!=="none"?I.append("restaurantId",C.restaurantId):I.append("restaurantId",""),C.discountPercentage?I.append("discountPercentage",Number(C.discountPercentage)):I.append("discountPercentage",0),C.discountLabel&&I.append("discountLabel",C.discountLabel),I.append("isPopular",C.isPopular),I.append("isFeatured",C.isFeatured),C.tags&&I.append("tags",C.tags),R&&(console.log("Using Cloudinary URL:",R),I.append("image",R));for(let he of I.entries())console.log(he[0]+": "+he[1]);const xe=await me.post(`${s}/api/food/update`,I,{headers:{"Content-Type":"multipart/form-data"},timeout:3e4});console.log("Update response:",xe.data),xe.data.success?(Y.success(xe.data.message||"Food item updated successfully"),await F(),ce()):(console.error("Update failed:",xe.data.message),Y.error(xe.data.message||"Failed to update food item"))}catch(I){console.error("Error updating food item:",I),console.error("Error response:",(se=I.response)==null?void 0:se.data),((S=I.response)==null?void 0:S.status)===404?Y.error("Food item not found"):((V=I.response)==null?void 0:V.status)===400?Y.error(I.response.data.message||"Invalid data provided"):(ae=(le=I.response)==null?void 0:le.data)!=null&&ae.message?Y.error(I.response.data.message):Y.error("An error occurred while updating food item")}};E.useEffect(()=>{let q=r;g&&(q=q.filter(se=>se.name.toLowerCase().includes(g.toLowerCase())||se.category.toLowerCase().includes(g.toLowerCase()))),m!=="all"&&(q=q.filter(se=>se.category===m)),d(q)},[r,g,m]),E.useEffect(()=>{P(),F(),ee(),te()},[]);const te=async()=>{try{const q=await me.get(`${s}/api/category/list`);q.data.success&&D(q.data.data)}catch(q){console.error("Error fetching categories:",q)}};return f?n.jsx("div",{className:"list",children:n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading food items..."})]})}):n.jsxs("div",{className:"list",children:[n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsxs("div",{children:[n.jsx("h2",{className:"card-title",children:"Food Items"}),n.jsx("p",{className:"card-subtitle",children:"Manage your restaurant menu items"})]}),n.jsxs("div",{className:"card-actions",children:[n.jsx("button",{className:"btn btn-primary",onClick:P,title:"Test API connection",style:{marginRight:"1rem"},children:"🔗 Test API"}),n.jsx("button",{className:"btn btn-danger",onClick:$,title:"Clear all food items from database",children:"🗑️ Clear All Items"})]})]}),n.jsxs("div",{className:"list-stats",children:[n.jsxs("div",{className:"list-stat",children:[n.jsx("span",{className:"list-stat-value",children:r.length}),n.jsx("span",{className:"list-stat-label",children:"Total Items"})]}),n.jsxs("div",{className:"list-stat",children:[n.jsx("span",{className:"list-stat-value",children:z.length}),n.jsx("span",{className:"list-stat-label",children:"Categories"})]}),n.jsxs("div",{className:"list-stat",children:[n.jsxs("span",{className:"list-stat-value",children:["₹",r.reduce((q,se)=>q+se.price,0)]}),n.jsx("span",{className:"list-stat-label",children:"Total Value"})]})]}),n.jsxs("div",{className:"list-filters",children:[n.jsx("input",{type:"text",placeholder:"Search food items...",value:g,onChange:q=>v(q.target.value),className:"search-input"}),n.jsxs("select",{value:m,onChange:q=>x(q.target.value),className:"filter-select",children:[n.jsx("option",{value:"all",children:"All Categories"}),z.map(q=>n.jsx("option",{value:q.name,children:q.name},q._id))]})]}),n.jsxs("div",{className:"list-table",children:[n.jsxs("div",{className:"list-table-format title",children:[n.jsx("span",{children:"Image"}),n.jsx("span",{children:"Food Details"}),n.jsx("span",{children:"Restaurant"}),n.jsx("span",{children:"Category"}),n.jsx("span",{children:"Price"}),n.jsx("span",{children:"Status"}),n.jsx("span",{children:"Actions"})]}),c.length>0?c.map((q,se)=>{var S;return n.jsxs("div",{className:"list-table-format",children:[n.jsx("img",{src:Rx(q.image,s),alt:q.name,className:"food-image",onError:V=>{console.error("Failed to load image:",q.image),V.target.src="/api/placeholder/50/50"}}),n.jsxs("div",{className:"food-info",children:[n.jsx("h4",{className:"food-name",children:q.name}),n.jsx("p",{className:"food-description",children:q.description||"No description available"})]}),n.jsx("span",{className:"food-restaurant",children:((S=q.restaurantId)==null?void 0:S.name)||"General Item"}),n.jsx("span",{className:"food-category",children:q.category}),n.jsxs("div",{className:"food-price-container",children:[q.isOnSale&&q.originalPrice?n.jsxs("div",{className:"price-with-discount",children:[n.jsxs("span",{className:"original-price",children:["₹",q.originalPrice]}),n.jsxs("span",{className:"discounted-price",children:["₹",q.price]}),n.jsxs("span",{className:"discount-badge-small",children:[q.discountPercentage,"% OFF"]})]}):n.jsxs("span",{className:"food-price",children:["₹",q.price]}),q.isPopular&&n.jsx("span",{className:"item-tag popular",children:"🔥 Popular"}),q.isFeatured&&n.jsx("span",{className:"item-tag featured",children:"⭐ Featured"})]}),n.jsx("span",{className:"badge badge-success",children:"Available"}),n.jsxs("div",{className:"food-actions",children:[n.jsx("button",{className:"action-btn edit-btn",title:"Edit Item",onClick:()=>ne(q),children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),n.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),n.jsx("button",{className:"action-btn delete-btn",title:"Delete Item",onClick:()=>Q(q._id),children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"3,6 5,6 21,6"}),n.jsx("path",{d:"M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"})]})})]})]},se)}):n.jsxs("div",{className:"no-items",children:[n.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"11",cy:"11",r:"8"}),n.jsx("path",{d:"M21 21l-4.35-4.35"})]}),n.jsx("h3",{children:"No items found"}),n.jsx("p",{children:"Try adjusting your search or filter criteria"})]})]})]}),b&&n.jsx("div",{className:"edit-modal-overlay",onClick:ce,children:n.jsxs("div",{className:"edit-modal",onClick:q=>q.stopPropagation(),children:[n.jsxs("div",{className:"edit-modal-header",children:[n.jsx("h3",{children:"Edit Food Item"}),n.jsx("button",{className:"close-btn",onClick:ce,children:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),n.jsxs("form",{onSubmit:X,className:"edit-form",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-name",children:"Food Name *"}),n.jsx("input",{type:"text",id:"edit-name",name:"name",value:C.name,onChange:re,placeholder:"Enter food name",required:!0})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-description",children:"Description"}),n.jsx("textarea",{id:"edit-description",name:"description",value:C.description,onChange:re,placeholder:"Enter food description",rows:"3"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-restaurant",children:"Restaurant"}),n.jsxs("select",{id:"edit-restaurant",name:"restaurantId",value:C.restaurantId,onChange:re,children:[n.jsx("option",{value:"",children:"No Restaurant (General Item)"}),U.map(q=>n.jsx("option",{value:q._id,children:q.name},q._id))]}),n.jsx("small",{children:"Select a restaurant to associate this food item with"})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-category",children:"Category *"}),n.jsxs("select",{id:"edit-category",name:"category",value:C.category,onChange:re,required:!0,children:[n.jsx("option",{value:"",children:"Select Category"}),z.map(q=>n.jsx("option",{value:q.name,children:q.name},q._id))]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-price",children:"Price (₹) *"}),n.jsx("input",{type:"number",id:"edit-price",name:"price",value:C.price,onChange:re,placeholder:"Enter price",min:"1",required:!0}),n.jsx("small",{children:"This is the original price before discount"})]})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-discount",children:"Discount (%)"}),n.jsx("input",{type:"number",id:"edit-discount",name:"discountPercentage",value:C.discountPercentage,onChange:re,placeholder:"0",min:"0",max:"100"}),n.jsx("small",{children:"Enter 0 to remove discount"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-discount-label",children:"Discount Label"}),n.jsx("input",{type:"text",id:"edit-discount-label",name:"discountLabel",value:C.discountLabel,onChange:re,placeholder:"e.g., MEGA SALE, LIMITED TIME"})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-tags",children:"Tags"}),n.jsx("input",{type:"text",id:"edit-tags",name:"tags",value:C.tags,onChange:re,placeholder:"e.g., Bestseller, New, Spicy (comma separated)"})]}),n.jsxs("div",{className:"form-row",children:[n.jsx("div",{className:"form-group checkbox-group",children:n.jsxs("label",{className:"checkbox-label",children:[n.jsx("input",{type:"checkbox",name:"isPopular",checked:C.isPopular,onChange:re}),n.jsx("span",{className:"checkmark"}),"Mark as Popular Item"]})}),n.jsx("div",{className:"form-group checkbox-group",children:n.jsxs("label",{className:"checkbox-label",children:[n.jsx("input",{type:"checkbox",name:"isFeatured",checked:C.isFeatured,onChange:re}),n.jsx("span",{className:"checkmark"}),"Mark as Featured Item"]})})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-image",children:"Update Image (Optional)"}),n.jsxs("div",{className:"image-upload-section",children:[R&&n.jsxs("div",{className:"current-image",children:[n.jsx("img",{src:R,alt:"Food preview",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"8px"}}),n.jsx("p",{children:"New image uploaded to Cloudinary"})]}),n.jsx("input",{type:"file",id:"edit-image",name:"image",onChange:re,accept:"image/*"}),C.image&&!R&&n.jsx("div",{className:"upload-actions",children:n.jsx("button",{type:"button",onClick:()=>ye(C.image),disabled:T,className:"upload-btn",style:{marginTop:"10px",padding:"8px 16px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:T?"not-allowed":"pointer"},children:T?"Uploading...":"Upload to Cloudinary"})}),n.jsx("small",{children:R?"Image uploaded to Cloudinary successfully":"Select an image and click 'Upload to Cloudinary' for faster loading, or leave empty to keep current image"})]})]}),n.jsxs("div",{className:"form-actions",children:[n.jsx("button",{type:"button",className:"btn btn-secondary",onClick:ce,children:"Cancel"}),n.jsx("button",{type:"submit",className:"btn btn-primary",children:"Update Item"})]})]})]})})]})},Dx=s=>`₹${s}`,Mx=({url:s})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,p]=E.useState("all"),[g,v]=E.useState(null),[m,x]=E.useState(!1),b=E.useCallback(async()=>{var N,R;try{d(!0);const W=await me.get(`${s}/api/order/list`);if(W.data.success){const P=(W.data.data||[]).sort((F,ee)=>new Date(ee.date)-new Date(F.date));u(P)}else Y.error(W.data.message||"Failed to load orders")}catch(W){console.log(W),Y.error(((R=(N=W.response)==null?void 0:N.data)==null?void 0:R.message)||"An error occurred while fetching orders")}finally{d(!1)}},[s]),_=async(N,R)=>{try{(await me.post(s+"/api/order/status",{orderId:R,status:N.target.value})).data.success?(Y.success("Status updated successfully"),b()):Y.error("Failed to update status")}catch(W){console.error("Error updating status:",W),Y.error("An error occurred while updating status")}},U=async N=>{if(window.confirm("Are you sure you want to delete this order? This action cannot be undone."))try{const R=await me.post(s+"/api/order/delete",{orderId:N});R.data.success?(Y.success("Order deleted successfully"),b()):Y.error(R.data.message||"Failed to delete order")}catch(R){console.error("Error deleting order:",R),Y.error("An error occurred while deleting order")}},w=N=>{v({_id:N._id,status:N.status,amount:N.amount,address:{...N.address}}),x(!0)},z=async()=>{try{const N=await me.post(s+"/api/order/edit",{orderId:g._id,status:g.status,amount:g.amount,address:g.address});N.data.success?(Y.success("Order updated successfully"),x(!1),v(null),b()):Y.error(N.data.message||"Failed to update order")}catch(N){console.error("Error updating order:",N),Y.error("An error occurred while updating order")}};E.useEffect(()=>{b()},[b]);const D=r.filter(N=>f==="all"?!0:N.status.toLowerCase().replace(" ","_")===f),C={total:r.length,processing:r.filter(N=>N.status==="Food Processing").length,delivery:r.filter(N=>N.status==="Out for delivery").length,delivered:r.filter(N=>N.status==="Delivered").length},A=N=>{switch(N){case"Food Processing":return"processing";case"Out for delivery":return"delivery";case"Delivered":return"delivered";default:return"processing"}},T=N=>{const R=new Date(N);return R.toLocaleDateString()+" "+R.toLocaleTimeString()};return c?n.jsx("div",{className:"orders",children:n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading orders..."})]})}):n.jsxs("div",{className:"orders",children:[n.jsxs("div",{className:"card",children:[n.jsx("div",{className:"card-header",children:n.jsxs("div",{children:[n.jsx("h2",{className:"card-title",children:"Orders Management"}),n.jsx("p",{className:"card-subtitle",children:"Track and manage customer orders"})]})}),n.jsxs("div",{className:"orders-stats",children:[n.jsxs("div",{className:"orders-stat-card",children:[n.jsx("div",{className:"stat-icon total",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),n.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:C.total}),n.jsx("p",{children:"Total Orders"})]})]}),n.jsxs("div",{className:"orders-stat-card",children:[n.jsx("div",{className:"stat-icon processing",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("polyline",{points:"12,6 12,12 16,14"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:C.processing}),n.jsx("p",{children:"Processing"})]})]}),n.jsxs("div",{className:"orders-stat-card",children:[n.jsx("div",{className:"stat-icon delivery",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M1 3h15l-1 9H2l2-7z"}),n.jsx("path",{d:"M16 8v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V8"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:C.delivery}),n.jsx("p",{children:"Out for Delivery"})]})]}),n.jsxs("div",{className:"orders-stat-card",children:[n.jsx("div",{className:"stat-icon delivered",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"9,11 12,14 22,4"}),n.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:C.delivered}),n.jsx("p",{children:"Delivered"})]})]})]}),n.jsx("div",{className:"orders-filters",children:n.jsxs("select",{value:f,onChange:N=>p(N.target.value),className:"filter-select",children:[n.jsxs("option",{value:"all",children:["All Orders (",C.total,")"]}),n.jsxs("option",{value:"food_processing",children:["Processing (",C.processing,")"]}),n.jsxs("option",{value:"out_for_delivery",children:["Out for Delivery (",C.delivery,")"]}),n.jsxs("option",{value:"delivered",children:["Delivered (",C.delivered,")"]})]})}),n.jsx("div",{className:"orders-list",children:D.length>0?D.map((N,R)=>n.jsxs("div",{className:`order-item ${A(N.status)}`,children:[n.jsxs("div",{className:"order-header",children:[n.jsxs("div",{children:[n.jsxs("h3",{className:"order-id",children:["Order #",N._id.slice(-6)]}),n.jsx("p",{className:"order-time",children:T(N.date)})]}),n.jsx("span",{className:`order-status-badge ${A(N.status)}`,children:N.status})]}),n.jsxs("div",{className:"order-content",children:[n.jsx("div",{className:"order-icon",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),n.jsx("circle",{cx:"12",cy:"10",r:"3"})]})}),n.jsxs("div",{className:"order-details",children:[n.jsx("p",{className:"order-item-food",children:N.items.map((W,P)=>P===N.items.length-1?W.name+" x "+W.quantity:W.name+" x "+W.quantity+", ")}),n.jsx("h4",{className:"order-item-name",children:N.address.firstName+" "+N.address.lastName}),n.jsxs("div",{className:"order-item-address",children:[n.jsxs("p",{children:[N.address.street,","]}),n.jsxs("p",{children:[N.address.city,", ",N.address.state,", ",N.address.zipCode]})]}),n.jsxs("p",{className:"order-item-phone",children:["📞 ",N.address.phone]})]}),n.jsxs("div",{className:"order-summary",children:[n.jsxs("p",{className:"order-items-count",children:["Items: ",N.items.length]}),n.jsx("p",{className:"order-amount",children:Dx(N.amount)})]}),n.jsxs("div",{className:"order-actions",children:[n.jsxs("select",{onChange:W=>_(W,N._id),value:N.status,className:"status-select",children:[n.jsx("option",{value:"Food Processing",children:"Food Processing"}),n.jsx("option",{value:"Out for delivery",children:"Out for delivery"}),n.jsx("option",{value:"Delivered",children:"Delivered"})]}),n.jsx("button",{className:"edit-order-btn",onClick:()=>w(N),title:"Edit Order",children:"✏️ Edit"}),n.jsx("button",{className:"delete-order-btn",onClick:()=>U(N._id),title:"Delete Order",children:"🗑️ Delete"})]})]})]},R)):n.jsxs("div",{className:"no-orders",children:[n.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),n.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]}),n.jsx("h3",{children:"No orders found"}),n.jsx("p",{children:"No orders match the selected filter criteria"})]})})]}),m&&g&&n.jsx("div",{className:"modal-overlay",children:n.jsxs("div",{className:"edit-modal",children:[n.jsxs("div",{className:"modal-header",children:[n.jsxs("h3",{children:["Edit Order #",g._id.slice(-6)]}),n.jsx("button",{className:"close-modal-btn",onClick:()=>{x(!1),v(null)},children:"✕"})]}),n.jsxs("div",{className:"modal-content",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Order Status:"}),n.jsxs("select",{value:g.status,onChange:N=>v({...g,status:N.target.value}),className:"edit-select",children:[n.jsx("option",{value:"Food Processing",children:"Food Processing"}),n.jsx("option",{value:"Out for delivery",children:"Out for delivery"}),n.jsx("option",{value:"Delivered",children:"Delivered"})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Order Amount (₹):"}),n.jsx("input",{type:"number",value:g.amount,onChange:N=>v({...g,amount:parseFloat(N.target.value)}),className:"edit-input",min:"0",step:"0.01"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Customer Name:"}),n.jsxs("div",{className:"name-inputs",children:[n.jsx("input",{type:"text",placeholder:"First Name",value:g.address.firstName,onChange:N=>v({...g,address:{...g.address,firstName:N.target.value}}),className:"edit-input"}),n.jsx("input",{type:"text",placeholder:"Last Name",value:g.address.lastName,onChange:N=>v({...g,address:{...g.address,lastName:N.target.value}}),className:"edit-input"})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Phone Number:"}),n.jsx("input",{type:"text",value:g.address.phone,onChange:N=>v({...g,address:{...g.address,phone:N.target.value}}),className:"edit-input"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Delivery Address:"}),n.jsx("input",{type:"text",placeholder:"Street",value:g.address.street,onChange:N=>v({...g,address:{...g.address,street:N.target.value}}),className:"edit-input"}),n.jsxs("div",{className:"address-inputs",children:[n.jsx("input",{type:"text",placeholder:"City",value:g.address.city,onChange:N=>v({...g,address:{...g.address,city:N.target.value}}),className:"edit-input"}),n.jsx("input",{type:"text",placeholder:"State",value:g.address.state,onChange:N=>v({...g,address:{...g.address,state:N.target.value}}),className:"edit-input"}),n.jsx("input",{type:"text",placeholder:"ZIP Code",value:g.address.zipCode,onChange:N=>v({...g,address:{...g.address,zipCode:N.target.value}}),className:"edit-input"})]})]})]}),n.jsxs("div",{className:"modal-actions",children:[n.jsx("button",{className:"cancel-btn",onClick:()=>{x(!1),v(null)},children:"Cancel"}),n.jsx("button",{className:"save-btn",onClick:z,children:"Save Changes"})]})]})})]})},zx=({url:s})=>{const[r,u]=E.useState({salesTrend:[],categoryStats:[],orderStats:{total:0,completed:0,pending:0,cancelled:0},revenueStats:{today:0,week:0,month:0,total:0},topItems:[],customerStats:{newCustomers:0,returningCustomers:0,totalCustomers:0}}),[c,d]=E.useState(!0),[f,p]=E.useState("week");E.useEffect(()=>{g()},[f]);const g=async()=>{try{d(!0);const x=await me.get(`${s}/api/order/list`),b=x.data.success?x.data.data:[],_=await me.get(`${s}/api/food/list`),U=_.data.success?_.data.data:[];v(b,U)}catch(x){console.error("Error fetching analytics data:",x)}finally{d(!1)}},v=(x,b)=>{const _=new Date,U=new Date(_.getFullYear(),_.getMonth(),_.getDate()),w=new Date(U.getTime()-7*24*60*60*1e3),z=new Date(U.getTime()-30*24*60*60*1e3),D={total:x.length,completed:x.filter(Q=>Q.status==="Delivered").length,pending:x.filter(Q=>Q.status==="Food Processing"||Q.status==="Out for delivery").length,cancelled:0},C=x.filter(Q=>new Date(Q.date)>=U),A=x.filter(Q=>new Date(Q.date)>=w),T=x.filter(Q=>new Date(Q.date)>=z),N={today:C.reduce((Q,$)=>Q+$.amount,0),week:A.reduce((Q,$)=>Q+$.amount,0),month:T.reduce((Q,$)=>Q+$.amount,0),total:x.reduce((Q,$)=>Q+$.amount,0)},R={};b.forEach(Q=>{R[Q.category]||(R[Q.category]={count:0,revenue:0}),R[Q.category].count++}),x.forEach(Q=>{Q.items.forEach($=>{const ne=b.find(ce=>ce.name===$.name);ne&&R[ne.category]&&(R[ne.category].revenue+=$.price*$.quantity)})});const W=Object.entries(R).map(([Q,$])=>({category:Q,count:$.count,revenue:$.revenue,percentage:($.revenue/N.total*100).toFixed(1)})).sort((Q,$)=>$.revenue-Q.revenue),P=[];for(let Q=6;Q>=0;Q--){const $=new Date(U.getTime()-Q*24*60*60*1e3),ne=x.filter(ce=>new Date(ce.date).toDateString()===$.toDateString());P.push({date:$.toLocaleDateString("en-US",{weekday:"short"}),orders:ne.length,revenue:ne.reduce((ce,re)=>ce+re.amount,0)})}const F={};x.forEach(Q=>{Q.items.forEach($=>{F[$.name]||(F[$.name]={quantity:0,revenue:0}),F[$.name].quantity+=$.quantity,F[$.name].revenue+=$.price*$.quantity})});const ee=Object.entries(F).map(([Q,$])=>({name:Q,...$})).sort((Q,$)=>$.quantity-Q.quantity).slice(0,5);u({salesTrend:P,categoryStats:W,orderStats:D,revenueStats:N,topItems:ee,customerStats:{newCustomers:Math.floor(x.length*.3),returningCustomers:Math.floor(x.length*.7),totalCustomers:x.length}})},m=x=>`₹${x}`;return c?n.jsx("div",{className:"analytics",children:n.jsxs("div",{className:"analytics-loading",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading analytics..."})]})}):n.jsxs("div",{className:"analytics",children:[n.jsxs("div",{className:"analytics-header",children:[n.jsxs("div",{children:[n.jsx("h1",{children:"Analytics Dashboard"}),n.jsx("p",{children:"Detailed insights into your business performance"})]}),n.jsxs("div",{className:"time-range-selector",children:[n.jsx("button",{className:f==="week"?"active":"",onClick:()=>p("week"),children:"Week"}),n.jsx("button",{className:f==="month"?"active":"",onClick:()=>p("month"),children:"Month"}),n.jsx("button",{className:f==="year"?"active":"",onClick:()=>p("year"),children:"Year"})]})]}),n.jsxs("div",{className:"metrics-grid",children:[n.jsxs("div",{className:"metric-card",children:[n.jsx("div",{className:"metric-icon revenue",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"12",y1:"1",x2:"12",y2:"23"}),n.jsx("path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})]})}),n.jsxs("div",{className:"metric-info",children:[n.jsx("h3",{children:m(r.revenueStats.total)}),n.jsx("p",{children:"Total Revenue"}),n.jsx("span",{className:"metric-change positive",children:"+12.5%"})]})]}),n.jsxs("div",{className:"metric-card",children:[n.jsx("div",{className:"metric-icon orders",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),n.jsx("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]})}),n.jsxs("div",{className:"metric-info",children:[n.jsx("h3",{children:r.orderStats.total}),n.jsx("p",{children:"Total Orders"}),n.jsx("span",{className:"metric-change positive",children:"+8.2%"})]})]}),n.jsxs("div",{className:"metric-card",children:[n.jsx("div",{className:"metric-icon customers",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"9",cy:"7",r:"4"}),n.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),n.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),n.jsxs("div",{className:"metric-info",children:[n.jsx("h3",{children:r.customerStats.totalCustomers}),n.jsx("p",{children:"Total Customers"}),n.jsx("span",{className:"metric-change positive",children:"+15.3%"})]})]}),n.jsxs("div",{className:"metric-card",children:[n.jsx("div",{className:"metric-icon completion",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"9,11 12,14 22,4"}),n.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})}),n.jsxs("div",{className:"metric-info",children:[n.jsxs("h3",{children:[(r.orderStats.completed/r.orderStats.total*100).toFixed(1),"%"]}),n.jsx("p",{children:"Completion Rate"}),n.jsx("span",{className:"metric-change positive",children:"+2.1%"})]})]})]}),n.jsxs("div",{className:"charts-section",children:[n.jsx("div",{className:"chart-container",children:n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Sales Trend"}),n.jsx("p",{className:"card-subtitle",children:"Daily sales over the last week"})]}),n.jsx("div",{className:"sales-chart",children:r.salesTrend.map((x,b)=>n.jsxs("div",{className:"chart-bar",children:[n.jsx("div",{className:"bar",style:{height:`${x.revenue/Math.max(...r.salesTrend.map(_=>_.revenue))*100}%`}}),n.jsx("span",{className:"bar-label",children:x.date}),n.jsx("span",{className:"bar-value",children:m(x.revenue)})]},b))})]})}),n.jsx("div",{className:"chart-container",children:n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Category Performance"}),n.jsx("p",{className:"card-subtitle",children:"Revenue by food category"})]}),n.jsx("div",{className:"category-chart",children:r.categoryStats.slice(0,5).map((x,b)=>n.jsxs("div",{className:"category-item",children:[n.jsxs("div",{className:"category-info",children:[n.jsx("span",{className:"category-name",children:x.category}),n.jsx("span",{className:"category-revenue",children:m(x.revenue)})]}),n.jsx("div",{className:"category-bar",children:n.jsx("div",{className:"category-fill",style:{width:`${x.percentage}%`}})}),n.jsxs("span",{className:"category-percentage",children:[x.percentage,"%"]})]},b))})]})})]}),n.jsxs("div",{className:"bottom-section",children:[n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Top Selling Items"}),n.jsx("p",{className:"card-subtitle",children:"Most popular food items"})]}),n.jsxs("div",{className:"top-items-table",children:[n.jsxs("div",{className:"table-header",children:[n.jsx("span",{children:"Rank"}),n.jsx("span",{children:"Item Name"}),n.jsx("span",{children:"Quantity Sold"}),n.jsx("span",{children:"Revenue"})]}),r.topItems.map((x,b)=>n.jsxs("div",{className:"table-row",children:[n.jsxs("span",{className:"rank",children:["#",b+1]}),n.jsx("span",{className:"item-name",children:x.name}),n.jsx("span",{className:"quantity",children:x.quantity}),n.jsx("span",{className:"revenue",children:m(x.revenue)})]},b))]})]}),n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Order Status"}),n.jsx("p",{className:"card-subtitle",children:"Current order distribution"})]}),n.jsxs("div",{className:"order-status",children:[n.jsxs("div",{className:"status-item",children:[n.jsx("div",{className:"status-circle completed"}),n.jsxs("div",{className:"status-info",children:[n.jsx("span",{className:"status-count",children:r.orderStats.completed}),n.jsx("span",{className:"status-label",children:"Completed"})]})]}),n.jsxs("div",{className:"status-item",children:[n.jsx("div",{className:"status-circle pending"}),n.jsxs("div",{className:"status-info",children:[n.jsx("span",{className:"status-count",children:r.orderStats.pending}),n.jsx("span",{className:"status-label",children:"Pending"})]})]}),n.jsxs("div",{className:"status-item",children:[n.jsx("div",{className:"status-circle cancelled"}),n.jsxs("div",{className:"status-info",children:[n.jsx("span",{className:"status-count",children:r.orderStats.cancelled}),n.jsx("span",{className:"status-label",children:"Cancelled"})]})]})]})]})]})]})},Ux=async(s,r="eatzone/categories")=>{var u;try{if(!s)throw new Error("No file provided");if(!s.type.startsWith("image/"))throw new Error("Only image files are allowed");const c=5*1024*1024;if(s.size>c)throw new Error("File size must be less than 5MB");console.log("Uploading category image to Cloudinary:",{name:s.name,size:s.size,type:s.type,folder:r});const d=new FormData;d.append("file",s),d.append("upload_preset","eatzone_admin"),d.append("folder",r),d.append("tags","category,menu");const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const g=await f.json();throw console.error("Cloudinary upload error:",g),new Error(((u=g.error)==null?void 0:u.message)||`Upload failed with status ${f.status}`)}const p=await f.json();return console.log("Cloudinary upload successful:",p),{success:!0,url:p.secure_url,publicId:p.public_id}}catch(c){return console.error("Cloudinary upload error:",c),{success:!1,error:c.message}}},Bx=(s,r)=>s?s.startsWith("http")?s:`${r}/images/${s}`:cp("default"),cp=s=>{const r={Rolls:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/rolls.jpg",Salad:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/salad.jpg",Deserts:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/desserts.jpg",Sandwich:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sandwich.jpg",Cake:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/cake.jpg",Veg:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/veg.jpg",Pizza:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pizza.jpg",Pasta:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pasta.jpg",Noodles:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/noodles.jpg","Main Course":"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/main-course.jpg",Appetizer:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/appetizer.jpg",Sushi:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sushi.jpg",default:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/default-food.jpg"};return r[s]||r.default},Lx=({url:s})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!1),[f,p]=E.useState(!1),[g,v]=E.useState(null),[m,x]=E.useState({name:"",description:"",image:null,order:0,isActive:!0}),[b,_]=E.useState(""),[U,w]=E.useState(""),[z,D]=E.useState(!1),C=async()=>{var Q;try{d(!0),console.log("🔄 Fetching categories from:",`${s}/api/category/list-all`);const $=await me.get(`${s}/api/category/list-all`,{timeout:1e4,headers:{Accept:"application/json","Content-Type":"application/json"}});console.log("📦 Categories response:",$),$.data.success?(u($.data.data),console.log("✅ Categories loaded successfully:",$.data.data.length,"categories"),$.data.data.length===0&&Y.info("No categories found. You can add your first category!")):(console.error("❌ API returned error:",$.data.message),Y.error(`Failed to fetch categories: ${$.data.message}`))}catch($){console.error("❌ Error fetching categories:",$),$.code==="ECONNABORTED"?Y.error("Request timeout - Server might be slow or unreachable"):$.response?Y.error(`Server error: ${$.response.status} - ${((Q=$.response.data)==null?void 0:Q.message)||"Unknown error"}`):$.request?Y.error("Network error - Cannot reach server. Check your connection and server status."):Y.error(`Error: ${$.message}`)}finally{d(!1)}};E.useEffect(()=>{C()},[s]);const A=Q=>{const{name:$,value:ne,type:ce,checked:re}=Q.target;x(ye=>({...ye,[$]:ce==="checkbox"?re:ne}))},T=async Q=>{const $=Q.target.files[0];if($){x(ce=>({...ce,image:$}));const ne=new FileReader;ne.onloadend=()=>{_(ne.result)},ne.readAsDataURL($),D(!0),Y.info("Uploading image to Cloudinary...");try{const ce=await Ux($,"eatzone/categories");ce.success?(console.log("Category image uploaded successfully:",ce.url),w(ce.url),_(ce.url),Y.success("Image uploaded successfully!")):(console.error("Cloudinary upload failed:",ce.error),Y.error(ce.error||"Failed to upload image"))}catch(ce){console.error("Image upload error:",ce),Y.error("Failed to upload image")}finally{D(!1)}}},N=()=>{x({name:"",description:"",image:null,order:0,isActive:!0}),_(""),w(""),v(null),D(!1)},R=()=>{N(),p(!0)},W=Q=>{v(Q),x({name:Q.name,description:Q.description||"",image:null,order:Q.order||0,isActive:Q.isActive});const $=Q.image.startsWith("http")?Q.image:`${s}/images/${Q.image}`;_($),Q.image.startsWith("http")?w(Q.image):w(""),p(!0)},P=()=>{p(!1),N()},F=async Q=>{var $,ne;if(Q.preventDefault(),!m.name.trim()){Y.error("Category name is required");return}if(!g&&!U){Y.error("Category image is required. Please upload an image.");return}if(z){Y.error("Please wait for image upload to complete");return}try{d(!0);const ce=new FormData;ce.append("name",m.name.trim()),ce.append("description",m.description.trim()),ce.append("order",m.order),ce.append("isActive",m.isActive),U?ce.append("image",U):g&&g.image&&ce.append("image",g.image);let re;if(g?(ce.append("id",g._id),re=await me.post(`${s}/api/category/update`,ce)):re=await me.post(`${s}/api/category/add`,ce),re.data.success){Y.success(g?"Category updated successfully!":"Category added successfully!"),C(),P();try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"CATEGORY_UPDATED"},"*"),window.dispatchEvent(new CustomEvent("categoryUpdated"))}catch(ye){console.log("Could not notify client of category update:",ye)}}else Y.error(re.data.message||"Operation failed")}catch(ce){console.error("Error submitting category:",ce),Y.error(((ne=($=ce.response)==null?void 0:$.data)==null?void 0:ne.message)||"Error submitting category")}finally{d(!1)}},ee=async Q=>{if(window.confirm("Are you sure you want to delete this category?"))try{if(d(!0),(await me.post(`${s}/api/category/remove`,{id:Q})).data.success){Y.success("Category deleted successfully!"),C();try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"CATEGORY_UPDATED"},"*"),window.dispatchEvent(new CustomEvent("categoryUpdated"))}catch(ne){console.log("Could not notify client of category update:",ne)}}else Y.error("Failed to delete category")}catch($){console.error("Error deleting category:",$),Y.error("Error deleting category")}finally{d(!1)}};return n.jsxs("div",{className:"categories-container",children:[n.jsxs("div",{className:"categories-header",children:[n.jsx("h2",{children:"Food Categories Management"}),n.jsx("button",{className:"add-btn",onClick:R,children:"+ Add New Category"})]}),c&&n.jsx("div",{className:"loading",children:"Loading..."}),n.jsx("div",{className:"categories-grid",children:r.map(Q=>n.jsxs("div",{className:`category-card ${Q.isActive?"":"inactive"}`,children:[n.jsx("div",{className:"category-image",children:n.jsx("img",{src:Bx(Q.image,s),alt:Q.name,onError:$=>{console.log(`Failed to load admin image for ${Q.name}:`,Q.image),$.target.src=cp(Q.name)},onLoad:()=>{console.log(`Successfully loaded admin image for ${Q.name}`)}})}),n.jsxs("div",{className:"category-info",children:[n.jsx("h3",{children:Q.name}),n.jsx("p",{children:Q.description}),n.jsxs("div",{className:"category-meta",children:[n.jsxs("span",{children:["Order: ",Q.order]}),n.jsx("span",{className:`status ${Q.isActive?"active":"inactive"}`,children:Q.isActive?"Active":"Inactive"})]})]}),n.jsxs("div",{className:"category-actions",children:[n.jsx("button",{className:"edit-btn",onClick:()=>W(Q),children:"Edit"}),n.jsx("button",{className:"delete-btn",onClick:()=>ee(Q._id),children:"Delete"})]})]},Q._id))}),r.length===0&&!c&&n.jsx("div",{className:"no-categories",children:n.jsx("p",{children:"No categories found. Add your first category!"})}),f&&n.jsx("div",{className:"modal-overlay",onClick:P,children:n.jsxs("div",{className:"modal-content",onClick:Q=>Q.stopPropagation(),children:[n.jsxs("div",{className:"modal-header",children:[n.jsx("h3",{children:g?"Edit Category":"Add New Category"}),n.jsx("button",{className:"close-btn",onClick:P,children:"×"})]}),n.jsxs("form",{onSubmit:F,className:"category-form",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Category Name *"}),n.jsx("input",{type:"text",name:"name",value:m.name,onChange:A,placeholder:"Enter category name",required:!0})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Description"}),n.jsx("textarea",{name:"description",value:m.description,onChange:A,placeholder:"Enter category description",rows:"3"})]}),n.jsxs("div",{className:"form-group",children:[n.jsxs("label",{children:["Category Image ",!g&&"*"]}),n.jsx("input",{type:"file",accept:"image/*",onChange:T,disabled:z}),z&&n.jsx("div",{className:"upload-status",children:n.jsx("p",{children:"🔄 Uploading image to Cloudinary..."})}),b&&!z&&n.jsxs("div",{className:"image-preview",children:[n.jsx("img",{src:b,alt:"Preview",onError:Q=>{Q.target.src="/placeholder-food.jpg"}}),U&&n.jsxs("div",{className:"upload-success",children:[n.jsx("span",{className:"success-text",children:"✅ Image uploaded to Cloudinary!"}),n.jsxs("small",{children:["URL: ",U]})]})]}),n.jsx("small",{className:"form-help",children:"Recommended: 400x400px, JPG or PNG, max 5MB. Images will be automatically uploaded to Cloudinary."})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{children:"Display Order"}),n.jsx("input",{type:"number",name:"order",value:m.order,onChange:A,min:"0"})]}),n.jsx("div",{className:"form-group",children:n.jsxs("label",{className:"checkbox-label",children:[n.jsx("input",{type:"checkbox",name:"isActive",checked:m.isActive,onChange:A}),"Active"]})})]}),n.jsxs("div",{className:"form-actions",children:[n.jsx("button",{type:"button",onClick:P,className:"cancel-btn",children:"Cancel"}),n.jsx("button",{type:"submit",className:"submit-btn",disabled:c,children:c?"Saving...":g?"Update Category":"Add Category"})]})]})]})})]})},kx=({url:s})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,p]=E.useState(!1),[g,v]=E.useState(null),[m,x]=E.useState({name:"",phone:"",email:"",vehicleType:"bike",vehicleNumber:"",status:"available"}),b=[{_id:"1",name:"Rajesh Kumar",phone:"+91 9876543210",email:"<EMAIL>",vehicleType:"bike",vehicleNumber:"MH12AB1234",status:"available",assignedOrders:0,completedOrders:45,rating:4.8,joinDate:"2024-01-15"},{_id:"2",name:"Priya Sharma",phone:"+91 9876543211",email:"<EMAIL>",vehicleType:"scooter",vehicleNumber:"MH12CD5678",status:"busy",assignedOrders:2,completedOrders:67,rating:4.9,joinDate:"2024-02-20"},{_id:"3",name:"Amit Patel",phone:"+91 9876543212",email:"<EMAIL>",vehicleType:"bike",vehicleNumber:"MH12EF9012",status:"offline",assignedOrders:0,completedOrders:23,rating:4.6,joinDate:"2024-03-10"},{_id:"4",name:"Sneha Reddy",phone:"+91 9876543213",email:"<EMAIL>",vehicleType:"scooter",vehicleNumber:"MH12GH3456",status:"available",assignedOrders:0,completedOrders:89,rating:4.7,joinDate:"2024-01-05"}];E.useEffect(()=>{_()},[]);const _=async()=>{try{d(!0),setTimeout(()=>{u(b),d(!1)},1e3)}catch(N){console.error("Error fetching delivery partners:",N),Y.error("Failed to load delivery partners"),d(!1)}},U=N=>{const{name:R,value:W}=N.target;x(P=>({...P,[R]:W}))},w=async N=>{N.preventDefault();try{if(g){const R=r.map(W=>W._id===g._id?{...W,...m}:W);u(R),Y.success("Partner updated successfully")}else{const R={_id:Date.now().toString(),...m,assignedOrders:0,completedOrders:0,rating:0,joinDate:new Date().toISOString().split("T")[0]};u(W=>[...W,R]),Y.success("Partner added successfully")}z()}catch(R){console.error("Error saving partner:",R),Y.error("Failed to save partner")}},z=()=>{x({name:"",phone:"",email:"",vehicleType:"bike",vehicleNumber:"",status:"available"}),p(!1),v(null)},D=N=>{v(N),x({name:N.name,phone:N.phone,email:N.email,vehicleType:N.vehicleType,vehicleNumber:N.vehicleNumber,status:N.status}),p(!0)},C=async N=>{if(window.confirm("Are you sure you want to delete this partner?"))try{u(R=>R.filter(W=>W._id!==N)),Y.success("Partner deleted successfully")}catch(R){console.error("Error deleting partner:",R),Y.error("Failed to delete partner")}},A=async(N,R)=>{try{const W=r.map(P=>P._id===N?{...P,status:R}:P);u(W),Y.success("Status updated successfully")}catch(W){console.error("Error updating status:",W),Y.error("Failed to update status")}},T=N=>{switch(N){case"available":return n.jsx("div",{className:"status-dot available"});case"busy":return n.jsx("div",{className:"status-dot busy"});case"offline":return n.jsx("div",{className:"status-dot offline"});default:return n.jsx("div",{className:"status-dot"})}};return c?n.jsx("div",{className:"delivery-partners",children:n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading delivery partners..."})]})}):n.jsxs("div",{className:"delivery-partners",children:[n.jsxs("div",{className:"page-header",children:[n.jsxs("div",{children:[n.jsx("h1",{children:"Delivery Partners"}),n.jsx("p",{children:"Manage your delivery team and track their performance"})]}),n.jsxs("button",{className:"btn btn-primary",onClick:()=>p(!0),children:[n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),n.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]}),"Add Partner"]})]}),n.jsxs("div",{className:"stats-grid",children:[n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon available",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"9",cy:"7",r:"4"}),n.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),n.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(N=>N.status==="available").length}),n.jsx("p",{children:"Available"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon busy",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("polyline",{points:"12,6 12,12 16,14"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(N=>N.status==="busy").length}),n.jsx("p",{children:"Busy"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon offline",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"4.93",y1:"4.93",x2:"19.07",y2:"19.07"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(N=>N.status==="offline").length}),n.jsx("p",{children:"Offline"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon total",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),n.jsx("circle",{cx:"9",cy:"7",r:"4"}),n.jsx("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),n.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.length}),n.jsx("p",{children:"Total Partners"})]})]})]}),n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"All Delivery Partners"}),n.jsx("p",{className:"card-subtitle",children:"Manage and monitor your delivery team"})]}),n.jsxs("div",{className:"partners-table",children:[n.jsxs("div",{className:"table-header",children:[n.jsx("span",{children:"Partner"}),n.jsx("span",{children:"Contact"}),n.jsx("span",{children:"Vehicle"}),n.jsx("span",{children:"Status"}),n.jsx("span",{children:"Orders"}),n.jsx("span",{children:"Rating"}),n.jsx("span",{children:"Actions"})]}),r.map(N=>n.jsxs("div",{className:"table-row",children:[n.jsxs("div",{className:"partner-info",children:[n.jsx("div",{className:"partner-avatar",children:N.name.charAt(0).toUpperCase()}),n.jsxs("div",{children:[n.jsx("h4",{children:N.name}),n.jsxs("p",{children:["Joined ",new Date(N.joinDate).toLocaleDateString()]})]})]}),n.jsxs("div",{className:"contact-info",children:[n.jsx("p",{children:N.phone}),n.jsx("p",{children:N.email})]}),n.jsxs("div",{className:"vehicle-info",children:[n.jsx("p",{children:N.vehicleType.charAt(0).toUpperCase()+N.vehicleType.slice(1)}),n.jsx("p",{children:N.vehicleNumber})]}),n.jsxs("div",{className:"status-info",children:[T(N.status),n.jsxs("select",{value:N.status,onChange:R=>A(N._id,R.target.value),className:"status-select",children:[n.jsx("option",{value:"available",children:"Available"}),n.jsx("option",{value:"busy",children:"Busy"}),n.jsx("option",{value:"offline",children:"Offline"})]})]}),n.jsxs("div",{className:"orders-info",children:[n.jsxs("p",{children:["Active: ",N.assignedOrders]}),n.jsxs("p",{children:["Completed: ",N.completedOrders]})]}),n.jsx("div",{className:"rating-info",children:n.jsxs("div",{className:"rating",children:[n.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:n.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})}),N.rating]})}),n.jsxs("div",{className:"actions",children:[n.jsx("button",{className:"btn-icon edit",onClick:()=>D(N),title:"Edit Partner",children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),n.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),n.jsx("button",{className:"btn-icon delete",onClick:()=>C(N._id),title:"Delete Partner",children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"3,6 5,6 21,6"}),n.jsx("path",{d:"M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"})]})})]})]},N._id))]})]}),f&&n.jsx("div",{className:"modal-overlay",onClick:z,children:n.jsxs("div",{className:"modal",onClick:N=>N.stopPropagation(),children:[n.jsxs("div",{className:"modal-header",children:[n.jsx("h3",{children:g?"Edit Partner":"Add New Partner"}),n.jsx("button",{className:"modal-close",onClick:z,children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),n.jsxs("form",{onSubmit:w,className:"modal-form",children:[n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Name *"}),n.jsx("input",{type:"text",name:"name",value:m.name,onChange:U,className:"form-input",required:!0})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Phone *"}),n.jsx("input",{type:"tel",name:"phone",value:m.phone,onChange:U,className:"form-input",required:!0})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Email *"}),n.jsx("input",{type:"email",name:"email",value:m.email,onChange:U,className:"form-input",required:!0})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Vehicle Type *"}),n.jsxs("select",{name:"vehicleType",value:m.vehicleType,onChange:U,className:"form-select",required:!0,children:[n.jsx("option",{value:"bike",children:"Bike"}),n.jsx("option",{value:"scooter",children:"Scooter"}),n.jsx("option",{value:"car",children:"Car"})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Vehicle Number *"}),n.jsx("input",{type:"text",name:"vehicleNumber",value:m.vehicleNumber,onChange:U,className:"form-input",required:!0})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Status"}),n.jsxs("select",{name:"status",value:m.status,onChange:U,className:"form-select",children:[n.jsx("option",{value:"available",children:"Available"}),n.jsx("option",{value:"busy",children:"Busy"}),n.jsx("option",{value:"offline",children:"Offline"})]})]}),n.jsxs("div",{className:"modal-actions",children:[n.jsx("button",{type:"button",className:"btn btn-secondary",onClick:z,children:"Cancel"}),n.jsx("button",{type:"submit",className:"btn btn-primary",children:g?"Update Partner":"Add Partner"})]})]})]})})]})},Hx=({url:s})=>{const[r,u]=E.useState([]),[c,d]=E.useState(!0),[f,p]=E.useState("all"),[g,v]=E.useState(null),[m,x]=E.useState(!1),b=[{_id:"1",customerName:"Rahul Sharma",customerEmail:"<EMAIL>",orderId:"ORD001",type:"complaint",subject:"Late Delivery",message:"My order was delivered 45 minutes late. The food was cold when it arrived.",rating:2,status:"pending",priority:"high",createdAt:"2024-01-15T10:30:00Z",resolvedAt:null,response:null,category:"delivery"},{_id:"2",customerName:"Priya Patel",customerEmail:"<EMAIL>",orderId:"ORD002",type:"feedback",subject:"Great Service",message:"Excellent food quality and fast delivery. Really impressed with the service!",rating:5,status:"resolved",priority:"low",createdAt:"2024-01-14T15:20:00Z",resolvedAt:"2024-01-14T16:00:00Z",response:"Thank you for your positive feedback! We appreciate your business.",category:"service"},{_id:"3",customerName:"Amit Kumar",customerEmail:"<EMAIL>",orderId:"ORD003",type:"complaint",subject:"Wrong Order",message:"I ordered chicken biryani but received vegetable biryani instead.",rating:1,status:"pending",priority:"high",createdAt:"2024-01-13T12:45:00Z",resolvedAt:null,response:null,category:"order"},{_id:"4",customerName:"Sneha Reddy",customerEmail:"<EMAIL>",orderId:"ORD004",type:"suggestion",subject:"App Improvement",message:"It would be great to have a feature to track delivery in real-time.",rating:4,status:"resolved",priority:"medium",createdAt:"2024-01-12T09:15:00Z",resolvedAt:"2024-01-12T14:30:00Z",response:"Thank you for the suggestion. We are working on implementing real-time tracking.",category:"app"}];E.useEffect(()=>{_()},[]);const _=async()=>{try{d(!0),setTimeout(()=>{u(b),d(!1)},1e3)}catch(R){console.error("Error fetching feedbacks:",R),Y.error("Failed to load feedbacks"),d(!1)}},U=async(R,W,P=null)=>{try{const F=r.map(ee=>ee._id===R?{...ee,status:W,resolvedAt:W==="resolved"?new Date().toISOString():null,response:P||ee.response}:ee);u(F),Y.success("Status updated successfully")}catch(F){console.error("Error updating status:",F),Y.error("Failed to update status")}},w=R=>{v(R),x(!0)},z=async(R,W)=>{try{await U(R,"resolved",W),x(!1),v(null)}catch(P){console.error("Error responding to feedback:",P),Y.error("Failed to respond to feedback")}},D=r.filter(R=>f==="all"?!0:R.status===f),C=R=>`badge ${{pending:"badge-warning",resolved:"badge-success",in_progress:"badge-info"}[R]||"badge-info"}`,A=R=>`badge ${{high:"badge-danger",medium:"badge-warning",low:"badge-success"}[R]||"badge-info"}`,T=R=>{switch(R){case"complaint":return n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),n.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]});case"feedback":return n.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("path",{d:"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"})});case"suggestion":return n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),n.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]});default:return null}},N=R=>Array.from({length:5},(W,P)=>n.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:P<R?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",className:P<R?"star-filled":"star-empty",children:n.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})},P));return c?n.jsx("div",{className:"feedback",children:n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading feedback..."})]})}):n.jsxs("div",{className:"feedback",children:[n.jsxs("div",{className:"page-header",children:[n.jsxs("div",{children:[n.jsx("h1",{children:"Feedback & Complaints"}),n.jsx("p",{children:"Manage customer feedback and resolve complaints"})]}),n.jsxs("div",{className:"filter-buttons",children:[n.jsxs("button",{className:f==="all"?"active":"",onClick:()=>p("all"),children:["All (",r.length,")"]}),n.jsxs("button",{className:f==="pending"?"active":"",onClick:()=>p("pending"),children:["Pending (",r.filter(R=>R.status==="pending").length,")"]}),n.jsxs("button",{className:f==="resolved"?"active":"",onClick:()=>p("resolved"),children:["Resolved (",r.filter(R=>R.status==="resolved").length,")"]})]})]}),n.jsxs("div",{className:"stats-grid",children:[n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon complaints",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),n.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(R=>R.type==="complaint").length}),n.jsx("p",{children:"Complaints"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon feedback-positive",children:n.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("path",{d:"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"})})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(R=>R.type==="feedback").length}),n.jsx("p",{children:"Positive Feedback"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon suggestions",children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("circle",{cx:"12",cy:"12",r:"10"}),n.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),n.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:r.filter(R=>R.type==="suggestion").length}),n.jsx("p",{children:"Suggestions"})]})]}),n.jsxs("div",{className:"stat-card",children:[n.jsx("div",{className:"stat-icon average-rating",children:n.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:n.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})})}),n.jsxs("div",{className:"stat-info",children:[n.jsx("h3",{children:(r.reduce((R,W)=>R+W.rating,0)/r.length).toFixed(1)}),n.jsx("p",{children:"Average Rating"})]})]})]}),n.jsxs("div",{className:"card",children:[n.jsxs("div",{className:"card-header",children:[n.jsx("h3",{className:"card-title",children:"Customer Feedback"}),n.jsx("p",{className:"card-subtitle",children:"Review and respond to customer feedback"})]}),n.jsxs("div",{className:"feedback-table",children:[n.jsxs("div",{className:"table-header",children:[n.jsx("span",{children:"Customer"}),n.jsx("span",{children:"Type"}),n.jsx("span",{children:"Subject"}),n.jsx("span",{children:"Rating"}),n.jsx("span",{children:"Priority"}),n.jsx("span",{children:"Status"}),n.jsx("span",{children:"Date"}),n.jsx("span",{children:"Actions"})]}),D.map(R=>n.jsxs("div",{className:"table-row",children:[n.jsxs("div",{className:"customer-info",children:[n.jsx("div",{className:"customer-avatar",children:R.customerName.charAt(0).toUpperCase()}),n.jsxs("div",{children:[n.jsx("h4",{children:R.customerName}),n.jsxs("p",{children:["Order #",R.orderId]})]})]}),n.jsxs("div",{className:"type-info",children:[T(R.type),n.jsx("span",{className:"type-label",children:R.type})]}),n.jsxs("div",{className:"subject-info",children:[n.jsx("h4",{children:R.subject}),n.jsxs("p",{children:[R.message.substring(0,50),"..."]})]}),n.jsxs("div",{className:"rating-info",children:[n.jsx("div",{className:"rating-stars",children:N(R.rating)}),n.jsxs("span",{className:"rating-value",children:[R.rating,"/5"]})]}),n.jsx("div",{className:"priority-info",children:n.jsx("span",{className:A(R.priority),children:R.priority})}),n.jsx("div",{className:"status-info",children:n.jsx("span",{className:C(R.status),children:R.status})}),n.jsxs("div",{className:"date-info",children:[n.jsx("p",{children:new Date(R.createdAt).toLocaleDateString()}),n.jsx("p",{children:new Date(R.createdAt).toLocaleTimeString()})]}),n.jsxs("div",{className:"actions",children:[n.jsx("button",{className:"btn-icon view",onClick:()=>w(R),title:"View Details",children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),n.jsx("circle",{cx:"12",cy:"12",r:"3"})]})}),R.status==="pending"&&n.jsx("button",{className:"btn-icon resolve",onClick:()=>U(R._id,"resolved"),title:"Mark as Resolved",children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"9,11 12,14 22,4"}),n.jsx("path",{d:"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"})]})})]})]},R._id))]})]}),m&&g&&n.jsx(qx,{feedback:g,onClose:()=>{x(!1),v(null)},onRespond:z})]})},qx=({feedback:s,onClose:r,onRespond:u})=>{const[c,d]=E.useState(s.response||""),f=p=>{p.preventDefault(),c.trim()&&u(s._id,c)};return n.jsx("div",{className:"modal-overlay",onClick:r,children:n.jsxs("div",{className:"modal feedback-modal",onClick:p=>p.stopPropagation(),children:[n.jsxs("div",{className:"modal-header",children:[n.jsx("h3",{children:"Feedback Details"}),n.jsx("button",{className:"modal-close",onClick:r,children:n.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),n.jsxs("div",{className:"modal-content",children:[n.jsxs("div",{className:"feedback-details",children:[n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Customer:"}),n.jsxs("span",{children:[s.customerName," (",s.customerEmail,")"]})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Order ID:"}),n.jsx("span",{children:s.orderId})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Type:"}),n.jsx("span",{className:"type-badge",children:s.type})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Subject:"}),n.jsx("span",{children:s.subject})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Rating:"}),n.jsxs("div",{className:"rating-display",children:[Array.from({length:5},(p,g)=>n.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:g<s.rating?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",className:g<s.rating?"star-filled":"star-empty",children:n.jsx("polygon",{points:"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"})},g)),n.jsxs("span",{children:["(",s.rating,"/5)"]})]})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Message:"}),n.jsx("div",{className:"message-content",children:s.message})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Date:"}),n.jsx("span",{children:new Date(s.createdAt).toLocaleString()})]}),n.jsxs("div",{className:"detail-row",children:[n.jsx("label",{children:"Status:"}),n.jsx("span",{className:`badge ${s.status==="resolved"?"badge-success":"badge-warning"}`,children:s.status})]})]}),s.status==="pending"&&n.jsxs("form",{onSubmit:f,className:"response-form",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{className:"form-label",children:"Response:"}),n.jsx("textarea",{value:c,onChange:p=>d(p.target.value),className:"form-textarea",rows:"4",placeholder:"Type your response here...",required:!0})]}),n.jsxs("div",{className:"modal-actions",children:[n.jsx("button",{type:"button",className:"btn btn-secondary",onClick:r,children:"Cancel"}),n.jsx("button",{type:"submit",className:"btn btn-primary",children:"Send Response & Mark Resolved"})]})]}),s.status==="resolved"&&s.response&&n.jsxs("div",{className:"existing-response",children:[n.jsx("h4",{children:"Response:"}),n.jsx("div",{className:"response-content",children:s.response}),n.jsxs("p",{className:"response-date",children:["Resolved on ",new Date(s.resolvedAt).toLocaleString()]})]})]})]})})},Yx=({url:s,token:r})=>{const{admin:u}=Vi(),[c,d]=E.useState(null),[f,p]=E.useState(!1),[g,v]=E.useState(""),[m,x]=E.useState({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[]}),[b,_]=E.useState(""),U=A=>{const T=A.target.name,N=A.target.value;x(R=>({...R,[T]:N}))},w=()=>{b.trim()&&!m.cuisineTypes.includes(b.trim())&&(x(A=>({...A,cuisineTypes:[...A.cuisineTypes,b.trim()]})),_(""))},z=A=>{x(T=>({...T,cuisineTypes:T.cuisineTypes.filter(N=>N!==A)}))},D=async A=>{if(A){p(!0),Y.info("Uploading restaurant image to Cloudinary...");try{const T=await Yc(A,oa.restaurant.folder,{tags:oa.restaurant.tags,transformation:oa.restaurant.transformation});T.success?(v(T.url),Y.success("Restaurant image uploaded successfully!")):Y.error(T.error||"Failed to upload restaurant image")}catch(T){console.error("Restaurant image upload error:",T),Y.error("Failed to upload restaurant image")}finally{p(!1)}}},C=async A=>{if(A.preventDefault(),!c&&!g){Y.error("Please select and upload a restaurant image");return}if(c&&!g){await D(c),Y.info("Please submit again after image upload completes");return}const T=new FormData;T.append("name",m.name),T.append("description",m.description),T.append("address",m.address),T.append("phone",m.phone),T.append("email",m.email),T.append("deliveryTime",m.deliveryTime),T.append("deliveryFee",m.deliveryFee),T.append("minimumOrder",m.minimumOrder),T.append("cuisineTypes",JSON.stringify(m.cuisineTypes)),T.append("image",g);try{const R=await(await fetch(`${s}/api/restaurant/add`,{method:"POST",headers:{Authorization:`Bearer ${r}`},body:T})).json();R.success?(x({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[]}),d(null),v(""),Y.success(R.message)):Y.error(R.message)}catch(N){console.error("Error adding restaurant:",N),Y.error("Error adding restaurant")}};return n.jsx("div",{className:"add-restaurant",children:n.jsxs("form",{className:"flex-col",onSubmit:C,children:[n.jsxs("div",{className:"add-img-upload flex-col",children:[n.jsx("p",{children:"Upload Restaurant Image"}),n.jsx("label",{htmlFor:"image",children:n.jsx("img",{src:g||(c?URL.createObjectURL(c):"/api/placeholder/150/150"),alt:"Restaurant"})}),n.jsx("input",{onChange:A=>d(A.target.files[0]),type:"file",id:"image",hidden:!0,required:!0}),c&&!g&&n.jsx("div",{className:"upload-actions",children:n.jsx("button",{type:"button",onClick:()=>D(c),disabled:f,className:"upload-btn",children:f?"Uploading...":"Upload to Cloudinary"})}),g&&n.jsx("div",{className:"upload-success",children:n.jsx("span",{className:"success-text",children:"✅ Restaurant image uploaded to Cloudinary successfully!"})})]}),n.jsxs("div",{className:"add-restaurant-name flex-col",children:[n.jsx("p",{children:"Restaurant Name"}),n.jsx("input",{onChange:U,value:m.name,type:"text",name:"name",placeholder:"Type here",required:!0})]}),n.jsxs("div",{className:"add-restaurant-description flex-col",children:[n.jsx("p",{children:"Restaurant Description"}),n.jsx("textarea",{onChange:U,value:m.description,name:"description",rows:"6",placeholder:"Write restaurant description here",required:!0})]}),n.jsxs("div",{className:"add-restaurant-address flex-col",children:[n.jsx("p",{children:"Restaurant Address"}),n.jsx("input",{onChange:U,value:m.address,type:"text",name:"address",placeholder:"Restaurant address",required:!0})]}),n.jsxs("div",{className:"add-restaurant-contact flex-row",children:[n.jsxs("div",{className:"flex-col",children:[n.jsx("p",{children:"Phone Number"}),n.jsx("input",{onChange:U,value:m.phone,type:"tel",name:"phone",placeholder:"Phone number"})]}),n.jsxs("div",{className:"flex-col",children:[n.jsx("p",{children:"Email"}),n.jsx("input",{onChange:U,value:m.email,type:"email",name:"email",placeholder:"Email address"})]})]}),n.jsxs("div",{className:"add-restaurant-details flex-row",children:[n.jsxs("div",{className:"flex-col",children:[n.jsx("p",{children:"Delivery Time"}),n.jsx("input",{onChange:U,value:m.deliveryTime,type:"text",name:"deliveryTime",placeholder:"e.g., 30-45 mins"})]}),n.jsxs("div",{className:"flex-col",children:[n.jsx("p",{children:"Delivery Fee (₹)"}),n.jsx("input",{onChange:U,value:m.deliveryFee,type:"number",name:"deliveryFee",placeholder:"0",min:"0"})]}),n.jsxs("div",{className:"flex-col",children:[n.jsx("p",{children:"Minimum Order (₹)"}),n.jsx("input",{onChange:U,value:m.minimumOrder,type:"number",name:"minimumOrder",placeholder:"0",min:"0"})]})]}),n.jsxs("div",{className:"add-cuisine-types flex-col",children:[n.jsx("p",{children:"Cuisine Types"}),n.jsxs("div",{className:"cuisine-input-container",children:[n.jsx("input",{value:b,onChange:A=>_(A.target.value),type:"text",placeholder:"e.g., Indian, Chinese, Italian",onKeyPress:A=>A.key==="Enter"&&(A.preventDefault(),w())}),n.jsx("button",{type:"button",onClick:w,className:"add-cuisine-btn",children:"Add"})]}),m.cuisineTypes.length>0&&n.jsx("div",{className:"cuisine-tags",children:m.cuisineTypes.map((A,T)=>n.jsxs("span",{className:"cuisine-tag",children:[A,n.jsx("button",{type:"button",onClick:()=>z(A),className:"remove-cuisine",children:"×"})]},T))})]}),n.jsx("button",{type:"submit",className:"add-btn",children:"ADD RESTAURANT"})]})})},Gx=async(s,r="eatzone",u={})=>{var c;try{const d=new FormData;d.append("file",s),d.append("upload_preset","eatzone_admin"),r&&d.append("folder",r);const f=await fetch("https://api.cloudinary.com/v1_1/dodxdudew/image/upload",{method:"POST",body:d});if(!f.ok){const g=await f.json();throw new Error(((c=g.error)==null?void 0:c.message)||"Upload failed")}const p=await f.json();return{success:!0,url:p.secure_url,publicId:p.public_id}}catch(d){return{success:!1,error:d.message}}},Xx=(s,r)=>{if(!s)return"/api/placeholder/50/50";const u=String(s);if(u.includes("cloudinary.com")||u.startsWith("http")||u.startsWith("/")||u.startsWith("data:"))return u;if(u.includes(".png")||u.includes(".jpg")||u.includes(".jpeg")){const c=u.startsWith("/")?u.substring(1):u;return`${r}/images/${c}`}return`${r}/images/${u}`},Vx=({url:s,token:r})=>{const[u,c]=E.useState([]),[d,f]=E.useState(!0),[p,g]=E.useState(null),[v,m]=E.useState({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[],image:null}),[x,b]=E.useState(!1),[_,U]=E.useState(""),[w,z]=E.useState(""),D=async F=>{if(F){b(!0),Y.info("Uploading restaurant image to Cloudinary...");try{const ee=await Gx(F,"eatzone/restaurants",{tags:["restaurant","cover"]});ee.success?(U(ee.url),Y.success("Restaurant image uploaded successfully!")):Y.error(ee.error||"Failed to upload restaurant image")}catch(ee){console.error("Restaurant image upload error:",ee),Y.error("Failed to upload restaurant image")}finally{b(!1)}}},C=async()=>{try{f(!0);const ee=await(await fetch(`${s}/api/restaurant/list`)).json();ee.success?c(ee.data):Y.error("Error fetching restaurants")}catch(F){console.error("Error fetching restaurants:",F),Y.error("Error fetching restaurants")}finally{f(!1)}};E.useEffect(()=>{C()},[]);const A=async(F,ee)=>{if(window.confirm(`Are you sure you want to delete "${ee}"? This action cannot be undone.`))try{const $=await(await fetch(`${s}/api/restaurant/remove`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify({id:F})})).json();$.success?(Y.success("Restaurant removed successfully"),C()):Y.error($.message||"Error removing restaurant")}catch(Q){console.error("Error removing restaurant:",Q),Y.error("Error removing restaurant")}},T=F=>{var ee,Q;console.log("Starting edit for restaurant:",F),g(F),m({name:F.name||"",description:F.description||"",address:F.address||"",phone:F.phone||"",email:F.email||"",deliveryTime:F.deliveryTime||"30-45 mins",deliveryFee:((ee=F.deliveryFee)==null?void 0:ee.toString())||"0",minimumOrder:((Q=F.minimumOrder)==null?void 0:Q.toString())||"0",cuisineTypes:F.cuisineTypes||[],image:null}),z(F.cuisineTypes?F.cuisineTypes.join(", "):""),U(""),b(!1)},N=()=>{g(null),m({name:"",description:"",address:"",phone:"",email:"",deliveryTime:"30-45 mins",deliveryFee:"0",minimumOrder:"0",cuisineTypes:[],image:null}),z(""),U(""),b(!1)},R=F=>{const{name:ee,value:Q,files:$}=F.target;m(ee==="image"?ne=>({...ne,image:$[0]}):ne=>({...ne,[ee]:Q}))},W=F=>{z(F.target.value);const ee=F.target.value.split(",").map(Q=>Q.trim()).filter(Q=>Q.length>0);m(Q=>({...Q,cuisineTypes:ee}))},P=async F=>{var ee,Q,$;if(F.preventDefault(),!v.name||!v.description||!v.address){Y.error("Please fill in all required fields");return}if(isNaN(v.deliveryFee)||Number(v.deliveryFee)<0){Y.error("Please enter a valid delivery fee");return}if(isNaN(v.minimumOrder)||Number(v.minimumOrder)<0){Y.error("Please enter a valid minimum order amount");return}try{console.log("Updating restaurant:",p._id),console.log("Form data:",v),Y.info("Updating restaurant...",{autoClose:1e3});const ne=new FormData;ne.append("id",p._id),ne.append("name",v.name.trim()),ne.append("description",v.description.trim()),ne.append("address",v.address.trim()),ne.append("phone",v.phone.trim()),ne.append("email",v.email.trim()),ne.append("deliveryTime",v.deliveryTime),ne.append("deliveryFee",v.deliveryFee),ne.append("minimumOrder",v.minimumOrder),ne.append("cuisineTypes",JSON.stringify(v.cuisineTypes)),_&&(console.log("Using Cloudinary URL:",_),ne.append("image",_));for(let ye of ne.entries())console.log(ye[0]+": "+ye[1]);const re=await(await fetch(`${s}/api/restaurant/update`,{method:"POST",headers:{Authorization:`Bearer ${r}`},body:ne})).json();console.log("Update response:",re),re.success?(Y.success(re.message||"Restaurant updated successfully"),await C(),N()):(console.error("Update failed:",re.message),Y.error(re.message||"Failed to update restaurant"))}catch(ne){console.error("Error updating restaurant:",ne),((ee=ne.response)==null?void 0:ee.status)===404?Y.error("Restaurant not found"):((Q=ne.response)==null?void 0:Q.status)===400?Y.error("Invalid data provided"):(($=ne.response)==null?void 0:$.status)===403?Y.error("You don't have permission to update this restaurant"):Y.error("Failed to update restaurant. Please try again.")}};return E.useEffect(()=>{C()},[]),d?n.jsx("div",{className:"restaurant-list",children:n.jsx("p",{children:"Loading restaurants..."})}):n.jsxs("div",{className:"restaurant-list",children:[n.jsxs("div",{className:"restaurant-list-header",children:[n.jsx("h2",{children:"All Restaurants"}),n.jsxs("p",{children:["Total: ",u.length," restaurants"]})]}),u.length===0?n.jsx("div",{className:"no-restaurants",children:n.jsx("p",{children:"No restaurants found. Add your first restaurant!"})}):n.jsxs("div",{className:"restaurant-list-table",children:[n.jsxs("div",{className:"restaurant-list-table-format title",children:[n.jsx("b",{children:"Image"}),n.jsx("b",{children:"Name"}),n.jsx("b",{children:"Description"}),n.jsx("b",{children:"Address"}),n.jsx("b",{children:"Delivery"}),n.jsx("b",{children:"Rating"}),n.jsx("b",{children:"Actions"})]}),u.map((F,ee)=>n.jsxs("div",{className:"restaurant-list-table-format",children:[n.jsx("img",{src:Xx(F.image,s),alt:F.name,onError:Q=>{console.error("Failed to load restaurant image:",F.image),Q.target.src="/api/placeholder/50/50"}}),n.jsx("p",{children:F.name}),n.jsx("p",{className:"description",children:F.description}),n.jsx("p",{className:"address",children:F.address}),n.jsxs("div",{className:"delivery-info",children:[n.jsx("p",{children:F.deliveryTime}),n.jsxs("p",{children:["₹",F.deliveryFee]})]}),n.jsx("div",{className:"rating",children:n.jsxs("span",{children:["⭐ ",F.rating]})}),n.jsxs("div",{className:"restaurant-actions",children:[n.jsx("button",{className:"action-btn edit-btn",title:"Edit Restaurant",onClick:()=>T(F),children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),n.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]})}),n.jsx("button",{className:"action-btn delete-btn",title:"Delete Restaurant",onClick:()=>A(F._id,F.name),children:n.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("polyline",{points:"3,6 5,6 21,6"}),n.jsx("path",{d:"M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"}),n.jsx("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),n.jsx("line",{x1:"14",y1:"11",x2:"14",y2:"17"})]})})]})]},ee))]}),p&&n.jsx("div",{className:"edit-modal-overlay",onClick:N,children:n.jsxs("div",{className:"edit-modal",onClick:F=>F.stopPropagation(),children:[n.jsxs("div",{className:"edit-modal-header",children:[n.jsx("h3",{children:"Edit Restaurant"}),n.jsx("button",{className:"close-btn",onClick:N,children:n.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[n.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),n.jsxs("form",{onSubmit:P,className:"edit-form",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-name",children:"Restaurant Name *"}),n.jsx("input",{type:"text",id:"edit-name",name:"name",value:v.name,onChange:R,placeholder:"Enter restaurant name",required:!0})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-description",children:"Description *"}),n.jsx("textarea",{id:"edit-description",name:"description",value:v.description,onChange:R,placeholder:"Enter restaurant description",rows:"3",required:!0})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-address",children:"Address *"}),n.jsx("textarea",{id:"edit-address",name:"address",value:v.address,onChange:R,placeholder:"Enter restaurant address",rows:"2",required:!0})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-phone",children:"Phone"}),n.jsx("input",{type:"tel",id:"edit-phone",name:"phone",value:v.phone,onChange:R,placeholder:"Enter phone number"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-email",children:"Email"}),n.jsx("input",{type:"email",id:"edit-email",name:"email",value:v.email,onChange:R,placeholder:"Enter email address"})]})]}),n.jsxs("div",{className:"form-row",children:[n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-delivery-time",children:"Delivery Time"}),n.jsx("input",{type:"text",id:"edit-delivery-time",name:"deliveryTime",value:v.deliveryTime,onChange:R,placeholder:"e.g., 30-45 mins"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-delivery-fee",children:"Delivery Fee (₹)"}),n.jsx("input",{type:"number",id:"edit-delivery-fee",name:"deliveryFee",value:v.deliveryFee,onChange:R,placeholder:"Enter delivery fee",min:"0"})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-minimum-order",children:"Minimum Order (₹)"}),n.jsx("input",{type:"number",id:"edit-minimum-order",name:"minimumOrder",value:v.minimumOrder,onChange:R,placeholder:"Enter minimum order amount",min:"0"})]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-cuisine-types",children:"Cuisine Types"}),n.jsx("input",{type:"text",id:"edit-cuisine-types",value:w,onChange:W,placeholder:"Enter cuisine types separated by commas (e.g., Indian, Chinese, Italian)"}),n.jsxs("small",{children:["Separate multiple cuisines with commas. Current: ",v.cuisineTypes.length," cuisine(s)"]})]}),n.jsxs("div",{className:"form-group",children:[n.jsx("label",{htmlFor:"edit-image",children:"Restaurant Image"}),n.jsxs("div",{className:"image-upload-section",children:[_&&n.jsxs("div",{className:"current-image",children:[n.jsx("img",{src:_,alt:"Restaurant preview",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"8px"}}),n.jsx("p",{children:"New image uploaded to Cloudinary"})]}),n.jsx("input",{type:"file",id:"edit-image",name:"image",onChange:R,accept:"image/*"}),v.image&&!_&&n.jsx("div",{className:"upload-actions",children:n.jsx("button",{type:"button",onClick:()=>D(v.image),disabled:x,className:"upload-btn",style:{marginTop:"10px",padding:"8px 16px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:x?"not-allowed":"pointer"},children:x?"Uploading...":"Upload to Cloudinary"})}),n.jsx("small",{children:_?"Image uploaded to Cloudinary successfully":"Select an image and click 'Upload to Cloudinary' for faster loading, or leave empty to keep current image"})]})]}),n.jsxs("div",{className:"form-actions",children:[n.jsx("button",{type:"button",className:"btn-cancel",onClick:N,children:"Cancel"}),n.jsx("button",{type:"submit",className:"btn-save",children:"Update Restaurant"})]})]})]})})]})},Qx=()=>{const[s,r]=E.useState(!1),{isAuthenticated:u,loading:c}=Vi(),d="https://eatzone.onrender.com";return c?n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"spinner"}),n.jsx("p",{children:"Loading..."})]}):u?n.jsxs("div",{className:"app",children:[n.jsx(Fv,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0}),n.jsx(Tx,{setSidebarOpen:r}),n.jsxs("div",{className:"app-content",children:[n.jsx(_x,{isOpen:s,setSidebarOpen:r}),n.jsx("main",{className:"main-content",children:n.jsxs(zg,{children:[n.jsx(St,{path:"/",element:n.jsx(dm,{url:d})}),n.jsx(St,{path:"/dashboard",element:n.jsx(dm,{url:d})}),n.jsx(St,{path:"/add",element:n.jsx(Ax,{url:d})}),n.jsx(St,{path:"/list",element:n.jsx(Ox,{url:d})}),n.jsx(St,{path:"/orders",element:n.jsx(Mx,{url:d})}),n.jsx(St,{path:"/analytics",element:n.jsx(zx,{url:d})}),n.jsx(St,{path:"/categories",element:n.jsx(Lx,{url:d})}),n.jsx(St,{path:"/delivery-partners",element:n.jsx(kx,{url:d})}),n.jsx(St,{path:"/feedback",element:n.jsx(Hx,{url:d})}),n.jsx(St,{path:"/add-restaurant",element:n.jsx(Yx,{url:d})}),n.jsx(St,{path:"/restaurants",element:n.jsx(Vx,{url:d})}),n.jsx(St,{path:"*",element:n.jsx(Dg,{to:"/dashboard",replace:!0})})]})})]})]}):n.jsx(wx,{})},Zx=()=>n.jsx(Ex,{children:n.jsx(Qx,{})});function fm(){const s=document.getElementById("root");if(!s){console.error("❌ Root element not found");return}try{G0.createRoot(s).render(n.jsx(Me.StrictMode,{children:n.jsx(nv,{children:n.jsx(Zx,{})})})),console.log("✅ Admin app initialized successfully")}catch(r){console.error("❌ Failed to initialize admin app:",r)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",fm):fm();
