<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EatZone Admin Panel</title>
    <meta name="description" content="EatZone Admin Panel - Manage your restaurant operations" />
    
    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    
    <!-- Meta tags for security -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Theme color -->
    <meta name="theme-color" content="#ff4757">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="EatZone Admin Panel">
    <meta property="og:description" content="Manage your restaurant operations with EatZone Admin Panel">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="EatZone Admin Panel">
    <meta property="twitter:description" content="Manage your restaurant operations with EatZone Admin Panel">
    <script type="module" crossorigin src="/assets/index-BOlOJOXx.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-UqZsdtC7.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
