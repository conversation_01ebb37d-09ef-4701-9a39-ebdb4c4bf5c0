<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EatZone Admin - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 { color: #ff4757; }
        .success { color: #2ed573; font-size: 24px; margin: 20px 0; }
        .button {
            background: #ff4757;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .button:hover { background: #ff3838; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 EatZone Admin Panel</h1>
        <div class="success">✅ Authentication Successfully Removed!</div>
        <p><strong>Build Date:</strong> <span id="buildDate"></span></p>
        <p><strong>Status:</strong> Ready for deployment</p>
        <p><strong>Features:</strong> Direct admin access, no login required</p>
        
        <a href="index.html" class="button">Go to Admin Dashboard</a>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>Deployment Instructions:</h3>
            <ol style="text-align: left;">
                <li>Upload this entire dist folder to Netlify</li>
                <li>Clear Netlify cache</li>
                <li>Hard refresh browser (Ctrl+Shift+R)</li>
                <li>Access admin dashboard directly</li>
            </ol>
        </div>
    </div>
    
    <script>
        document.getElementById('buildDate').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
