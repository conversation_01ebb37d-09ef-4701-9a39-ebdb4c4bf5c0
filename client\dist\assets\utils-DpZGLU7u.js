var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(e,t)=>(t=Symbol[e])?t:Symbol.for("Symbol."+e),a=(t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,c=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&a(e,n,t[n]);if(r)for(var n of r(t))s.call(t,n)&&a(e,n,t[n]);return e},l=(e,t,n)=>new Promise((r,o)=>{var s=e=>{try{a(n.next(e))}catch(t){o(t)}},i=e=>{try{a(n.throw(e))}catch(t){o(t)}},a=e=>e.done?r(e.value):Promise.resolve(e.value).then(s,i);a((n=n.apply(e,t)).next())}),u=function(e,t){this[0]=e,this[1]=t},f=(e,t,n)=>{var r=(e,t,o,s)=>{try{var i=n[e](t),a=(t=i.value)instanceof u,c=i.done;Promise.resolve(a?t[0]:t).then(n=>a?r("return"===e?e:"next",t[1]?{done:n.done,value:n.value}:n,o,s):o({value:n,done:c})).catch(e=>r("throw",e,o,s))}catch(l){s(l)}},o=e=>s[e]=t=>new Promise((n,o)=>r(e,t,n,o)),s={};return n=n.apply(e,t),s[i("asyncIterator")]=()=>s,o("next"),o("throw"),o("return"),s},d=e=>{var t,n=e[i("asyncIterator")],r=!1,o={};return null==n?(n=e[i("iterator")](),t=e=>o[e]=t=>n[e](t)):(n=n.call(e),t=e=>o[e]=t=>{if(r){if(r=!1,"throw"===e)throw t;return t}return r=!0,{done:!1,value:new u(new Promise(r=>{var o=n[e](t);o instanceof Object||(e=>{throw TypeError(e)})("Object expected"),r(o)}),1)}}),o[i("iterator")]=()=>o,t("next"),"throw"in n?t("throw"):o.throw=e=>{throw e},"return"in n&&t("return"),o};function p(e,t){return function(){return e.apply(t,arguments)}}const{toString:h}=Object.prototype,{getPrototypeOf:m}=Object,{iterator:y,toStringTag:b}=Symbol,g=(e=>t=>{const n=h.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),w=e=>(e=e.toLowerCase(),t=>g(t)===e),E=e=>t=>typeof t===e,{isArray:O}=Array,R=E("undefined");const S=w("ArrayBuffer");const v=E("string"),T=E("function"),A=E("number"),x=e=>null!==e&&"object"==typeof e,j=e=>{if("object"!==g(e))return!1;const t=m(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||b in e||y in e)},C=w("Date"),P=w("File"),N=w("Blob"),U=w("FileList"),_=w("URLSearchParams"),[F,L,B,k]=["ReadableStream","Request","Response","Headers"].map(w);function D(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),O(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function q(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const I="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,M=e=>!R(e)&&e!==I;const z=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&m(Uint8Array)),H=w("HTMLFormElement"),J=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),W=w("RegExp"),K=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};D(n,(n,o)=>{let s;!1!==(s=t(n,o,e))&&(r[o]=s||n)}),Object.defineProperties(e,r)};const V=w("AsyncFunction"),$=(X="function"==typeof setImmediate,G=T(I.postMessage),X?setImmediate:G?(Q=`axios@${Math.random()}`,Z=[],I.addEventListener("message",({source:e,data:t})=>{e===I&&t===Q&&Z.length&&Z.shift()()},!1),e=>{Z.push(e),I.postMessage(Q,"*")}):e=>setTimeout(e));var X,G,Q,Z;const Y="undefined"!=typeof queueMicrotask?queueMicrotask.bind(I):"undefined"!=typeof process&&process.nextTick||$,ee={isArray:O,isArrayBuffer:S,isBuffer:function(e){return null!==e&&!R(e)&&null!==e.constructor&&!R(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||T(e.append)&&("formdata"===(t=g(e))||"object"===t&&T(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&S(e.buffer),t},isString:v,isNumber:A,isBoolean:e=>!0===e||!1===e,isObject:x,isPlainObject:j,isReadableStream:F,isRequest:L,isResponse:B,isHeaders:k,isUndefined:R,isDate:C,isFile:P,isBlob:N,isRegExp:W,isFunction:T,isStream:e=>x(e)&&T(e.pipe),isURLSearchParams:_,isTypedArray:z,isFileList:U,forEach:D,merge:function e(){const{caseless:t}=M(this)&&this||{},n={},r=(r,o)=>{const s=t&&q(n,o)||o;j(n[s])&&j(r)?n[s]=e(n[s],r):j(r)?n[s]=e({},r):O(r)?n[s]=r.slice():n[s]=r};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&D(arguments[o],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(D(t,(t,r)=>{n&&T(t)?e[r]=p(t,n):e[r]=t},{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,s,i;const a={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],r&&!r(i,e,t)||a[i]||(t[i]=e[i],a[i]=!0);e=!1!==n&&m(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:g,kindOfTest:w,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(O(e))return e;let t=e.length;if(!A(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[y]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:H,hasOwnProperty:J,hasOwnProp:J,reduceDescriptors:K,freezeMethods:e=>{K(e,(t,n)=>{if(T(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];T(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return O(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:q,global:I,isContextDefined:M,isSpecCompliantForm:function(e){return!!(e&&T(e.append)&&"FormData"===e[b]&&e[y])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(x(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=O(e)?[]:{};return D(e,(e,t)=>{const s=n(e,r+1);!R(s)&&(o[t]=s)}),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:V,isThenable:e=>e&&(x(e)||T(e))&&T(e.then)&&T(e.catch),setImmediate:$,asap:Y,isIterable:e=>null!=e&&T(e[y])};function te(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}ee.inherits(te,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ee.toJSONObject(this.config),code:this.code,status:this.status}}});const ne=te.prototype,re={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{re[e]={value:e}}),Object.defineProperties(te,re),Object.defineProperty(ne,"isAxiosError",{value:!0}),te.from=(e,t,n,r,o,s)=>{const i=Object.create(ne);return ee.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),te.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function oe(e){return ee.isPlainObject(e)||ee.isArray(e)}function se(e){return ee.endsWith(e,"[]")?e.slice(0,-2):e}function ie(e,t,n){return e?e.concat(t).map(function(e,t){return e=se(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const ae=ee.toFlatObject(ee,{},null,function(e){return/^is[A-Z]/.test(e)});function ce(e,t,n){if(!ee.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ee.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!ee.isUndefined(t[e])})).metaTokens,o=n.visitor||l,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ee.isSpecCompliantForm(t);if(!ee.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ee.isDate(e))return e.toISOString();if(!a&&ee.isBlob(e))throw new te("Blob is not supported. Use a Buffer instead.");return ee.isArrayBuffer(e)||ee.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(ee.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ee.isArray(e)&&function(e){return ee.isArray(e)&&!e.some(oe)}(e)||(ee.isFileList(e)||ee.endsWith(n,"[]"))&&(a=ee.toArray(e)))return n=se(n),a.forEach(function(e,r){!ee.isUndefined(e)&&null!==e&&t.append(!0===i?ie([n],r,s):null===i?n:n+"[]",c(e))}),!1;return!!oe(e)||(t.append(ie(o,n,s),c(e)),!1)}const u=[],f=Object.assign(ae,{defaultVisitor:l,convertValue:c,isVisitable:oe});if(!ee.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ee.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ee.forEach(n,function(n,s){!0===(!(ee.isUndefined(n)||null===n)&&o.call(t,n,ee.isString(s)?s.trim():s,r,f))&&e(n,r?r.concat(s):[s])}),u.pop()}}(e),t}function le(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ue(e,t){this._pairs=[],e&&ce(e,this,t)}const fe=ue.prototype;function de(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pe(e,t,n){if(!t)return e;const r=n&&n.encode||de;ee.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(s=o?o(t,n):ee.isURLSearchParams(t)?t.toString():new ue(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}fe.append=function(e,t){this._pairs.push([e,t])},fe.toString=function(e){const t=e?function(t){return e.call(this,t,le)}:le;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class he{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ee.forEach(this.handlers,function(t){null!==t&&e(t)})}}const me={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ye={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ue,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},be="undefined"!=typeof window&&"undefined"!=typeof document,ge="object"==typeof navigator&&navigator||void 0,we=be&&(!ge||["ReactNative","NativeScript","NS"].indexOf(ge.product)<0),Ee="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Oe=be&&window.location.href||"http://localhost",Re=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:be,hasStandardBrowserEnv:we,hasStandardBrowserWebWorkerEnv:Ee,navigator:ge,origin:Oe},Symbol.toStringTag,{value:"Module"})),Se=c(c({},Re),ye);function ve(e){function t(e,n,r,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&ee.isArray(r)?r.length:s,a)return ee.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&ee.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],o)&&ee.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}(r[s])),!i}if(ee.isFormData(e)&&ee.isFunction(e.entries)){const n={};return ee.forEachEntry(e,(e,r)=>{t(function(e){return ee.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null}const Te={transitional:me,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=ee.isObject(e);o&&ee.isHTMLForm(e)&&(e=new FormData(e));if(ee.isFormData(e))return r?JSON.stringify(ve(e)):e;if(ee.isArrayBuffer(e)||ee.isBuffer(e)||ee.isStream(e)||ee.isFile(e)||ee.isBlob(e)||ee.isReadableStream(e))return e;if(ee.isArrayBufferView(e))return e.buffer;if(ee.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ce(e,new Se.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Se.isNode&&ee.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=ee.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ce(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(ee.isString(e))try{return(t||JSON.parse)(e),ee.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Te.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ee.isResponse(e)||ee.isReadableStream(e))return e;if(e&&ee.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw te.from(o,te.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Se.classes.FormData,Blob:Se.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ee.forEach(["delete","get","head","post","put","patch"],e=>{Te.headers[e]={}});const Ae=ee.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),xe=Symbol("internals");function je(e){return e&&String(e).trim().toLowerCase()}function Ce(e){return!1===e||null==e?e:ee.isArray(e)?e.map(Ce):String(e)}function Pe(e,t,n,r,o){return ee.isFunction(r)?r.call(this,t,n):(o&&(t=n),ee.isString(t)?ee.isString(r)?-1!==t.indexOf(r):ee.isRegExp(r)?r.test(t):void 0:void 0)}let Ne=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=je(t);if(!o)throw new Error("header name must be a non-empty string");const s=ee.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=Ce(e))}const s=(e,t)=>ee.forEach(e,(e,n)=>o(e,n,t));if(ee.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(ee.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach(function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&Ae[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(ee.isObject(e)&&ee.isIterable(e)){let n,r,o={};for(const t of e){if(!ee.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?ee.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=je(e)){const n=ee.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ee.isFunction(t))return t.call(this,e,n);if(ee.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=je(e)){const n=ee.findKey(this,e);return!(!n||void 0===this[n]||t&&!Pe(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=je(e)){const o=ee.findKey(n,e);!o||t&&!Pe(0,n[o],o,t)||(delete n[o],r=!0)}}return ee.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Pe(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return ee.forEach(this,(r,o)=>{const s=ee.findKey(n,o);if(s)return t[s]=Ce(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(o):String(o).trim();i!==o&&delete t[o],t[i]=Ce(r),n[i]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return ee.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ee.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){const t=(this[xe]=this[xe]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=je(e);t[r]||(!function(e,t){const n=ee.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})})}(n,e),t[r]=!0)}return ee.isArray(e)?e.forEach(r):r(e),this}};function Ue(e,t){const n=this||Te,r=t||n,o=Ne.from(r.headers);let s=r.data;return ee.forEach(e,function(e){s=e.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function _e(e){return!(!e||!e.__CANCEL__)}function Fe(e,t,n){te.call(this,null==e?"canceled":e,te.ERR_CANCELED,t,n),this.name="CanceledError"}function Le(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new te("Request failed with status code "+n.status,[te.ERR_BAD_REQUEST,te.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}Ne.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ee.reduceDescriptors(Ne.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),ee.freezeMethods(Ne),ee.inherits(Fe,te,{__CANCEL__:!0});const Be=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[i];o||(o=c),n[s]=a,r[s]=c;let u=i,f=0;for(;u!==s;)f+=n[u++],u%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const d=l&&c-l;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(e,t){let n,r,o=0,s=1e3/t;const i=(t,s=Date.now())=>{o=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=s?i(e,t):(n=e,r||(r=setTimeout(()=>{r=null,i(n)},s-a)))},()=>n&&i(n)]}(n=>{const s=n.loaded,i=n.lengthComputable?n.total:void 0,a=s-r,c=o(a);r=s;e({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},ke=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},De=e=>(...t)=>ee.asap(()=>e(...t)),qe=Se.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Se.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Se.origin),Se.navigator&&/(msie|trident)/i.test(Se.navigator.userAgent)):()=>!0,Ie=Se.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];ee.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),ee.isString(r)&&i.push("path="+r),ee.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Me(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const ze=e=>e instanceof Ne?c({},e):e;function He(e,t){t=t||{};const n={};function r(e,t,n,r){return ee.isPlainObject(e)&&ee.isPlainObject(t)?ee.merge.call({caseless:r},e,t):ee.isPlainObject(t)?ee.merge({},t):ee.isArray(t)?t.slice():t}function o(e,t,n,o){return ee.isUndefined(t)?ee.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function s(e,t){if(!ee.isUndefined(t))return r(void 0,t)}function i(e,t){return ee.isUndefined(t)?ee.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,s){return s in t?r(n,o):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t,n)=>o(ze(e),ze(t),0,!0)};return ee.forEach(Object.keys(Object.assign({},e,t)),function(r){const s=c[r]||o,i=s(e[r],t[r],r);ee.isUndefined(i)&&s!==a||(n[r]=i)}),n}const Je=e=>{const t=He({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:c}=t;if(t.headers=a=Ne.from(a),t.url=pe(Me(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ee.isFormData(r))if(Se.hasStandardBrowserEnv||Se.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(Se.hasStandardBrowserEnv&&(o&&ee.isFunction(o)&&(o=o(t)),o||!1!==o&&qe(t.url))){const e=s&&i&&Ie.read(i);e&&a.set(s,e)}return t},We="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Je(e);let o=r.data;const s=Ne.from(r.headers).normalize();let i,a,c,l,u,{responseType:f,onUploadProgress:d,onDownloadProgress:p}=r;function h(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function y(){if(!m)return;const r=Ne.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Le(function(e){t(e),h()},function(e){n(e),h()},{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(n(new te("Request aborted",te.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new te("Network Error",te.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||me;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new te(t,o.clarifyTimeoutError?te.ETIMEDOUT:te.ECONNABORTED,e,m)),m=null},void 0===o&&s.setContentType(null),"setRequestHeader"in m&&ee.forEach(s.toJSON(),function(e,t){m.setRequestHeader(t,e)}),ee.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),f&&"json"!==f&&(m.responseType=r.responseType),p&&([c,u]=Be(p,!0),m.addEventListener("progress",c)),d&&m.upload&&([a,l]=Be(d),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Fe(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const b=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);b&&-1===Se.protocols.indexOf(b)?n(new te("Unsupported protocol "+b+":",te.ERR_BAD_REQUEST,e)):m.send(o||null)})},Ke=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof te?t:new Fe(t instanceof Error?t.message:t))}};let s=t&&setTimeout(()=>{s=null,o(new te(`timeout ${t} of ms exceeded`,te.ETIMEDOUT))},t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>ee.asap(i),a}},Ve=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},$e=function(e,t){return f(this,null,function*(){try{for(var n,r,o,s=((e,t,n)=>(t=e[i("asyncIterator")])?t.call(e):(e=e[i("iterator")](),t={},(n=(n,r)=>(r=e[n])&&(t[n]=t=>new Promise((n,o,s)=>(t=r.call(e,t),s=t.done,Promise.resolve(t.value).then(e=>n({value:e,done:s}),o)))))("next"),n("return"),t))(Xe(e));n=!(r=yield new u(s.next())).done;n=!1){const e=r.value;yield*d(Ve(e,t))}}catch(r){o=[r]}finally{try{n&&(r=s.return)&&(yield new u(r.call(s)))}finally{if(o)throw o[0]}}})},Xe=function(e){return f(this,null,function*(){if(e[Symbol.asyncIterator])return void(yield*d(e));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield new u(t.read());if(e)break;yield n}}finally{yield new u(t.cancel())}})},Ge=(e,t,n,r)=>{const o=$e(e,t);let s,i=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({pull(e){return l(this,null,function*(){try{const{done:t,value:r}=yield o.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=i+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}})},cancel:e=>(a(e),o.return())},{highWaterMark:2})},Qe="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Ze=Qe&&"function"==typeof ReadableStream,Ye=Qe&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):e=>l(void 0,null,function*(){return new Uint8Array(yield new Response(e).arrayBuffer())})),et=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},tt=Ze&&et(()=>{let e=!1;const t=new Request(Se.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),nt=Ze&&et(()=>ee.isReadableStream(new Response("").body)),rt={stream:nt&&(e=>e.body)};var ot;Qe&&(ot=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!rt[e]&&(rt[e]=ee.isFunction(ot[e])?t=>t[e]():(t,n)=>{throw new te(`Response type '${e}' is not supported`,te.ERR_NOT_SUPPORT,n)})}));const st=(e,t)=>l(void 0,null,function*(){const n=ee.toFiniteNumber(e.getContentLength());return null==n?(e=>l(void 0,null,function*(){if(null==e)return 0;if(ee.isBlob(e))return e.size;if(ee.isSpecCompliantForm(e)){const t=new Request(Se.origin,{method:"POST",body:e});return(yield t.arrayBuffer()).byteLength}return ee.isArrayBufferView(e)||ee.isArrayBuffer(e)?e.byteLength:(ee.isURLSearchParams(e)&&(e+=""),ee.isString(e)?(yield Ye(e)).byteLength:void 0)}))(t):n}),it={http:null,xhr:We,fetch:Qe&&(e=>l(void 0,null,function*(){let{url:r,method:o,data:s,signal:i,cancelToken:a,timeout:l,onDownloadProgress:u,onUploadProgress:f,responseType:d,headers:p,withCredentials:h="same-origin",fetchOptions:m}=Je(e);d=d?(d+"").toLowerCase():"text";let y,b=Ke([i,a&&a.toAbortSignal()],l);const g=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let w;try{if(f&&tt&&"get"!==o&&"head"!==o&&0!==(w=yield st(p,s))){let e,t=new Request(r,{method:"POST",body:s,duplex:"half"});if(ee.isFormData(s)&&(e=t.headers.get("content-type"))&&p.setContentType(e),t.body){const[e,n]=ke(w,Be(De(f)));s=Ge(t.body,65536,e,n)}}ee.isString(h)||(h=h?"include":"omit");const i="credentials"in Request.prototype;y=new Request(r,(E=c({},m),O={signal:b,method:o.toUpperCase(),headers:p.normalize().toJSON(),body:s,duplex:"half",credentials:i?h:void 0},t(E,n(O))));let a=yield fetch(y);const l=nt&&("stream"===d||"response"===d);if(nt&&(u||l&&g)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});const t=ee.toFiniteNumber(a.headers.get("content-length")),[n,r]=u&&ke(t,Be(De(u),!0))||[];a=new Response(Ge(a.body,65536,n,()=>{r&&r(),g&&g()}),e)}d=d||"text";let R=yield rt[ee.findKey(rt,d)||"text"](a,e);return!l&&g&&g(),yield new Promise((t,n)=>{Le(t,n,{data:R,headers:Ne.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:y})})}catch(R){if(g&&g(),R&&"TypeError"===R.name&&/Load failed|fetch/i.test(R.message))throw Object.assign(new te("Network Error",te.ERR_NETWORK,e,y),{cause:R.cause||R});throw te.from(R,R&&R.code,e,y)}var E,O}))};ee.forEach(it,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const at=e=>`- ${e}`,ct=e=>ee.isFunction(e)||null===e||!1===e,lt=e=>{e=ee.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!ct(n)&&(r=it[(t=String(n)).toLowerCase()],void 0===r))throw new te(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+s]=r}if(!r){const e=Object.entries(o).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new te("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(at).join("\n"):" "+at(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function ut(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Fe(null,e)}function ft(e){ut(e),e.headers=Ne.from(e.headers),e.data=Ue.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return lt(e.adapter||Te.adapter)(e).then(function(t){return ut(e),t.data=Ue.call(e,e.transformResponse,t),t.headers=Ne.from(t.headers),t},function(t){return _e(t)||(ut(e),t&&t.response&&(t.response.data=Ue.call(e,e.transformResponse,t.response),t.response.headers=Ne.from(t.response.headers))),Promise.reject(t)})}const dt="1.9.0",pt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{pt[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const ht={};pt.transitional=function(e,t,n){return(r,o,s)=>{if(!1===e)throw new te(function(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}(o," has been removed"+(t?" in "+t:"")),te.ERR_DEPRECATED);return t&&!ht[o]&&(ht[o]=!0),!e||e(r,o,s)}},pt.spelling=function(e){return(e,t)=>!0};const mt={assertOptions:function(e,t,n){if("object"!=typeof e)throw new te("options must be an object",te.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const t=e[s],n=void 0===t||i(t,s,e);if(!0!==n)throw new te("option "+s+" must be "+n,te.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new te("Unknown option "+s,te.ERR_BAD_OPTION)}},validators:pt},yt=mt.validators;let bt=class{constructor(e){this.defaults=e||{},this.interceptors={request:new he,response:new he}}request(e,t){return l(this,null,function*(){try{return yield this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}})}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=He(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&mt.assertOptions(n,{silentJSONParsing:yt.transitional(yt.boolean),forcedJSONParsing:yt.transitional(yt.boolean),clarifyTimeoutError:yt.transitional(yt.boolean)},!1),null!=r&&(ee.isFunction(r)?t.paramsSerializer={serialize:r}:mt.assertOptions(r,{encode:yt.function,serialize:yt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),mt.assertOptions(t,{baseUrl:yt.spelling("baseURL"),withXsrfToken:yt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&ee.merge(o.common,o[t.method]);o&&ee.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=Ne.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach(function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const c=[];let l;this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let u,f=0;if(!a){const e=[ft.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);f<u;)l=l.then(e[f++],e[f++]);return l}u=i.length;let d=t;for(f=0;f<u;){const e=i[f++],t=i[f++];try{d=e(d)}catch(p){t.call(this,p);break}}try{l=ft.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,u=c.length;f<u;)l=l.then(c[f++],c[f++]);return l}getUri(e){return pe(Me((e=He(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};ee.forEach(["delete","get","head","options"],function(e){bt.prototype[e]=function(t,n){return this.request(He(n||{},{method:e,url:t,data:(n||{}).data}))}}),ee.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,o){return this.request(He(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}bt.prototype[e]=t(),bt.prototype[e+"Form"]=t(!0)});const gt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(gt).forEach(([e,t])=>{gt[t]=e});const wt=function e(t){const n=new bt(t),r=p(bt.prototype.request,n);return ee.extend(r,bt.prototype,n,{allOwnKeys:!0}),ee.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(He(t,n))},r}(Te);wt.Axios=bt,wt.CanceledError=Fe,wt.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,o){n.reason||(n.reason=new Fe(e,r,o),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e(function(e){t=e}),cancel:t}}},wt.isCancel=_e,wt.VERSION=dt,wt.toFormData=ce,wt.AxiosError=te,wt.Cancel=wt.CanceledError,wt.all=function(e){return Promise.all(e)},wt.spread=function(e){return function(t){return e.apply(null,t)}},wt.isAxiosError=function(e){return ee.isObject(e)&&!0===e.isAxiosError},wt.mergeConfig=He,wt.AxiosHeaders=Ne,wt.formToJSON=e=>ve(ee.isHTMLForm(e)?new FormData(e):e),wt.getAdapter=lt,wt.HttpStatusCode=gt,wt.default=wt;const{Axios:Et,AxiosError:Ot,CanceledError:Rt,isCancel:St,CancelToken:vt,VERSION:Tt,all:At,Cancel:xt,isAxiosError:jt,spread:Ct,toFormData:Pt,AxiosHeaders:Nt,HttpStatusCode:Ut,formToJSON:_t,getAdapter:Ft,mergeConfig:Lt}=wt;export{wt as a};
