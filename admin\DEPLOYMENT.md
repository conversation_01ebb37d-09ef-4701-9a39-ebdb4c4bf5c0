# EatZone Admin Panel - Deployment Guide

## 🚀 Quick Deployment

### Option 1: Deploy to Netlify (Recommended)

1. **Build the admin panel**:
   ```bash
   cd admin
   npm run build
   ```

2. **Deploy to Netlify**:
   - Go to [Netlify](https://netlify.com)
   - Click "Add new site" → "Deploy manually"
   - Drag and drop the `admin/dist` folder
   - Your admin panel will be live at: `https://your-site-name.netlify.app`

3. **Configure Environment Variables**:
   In Netlify dashboard → Site settings → Environment variables:
   ```
   VITE_API_BASE_URL=https://eatzone.onrender.com
   VITE_APP_ENV=production
   NODE_ENV=production
   ```

### Option 2: Connect GitHub Repository

1. **Connect Repository**:
   - Netlify dashboard → "Add new site" → "Import from Git"
   - Connect your GitHub repository

2. **Build Settings**:
   ```
   Base directory: admin
   Build command: npm run build
   Publish directory: dist
   ```

3. **Environment Variables** (same as above)

## 🔧 Configuration

### Backend API Requirements

The admin panel expects these API endpoints to be available:

#### Authentication Endpoints
- `POST /api/admin/login` - Admin login
- `GET /api/admin/verify` - Verify admin token

#### Data Endpoints
- `GET /api/order/list` - Get all orders
- `POST /api/order/status` - Update order status
- `GET /api/food/list` - Get all food items
- `POST /api/food/add` - Add new food item
- `POST /api/food/remove` - Delete food item
- `GET /api/category/list` - Get all categories
- `POST /api/category/add` - Add new category
- `GET /api/restaurant/list` - Get all restaurants
- `GET /api/user/list` - Get all users (optional)

### Environment Variables

Create a `.env` file in the admin directory:
```env
VITE_API_BASE_URL=https://eatzone.onrender.com
VITE_APP_ENV=production
```

## 🔐 Admin Authentication

### Default Login (Demo)
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Setting Up Real Authentication

1. **Backend Setup**: Ensure your backend has admin authentication endpoints
2. **Admin Registration**: Use the secure admin registration endpoint with super admin key
3. **Token Management**: The admin panel handles JWT tokens automatically

## 📱 Features Overview

### ✅ Completed Features

1. **Dashboard Analytics**
   - Real-time statistics
   - Revenue tracking
   - Order metrics
   - User analytics

2. **Food Management**
   - Add/Edit/Delete food items
   - Image upload via Cloudinary
   - Category assignment
   - Discount management

3. **Order Management**
   - Order tracking
   - Status updates
   - Order history
   - Customer information

4. **Category Management**
   - CRUD operations
   - Image upload
   - Dynamic categories

5. **Restaurant Management**
   - Restaurant CRUD
   - Location management
   - Operating hours

6. **Analytics & Reporting**
   - Sales trends
   - Performance metrics
   - Top items analysis

7. **Delivery Partner Management**
   - Partner management
   - Assignment tracking

8. **Feedback Management**
   - Customer reviews
   - Rating management

9. **Authentication & Security**
   - Secure login
   - Token-based auth
   - Role-based access

## 🎯 Admin Panel URLs

After deployment, access different sections:

- **Dashboard**: `/dashboard`
- **Add Food**: `/add`
- **Food List**: `/list`
- **Orders**: `/orders`
- **Categories**: `/categories`
- **Restaurants**: `/restaurants`
- **Analytics**: `/analytics`
- **Delivery Partners**: `/delivery-partners`
- **Feedback**: `/feedback`

## 🔧 Troubleshooting

### Common Issues

1. **Build Fails**:
   - Check Node.js version (use Node 18+)
   - Clear node_modules: `rm -rf node_modules && npm install`

2. **API Connection Issues**:
   - Verify VITE_API_BASE_URL is correct
   - Check CORS settings on backend
   - Ensure backend is running

3. **Authentication Issues**:
   - Check admin credentials
   - Verify JWT token handling
   - Check backend admin endpoints

4. **Image Upload Issues**:
   - Verify Cloudinary configuration
   - Check upload preset settings
   - Ensure file size limits

### Performance Optimization

1. **Enable Gzip**: Netlify automatically enables gzip compression
2. **CDN**: Netlify provides global CDN
3. **Caching**: Static assets are cached automatically
4. **Image Optimization**: Cloudinary handles image optimization

## 📊 Monitoring

### Analytics
- Monitor admin panel usage
- Track API response times
- Monitor error rates

### Logs
- Check Netlify function logs
- Monitor backend API logs
- Track authentication attempts

## 🔄 Updates

### Deploying Updates

1. **Make changes** to the admin panel code
2. **Build**: `npm run build`
3. **Deploy**: 
   - Manual: Upload new dist folder
   - Auto: Push to connected Git repository

### Version Control
- Tag releases for tracking
- Maintain changelog
- Test in staging environment

## 🛡️ Security

### Best Practices
- Use HTTPS only
- Implement proper CORS
- Validate all inputs
- Use secure headers
- Regular security updates

### Environment Security
- Never commit .env files
- Use Netlify environment variables
- Rotate API keys regularly
- Monitor access logs

## 📞 Support

### Getting Help
1. Check console for errors
2. Verify API connectivity
3. Check environment variables
4. Review deployment logs

### Common Solutions
- Clear browser cache
- Check network connectivity
- Verify backend status
- Update dependencies

## 🎉 Success!

Your EatZone Admin Panel is now deployed and ready to use!

**Admin Panel URL**: `https://your-admin-site.netlify.app`
**Login**: Use demo credentials or your admin account

Enjoy managing your food delivery platform! 🍕
