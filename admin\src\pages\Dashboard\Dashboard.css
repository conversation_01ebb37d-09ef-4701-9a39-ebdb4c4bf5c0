.dashboard {
  padding: 2rem;
  background: var(--light-gray);
  min-height: calc(100vh - 80px);
  overflow-x: hidden;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0 0 0.5rem 0;
}

.dashboard-header p {
  color: var(--dark-gray);
  font-size: 1rem;
  margin: 0;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

.dashboard-loading p {
  color: var(--dark-gray);
  font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border-left: 4px solid transparent;
  overflow: hidden;
  min-width: 0;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.stat-icon.primary {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
}

.stat-icon.success {
  background: linear-gradient(135deg, var(--success), #218838);
}

.stat-icon.warning {
  background: linear-gradient(135deg, var(--warning), #e0a800);
}

.stat-icon.info {
  background: linear-gradient(135deg, var(--info), #138496);
}

.stat-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-black);
  margin: 0;
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-info p {
  color: var(--dark-gray);
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  margin-top: 0.5rem;
  display: inline-block;
}

.stat-change.increase {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.stat-change.decrease {
  background: rgba(220, 53, 69, 0.1);
  color: var(--danger);
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.dashboard-left,
.dashboard-right {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Recent Orders */
.recent-orders {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.order-item:hover {
  background: var(--medium-gray);
}

.order-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 0.25rem 0;
}

.order-info p {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.order-time {
  font-size: 0.625rem;
  color: var(--dark-gray);
}

.order-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.order-amount {
  font-weight: 600;
  color: var(--accent-black);
  font-size: 0.875rem;
}

/* Top Food Items */
.top-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.food-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.food-item:hover {
  background: var(--medium-gray);
}

.food-image {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-md);
  object-fit: cover;
  object-position: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  flex-shrink: 0;
}

.food-info {
  flex: 1;
}

.food-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0 0 0.25rem 0;
}

.food-info p {
  font-size: 0.75rem;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.food-price {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--primary-red);
}

.food-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--primary-red);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Quick Stats */
.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--medium-gray);
}

.quick-stat:last-child {
  border-bottom: none;
}

.quick-stat .stat-label {
  font-size: 0.875rem;
  color: var(--dark-gray);
}

.quick-stat .stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-black);
}

.no-data {
  text-align: center;
  color: var(--dark-gray);
  font-style: italic;
  padding: 2rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-card-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
  }
  
  .order-item,
  .food-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .order-details {
    align-items: center;
  }
}
