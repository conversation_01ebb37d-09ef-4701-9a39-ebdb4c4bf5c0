.food-display{
    margin-top: 30px;
    width: 100%;
}
.food-display h2{
    font-size: max(2vw,24px);
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: left;
}
.food-display-list{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 20px;
    width: 100%;
    justify-items: center;
}

.food-display .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
    font-style: italic;
    font-size: 18px;
}

/* Tablet Responsive */
@media (max-width: 1024px) {
    .food-display-list {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 20px;
        justify-items: center;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .food-display {
        margin-top: 20px;
        padding: 0 4px;
    }

    .food-display h2 {
        font-size: 22px;
        margin-bottom: 16px;
        text-align: left;
    }

    .food-display-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 16px;
        justify-items: stretch;
    }

    .food-display .loading {
        padding: 30px;
        font-size: 16px;
    }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
    .food-display {
        margin-top: 15px;
        padding: 0 2px;
    }

    .food-display h2 {
        font-size: 20px;
        margin-bottom: 12px;
        text-align: left;
    }

    .food-display-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-top: 12px;
        justify-items: stretch;
    }

    .food-display .loading {
        padding: 20px;
        font-size: 14px;
    }
}

/* Extra Small Mobile Responsive */
@media (max-width: 360px) {
    .food-display {
        margin-top: 12px;
        padding: 0 1px;
    }

    .food-display h2 {
        font-size: 18px;
        margin-bottom: 10px;
        text-align: left;
    }

    .food-display-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;
        margin-top: 10px;
        justify-items: stretch;
    }

    .food-display .loading {
        padding: 16px;
        font-size: 12px;
    }
}