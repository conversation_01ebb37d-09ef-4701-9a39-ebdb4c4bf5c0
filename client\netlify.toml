[build]
  command = "npm ci && npm run build"
  publish = "dist"

# Redirect all routes to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Environment variables for production build
[build.environment]
  NODE_VERSION = "18"
  VITE_API_BASE_URL = "https://eatzone.onrender.com"
  VITE_APP_ENV = "production"
  NODE_ENV = "production"

# Simplified security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"