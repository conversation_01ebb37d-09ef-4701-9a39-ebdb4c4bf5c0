.navbar {
  background-color: var(--white);
  border-bottom: 1px solid var(--medium-gray);
  height: 80px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem;
  max-width: 100%;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  color: var(--accent-black);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--light-gray);
}

.navbar-brand h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-red);
  margin: 0;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  background-color: var(--light-gray);
}

.admin-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
}

.admin-name {
  font-weight: 500;
  color: var(--accent-black);
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 1rem;
  }
  
  .navbar-brand h1 {
    font-size: 1.25rem;
  }
  
  .admin-name {
    display: none;
  }
}
