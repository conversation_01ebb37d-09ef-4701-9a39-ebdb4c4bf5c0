.navbar {
  background-color: var(--white);
  border-bottom: 1px solid var(--medium-gray);
  height: 80px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem;
  max-width: 100%;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  color: var(--accent-black);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--light-gray);
}

.navbar-brand h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-red);
  margin: 0;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  background-color: var(--light-gray);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.admin-profile:hover {
  background-color: var(--medium-gray);
}

.admin-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
}

.admin-name {
  font-weight: 500;
  color: var(--accent-black);
}

.dropdown-icon {
  color: var(--dark-gray);
  transition: var(--transition);
}

.admin-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--medium-gray);
  min-width: 200px;
  z-index: 1000;
  margin-top: 0.5rem;
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--accent-black);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background-color: var(--light-gray);
}

.dropdown-item:first-child {
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.dropdown-item.logout {
  color: var(--danger);
  border-top: 1px solid var(--medium-gray);
}

.dropdown-item.logout:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--medium-gray);
  margin: 0.5rem 0;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 1rem;
  }
  
  .navbar-brand h1 {
    font-size: 1.25rem;
  }
  
  .admin-name {
    display: none;
  }
}
