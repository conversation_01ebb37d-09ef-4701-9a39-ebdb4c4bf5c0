.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.login-container {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-red);
  margin: 0;
}

.logo span {
  font-size: 0.875rem;
  color: var(--dark-gray);
  font-weight: 500;
}

.login-header h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 1.5rem 0 0.5rem 0;
}

.login-header p {
  color: var(--dark-gray);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--accent-black);
  font-size: 0.875rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--dark-gray);
  z-index: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--medium-gray);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: var(--transition);
  background-color: var(--white);
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.password-toggle:hover {
  color: var(--accent-black);
  background-color: var(--light-gray);
}

.login-btn {
  background: var(--primary-red);
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.login-btn:hover:not(:disabled) {
  background: var(--primary-red-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.demo-btn {
  background: transparent;
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
  padding: 0.75rem 2rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.demo-btn:hover:not(:disabled) {
  background: var(--primary-red);
  color: var(--white);
}

.demo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--medium-gray);
}

.login-footer p {
  color: var(--dark-gray);
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-login {
    padding: 1rem;
  }
  
  .login-container {
    padding: 2rem;
  }
  
  .logo h1 {
    font-size: 1.75rem;
  }
  
  .login-header h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1.5rem;
  }
  
  .input-wrapper input {
    padding: 0.875rem 0.875rem 0.875rem 2.75rem;
  }
  
  .login-btn {
    padding: 0.875rem 1.5rem;
  }
}
