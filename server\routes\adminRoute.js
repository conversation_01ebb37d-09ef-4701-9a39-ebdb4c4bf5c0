import express from "express";
// Temporarily commented out to test
// import { loginAdmin, registerAdmin, googleAuthAdmin, googleAuthCallbackAdmin } from "../controllers/adminController.js";
// import adminAuthMiddleware from "../middleware/adminAuth.js";
// import passport from "../config/passport.js";

const adminRouter = express.Router();

// Test route (can be removed in production)
adminRouter.get("/test", (req, res) => {
    res.json({ message: "Admin routes are working!" });
});

// Regular admin authentication routes
adminRouter.post("/register", (req, res) => {
    res.json({ success: false, message: "Admin registration temporarily disabled for testing" });
});

// Working admin login endpoint
adminRouter.post("/login", (req, res) => {
    try {
        const { email, password } = req.body;

        // Demo credentials for testing
        if (email === "<EMAIL>" && password === "admin123") {
            // Create a simple token (in production, use proper JWT)
            const token = "demo-admin-token-" + Date.now();

            res.json({
                success: true,
                message: "Login successful",
                token: token,
                admin: {
                    id: "demo-admin-id",
                    name: "Demo Admin",
                    email: email,
                    role: "admin"
                }
            });
        } else {
            res.status(401).json({
                success: false,
                message: "Invalid email or password"
            });
        }
    } catch (error) {
        console.error("Admin login error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error"
        });
    }
});

// Google OAuth routes for admin
adminRouter.get("/auth/google", (req, res) => {
    console.log("Admin Google OAuth route hit!");
    console.log("Environment variables loaded:", {
        clientId: process.env.GOOGLE_CLIENT_ID ? "✓" : "✗",
        clientSecret: process.env.GOOGLE_CLIENT_SECRET ? "✓" : "✗"
    });

    // Redirect to Google OAuth
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
        `redirect_uri=${encodeURIComponent('https://eatzone.onrender.com/api/admin/auth/google/callback')}&` +
        `response_type=code&` +
        `scope=${encodeURIComponent('profile email')}&` +
        `access_type=offline&` +
        `prompt=select_account`;

    console.log("Redirecting to Google:", googleAuthUrl);
    res.redirect(googleAuthUrl);
});
// Temporarily simplified callback
adminRouter.get("/auth/google/callback", (req, res) => {
    console.log("Google callback hit!");
    console.log("Query params:", req.query);
    res.json({ message: "Google callback received", query: req.query });
});

// Verify admin token endpoint
adminRouter.get("/verify", (req, res) => {
    try {
        const token = req.headers.authorization?.split(" ")[1];

        if (!token) {
            return res.status(401).json({
                success: false,
                message: "No token provided"
            });
        }

        // Simple token validation (in production, use proper JWT verification)
        if (token.startsWith("demo-admin-token-")) {
            res.json({
                success: true,
                admin: {
                    id: "demo-admin-id",
                    name: "Demo Admin",
                    email: "<EMAIL>",
                    role: "admin"
                }
            });
        } else {
            res.status(401).json({
                success: false,
                message: "Invalid token"
            });
        }
    } catch (error) {
        console.error("Token verification error:", error);
        res.status(401).json({
            success: false,
            message: "Invalid token"
        });
    }
});

// Protected admin routes (temporarily disabled)
adminRouter.get("/profile", (req, res) => {
    res.json({ success: false, message: "Admin profile temporarily disabled for testing" });
});

adminRouter.post("/logout", (req, res) => {
    res.json({ success: true, message: "Admin logout temporarily disabled for testing" });
});

export default adminRouter;
