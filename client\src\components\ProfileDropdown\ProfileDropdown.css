.profile-dropdown-container {
  position: relative;
  display: inline-block;
}

.profile-icon-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: transform 0.2s ease;
  position: relative;
}

.profile-icon-container:hover {
  transform: scale(1.05);
}

.profile-icon-container.active .profile-letter-avatar {
  box-shadow: 0 4px 12px rgba(255, 77, 0, 0.4);
  transform: translateY(-1px);
}

.dropdown-arrow {
  font-size: 10px;
  color: #666;
  transition: all 0.2s ease;
  margin-left: 2px;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
  color: #ff4d00;
}

.profile-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #ff4d00;
}

.profile-letter-avatar {
  width: 38px;
  height: 38px;
  background: linear-gradient(135deg, #ff4d00, #ff6b35);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  border-radius: 50%;
  border: 2px solid #ff4d00;
  box-shadow: 0 3px 8px rgba(255, 77, 0, 0.3);
  transition: all 0.2s ease;
}

.profile-letter-avatar:hover {
  box-shadow: 0 4px 12px rgba(255, 77, 0, 0.4);
  transform: translateY(-1px);
}

.dropdown-menu {
  position: absolute;
  top: 50px;
  right: 0;
  width: 200px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-header {
  padding: 16px 18px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa, #ffffff);
}

.user-name {
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 15px;
  line-height: 1.2;
}

.user-email {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  opacity: 0.8;
}

.dropdown-items {
  padding: 6px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 18px;
  text-decoration: none;
  color: #2c3e50;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  padding-left: 22px;
}

.dropdown-item img {
  width: 16px;
  height: 16px;
  margin-right: 12px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.dropdown-item:hover img {
  opacity: 1;
}

.logout {
  cursor: pointer;
  color: #e74c3c;
  border-top: 1px solid #f0f0f0;
  margin-top: 4px;
}

.logout:hover {
  background-color: #fdf2f2;
  color: #c0392b;
}

/* Responsive styles to match navbar */
@media (max-width:1050px){
  .profile-letter-avatar {
    width: 34px;
    height: 34px;
    font-size: 15px;
  }

  .dropdown-menu {
    width: 190px;
    top: 45px;
  }

  .dropdown-header {
    padding: 14px 16px;
  }

  .dropdown-item {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width:900px){
  .profile-letter-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .dropdown-menu {
    width: 180px;
    top: 42px;
  }

  .dropdown-header {
    padding: 12px 14px;
  }

  .user-name {
    font-size: 14px;
  }

  .user-email {
    font-size: 11px;
  }

  .dropdown-item {
    padding: 9px 14px;
    font-size: 12px;
  }

  .dropdown-item img {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }
}

@media (max-width:750px){
  .profile-letter-avatar {
    width: 30px;
    height: 30px;
    font-size: 13px;
    border-width: 1.5px;
  }

  .dropdown-arrow {
    font-size: 8px;
    margin-left: 1px;
  }

  .dropdown-menu {
    width: 170px;
    top: 40px;
    right: -10px;
  }

  .dropdown-header {
    padding: 10px 12px;
  }

  .user-name {
    font-size: 13px;
  }

  .user-email {
    font-size: 10px;
  }

  .dropdown-item {
    padding: 8px 12px;
    font-size: 11px;
  }

  .dropdown-item img {
    width: 12px;
    height: 12px;
    margin-right: 8px;
  }
}

@media (max-width: 480px) {
  .profile-letter-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
    border-width: 1px;
  }

  .dropdown-arrow {
    font-size: 7px;
    margin-left: 0px;
  }

  .dropdown-menu {
    width: 160px;
    top: 38px;
    right: -15px;
    border-radius: 10px;
  }

  .dropdown-header {
    padding: 8px 10px;
  }

  .user-name {
    font-size: 12px;
  }

  .user-email {
    font-size: 9px;
  }

  .dropdown-item {
    padding: 7px 10px;
    font-size: 10px;
  }

  .dropdown-item img {
    width: 11px;
    height: 11px;
    margin-right: 7px;
  }
}

@media (max-width: 360px) {
  .profile-letter-avatar {
    width: 26px;
    height: 26px;
    font-size: 11px;
  }

  .dropdown-arrow {
    font-size: 6px;
    margin-left: 0px;
  }

  .dropdown-menu {
    width: 150px;
    top: 36px;
    right: -20px;
  }

  .dropdown-header {
    padding: 6px 8px;
  }

  .user-name {
    font-size: 11px;
  }

  .user-email {
    font-size: 8px;
  }

  .dropdown-item {
    padding: 6px 8px;
    font-size: 9px;
  }

  .dropdown-item img {
    width: 10px;
    height: 10px;
    margin-right: 6px;
  }
}
